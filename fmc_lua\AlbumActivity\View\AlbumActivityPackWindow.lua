AlbumActivityCardInfo = {}
AlbumActivityCardInfo.__index = AlbumActivityCardInfo

function AlbumActivityCardInfo:Init(cardID, model, bRed, flyTrans, bPlayAnim, delayTime)
  self.m_cardID = cardID
  self.m_model = model
  self.m_bRed = bRed
  self.m_flyTrans = flyTrans
  self.m_curCardNum = self.m_model:GetCardCount(self.m_cardID)
  self.m_bNewCard = self.m_model:GetNewCardId() == self.m_cardID
  self.m_AlbumActivityCardLuaTable:Init(cardID, model, bRed, true, nil, nil, not self.m_bNewCard and bPlayAnim, delayTime)
  if self.m_bNewCard then
    self.m_model:SetNewCardId("")
    self.m_cardCanv.alpha = 0
    UIUtil.SetLocalScale(self.m_cardRectTrans, 1.2, 1.2)
    local seq = DOTween.Sequence()
    seq:AppendInterval(0.2)
    seq:Append(self.m_cardRectTrans:DOScale(Vector3(1, 1, 1), 0.2):SetEase(Ease.InSine))
    seq:Join(self.m_cardCanv:DOFade(1, 0.2))
    seq:AppendCallback(function()
      self.m_AlbumActivityCardLuaTable:UpdateNewState(self.m_bRed, true)
      self.m_newSeq = nil
    end)
    self.m_newSeq = seq
  end
end

function AlbumActivityCardInfo:OnButtonClicked()
  self.m_AlbumActivityCardLuaTable:UpdateNewState(false)
  GM.UIManager:SetEventLock(true, self)
  local seq = DOTween.Sequence()
  self.m_AlbumActivityCardLuaTable.transform.parent = self.m_flyTrans
  self.m_OriginPos = self.m_AlbumActivityCardLuaTable.transform.position
  self.m_OriginScale = self.m_AlbumActivityCardLuaTable.transform.localScale
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
  GM.UIManager:OpenView(UIPrefabConfigName.AlbumActivityShowCardWindow, self.m_model:GetType(), self.m_AlbumActivityCardLuaTable.transform, self.m_bRed, function()
    if self.gameObject:IsNull() then
      return
    end
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
    self:OnFlyOriginPos()
  end, self.m_curCardNum == 0)
  seq:Append(self.m_AlbumActivityCardLuaTable.transform:DOLocalMove(Vector3.zero, 0.4))
  seq:Join(self.m_AlbumActivityCardLuaTable.transform:DOScale(1, 0.4))
  seq:AppendCallback(function()
    GM.UIManager:SetEventLock(false, self)
  end)
  self.m_bRed = false
end

function AlbumActivityCardInfo:OnFlyOriginPos()
  GM.UIManager:SetEventLock(true, self)
  local seq = DOTween.Sequence()
  local cardCanvas = self.m_AlbumActivityCardLuaTable.gameObject:GetComponent(typeof(CS.UnityEngine.Canvas))
  if cardCanvas ~= nil then
    cardCanvas.overrideSorting = false
  end
  seq:Append(self.m_AlbumActivityCardLuaTable.transform:DOMove(self.m_OriginPos, 0.4))
  seq:Join(self.m_AlbumActivityCardLuaTable.transform:DOScale(self.m_OriginScale, 0.4))
  seq:AppendCallback(function()
    GM.UIManager:SetEventLock(false, self)
    self.m_AlbumActivityCardLuaTable.transform.parent = self.m_cardRectTrans
  end)
end

function AlbumActivityCardInfo:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
  GM.UIManager:RemoveAllEventLocks(self)
  if self.m_newSeq ~= nil then
    self.m_newSeq:Kill()
    self.m_newSeq = nil
  end
end

function AlbumActivityCardInfo:GetCardCanv()
  return self.m_cardCanv
end

function AlbumActivityCardInfo:GetMiddleRect()
  return self.m_cardRectTrans
end

AlbumActivityCardGroup = {}
AlbumActivityCardGroup.__index = AlbumActivityCardGroup

function AlbumActivityCardGroup:Init(model, mainwindow)
  self.m_model = model
  self.m_cards = {}
  self.m_cards[1] = self.m_cardLuaTable
  self.m_progressLuaTable:Init(self.m_model)
  self.m_mainWindow = mainwindow
end

function AlbumActivityCardGroup:UpdateContent(CardGroupConfig, bPlayAnim, delayTime)
  self.m_cardGroupConfig = CardGroupConfig
  self.m_redInfo = self.m_model:GetRedPointInfoBySetID(self.m_cardGroupConfig.setId)
  local InitFunc = function()
    UIUtil.SetActive(self.gameObject, true)
    self.m_progressLuaTable:UpdateInfo(self.m_cardGroupConfig.setId)
    local albumID = self.m_model:GetAlbumConfig()
    self.m_titleText.text = GM.GameTextModel:GetText(self.m_cardGroupConfig.setId .. "_name")
    local preferWidth = math.min(self.m_titleText.preferredWidth, self.m_titleText.transform.sizeDelta.x)
    local midWidth = preferWidth / 2
    local cardNames = self.m_cardGroupConfig.cardGroup
    for _, cardLua in pairs(self.m_cards) do
      UIUtil.SetActive(cardLua.gameObject, false)
    end
    for index, cardID in ipairs(cardNames) do
      if self.m_cards[index] == nil then
        self.m_cards[index] = Object.Instantiate(self.m_cardLuaTable.gameObject, self.m_cardLuaTable.transform.parent):GetLuaTable()
      end
      UIUtil.SetActive(self.m_cards[index].gameObject, true)
    end
    for index, cardID in ipairs(cardNames) do
      self.m_cards[index]:Init(cardID, self.m_model, self.m_redInfo[cardID] == true, self.m_mainWindow:GetFlyTrans(), bPlayAnim, delayTime)
    end
    GM.UIManager:SetEventLock(false, self)
  end
  GM.UIManager:SetEventLock(true, self)
  InitFunc()
end

function AlbumActivityCardGroup:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
end

AlbumActivityPackProgress = {}
AlbumActivityPackProgress.__index = AlbumActivityPackProgress

function AlbumActivityPackProgress:Init(model)
  self.m_model = model
end

function AlbumActivityPackProgress:UpdateInfo(cardGroupID)
  local albumID = self.m_model:GetAlbumConfig()
  SpriteUtil.SetImage(self.m_cardSetIconImg, ImageFileConfigName[cardGroupID], false)
  local curSetCard, maxSetCard = self.m_model:GetSetCollectProgress(cardGroupID)
  UIUtil.SetActive(self.m_NoFinishGo, curSetCard < maxSetCard)
  UIUtil.SetActive(self.m_FinishGo, maxSetCard <= curSetCard)
  local curShowID, curMaxId = self.m_model:GetSetCollectProgress(cardGroupID)
  self.m_progressText.text = tostring(curShowID) .. "/" .. tostring(curMaxId)
  self.m_progressSlider.value = curShowID / curMaxId
  local cardGroupRewards = self.m_model:GetCardSetRewards(cardGroupID)
  self.m_rewardContent:Init(cardGroupRewards)
end

AlbumActivityPackWindow = setmetatable({}, AlbumActivityBaseWindow)
AlbumActivityPackWindow.__index = AlbumActivityPackWindow

function AlbumActivityPackWindow:Init(nIndex, ListCardGroup, activityType, cardId)
  AlbumActivityBaseWindow.Init(self, activityType, cardId == nil)
  self.m_nIndex = nIndex
  self.m_listCardGroups = ListCardGroup
  self.m_originCardGroup:Init(self.m_model, self)
  local canvasSize = GM.UIManager:GetCanvasSize()
  self.m_originCardGroup.transform.anchoredPosition = Vector2(canvasSize.x, 0)
  self.m_curcardGroup:Init(self.m_model, self)
  self.m_curcardGroup:UpdateContent(self.m_listCardGroups[self.m_nIndex], true)
  self:UpdatePage(self.m_nIndex)
  GM.UIManager:SetEventLock(true, self)
  DelayExecuteFuncInView(function()
    GM.UIManager:SetEventLock(false, self)
  end, 0.2, self)
  self:UpdateContent()
  GM.UIManager:SetEventLock(true, self)
  DelayExecuteFuncInView(function()
    GM.UIManager:SetEventLock(false, self)
  end, 0.5, self)
  self.m_FlyTransCanv.sortingOrder = self:GetSortingOrder() + 1
  
  function self.m_eventTrigger.OnLuaPointerDown(eventData)
    self:_OnPointerDown(eventData)
  end
  
  function self.m_eventTrigger.OnLuaPointerUp(eventData)
    self:_OnPointerUp(eventData)
  end
  
  function self.m_eventTrigger.OnLuaDrag(eventData)
    self:_OnDrag(eventData)
  end
  
  if cardId and self.m_model:GetCardCount(cardId) == 1 then
    local cardInfo = self.m_model:GetCardInfo(cardId)
    local curPro, maxPro = self.m_model:GetSetCollectProgress(cardInfo.setId)
    if curPro == maxPro then
      do
        local cur, max = self.m_model:GetAlbumCollectProgress()
        local bAlbumFinish = max <= cur
        GM.UIManager:SetEventLock(true, self)
        DelayExecuteFuncInView(function()
          GM.UIManager:SetEventLock(false, self)
          GM.UIManager:OpenView(self.m_activityDefinition.TakeSetRewardPrefabName, self.m_model:GetType(), {
            cardInfo.setId
          }, bAlbumFinish)
        end, 1, self)
      end
    end
  end
  self.m_newCardId = cardId
end

function AlbumActivityPackWindow:_OnPointerDown(eventData)
  self.m_startPos = eventData.position
  self.m_curPos = eventData.position
  if self.m_MoveOffset == nil then
    self.m_MoveOffset = 0
  end
end

function AlbumActivityPackWindow:_OnDrag(eventData)
  if self.m_startPos == nil then
    return
  end
  local moveOffset = eventData.position.x - self.m_curPos.x
  self.m_curPos = eventData.position
  self:MoveGroup(moveOffset)
end

function AlbumActivityPackWindow:MoveGroup(offset)
  if self.m_MoveOffset == nil then
    self.m_MoveOffset = 0
  end
  local CanvasSzie = GM.UIManager:GetCanvasSize()
  local curOffset = self.m_MoveOffset + offset
  if math.abs(curOffset) > CanvasSzie.x then
    return
  end
  if self.m_bMove == true then
    if 0 < curOffset then
      if 0 >= self.m_nIndex - 1 then
        return
      end
    elseif self.m_nIndex + 1 > #self.m_listCardGroups then
      return
    end
  end
  if self.m_MoveOffset * curOffset <= 0 then
    local index = 0
    if 0 < offset then
      if 0 >= self.m_nIndex - 1 then
        self.m_MoveOffset = nil
        return
      end
      index = self.m_nIndex - 1
    else
      if self.m_nIndex + 1 > #self.m_listCardGroups then
        self.m_MoveOffset = nil
        return
      end
      index = self.m_nIndex + 1
    end
    self.m_originCardGroup:UpdateContent(self.m_listCardGroups[index], false)
    self.m_originCardGroup.transform.anchoredPosition = Vector2((0 < offset and -CanvasSzie.x or CanvasSzie.x) + self.m_MoveOffset, 0)
  end
  self.m_MoveOffset = curOffset
  if self.m_bMove ~= true then
    UIUtil.SetActive(self.m_rightGo, false)
    UIUtil.SetActive(self.m_leftGo, false)
  end
  self.m_bMove = true
  local orgingPos = self.m_originCardGroup.transform.anchoredPosition
  local curGroupPos = self.m_curcardGroup.transform.anchoredPosition
  self.m_originCardGroup.transform.anchoredPosition = Vector2(orgingPos.x + offset, 0)
  self.m_curcardGroup.transform.anchoredPosition = Vector2(curGroupPos.x + offset, 0)
end

function AlbumActivityPackWindow:_OnPointerUp(eventData)
  if self.m_startPos == nil or self.m_MoveOffset == nil then
    return
  end
  local CanvasSzie = GM.UIManager:GetCanvasSize()
  self.m_curPos = eventData.position
  if math.abs(self.m_MoveOffset) > 100 then
    local delay = (1 - math.abs(self.m_MoveOffset) / CanvasSzie.x) * 0.7
    if self.m_MoveOffset > 0 then
      self:OnLeftButton(true, delay)
    else
      self:OnRightButton(true, delay)
    end
  elseif math.abs(self.m_MoveOffset) < 10 then
    eventData:PassClickEvent(self.m_eventTrigger.gameObject)
  else
    GM.UIManager:SetEventLock(true, self)
    local CanvasSzie = GM.UIManager:GetCanvasSize()
    local seq = DOTween.Sequence()
    seq:Append(self.m_curcardGroup.transform:DOAnchorPos(Vector2.zero, 0.3):SetEase(Ease.InQuad))
    seq:Join(self.m_originCardGroup.transform:DOAnchorPos(Vector2(self.m_MoveOffset > 0 and -CanvasSzie.x or CanvasSzie.x, 0), 0.3):SetEase(Ease.InQuad))
    seq:AppendCallback(function()
      GM.UIManager:SetEventLock(false, self)
    end)
  end
  self.m_bMove = false
  self.m_startPos = nil
  self.m_MoveOffset = nil
  self.m_curPos = nil
  self:UpdateDirectBtn()
end

function AlbumActivityPackWindow:GetFlyTrans()
  return self.m_FlyTransCanv.transform
end

function AlbumActivityPackWindow:UpdateContent()
  self.m_model:HideRedPoint(self.m_listCardGroups[self.m_nIndex].setId)
  self:UpdateDirectBtn()
end

function AlbumActivityPackWindow:OnLeftButton(bNoSetNewPos, fDelayTime)
  local index = self.m_nIndex - 1 <= 0 and #self.m_listCardGroups or self.m_nIndex - 1
  self:MoveNexGroup(index, 1, bNoSetNewPos, fDelayTime)
end

function AlbumActivityPackWindow:OnRightButton(bNoSetNewPos, fDelayTime)
  local index = self.m_nIndex + 1 > #self.m_listCardGroups and 1 or self.m_nIndex + 1
  self:MoveNexGroup(index, 2, bNoSetNewPos, fDelayTime)
end

function AlbumActivityPackWindow:UpdateDirectBtn()
  UIUtil.SetActive(self.m_leftGo, true)
  UIUtil.SetActive(self.m_rightGo, true)
  if self.m_nIndex == 1 then
    UIUtil.SetActive(self.m_leftGo, false)
  elseif self.m_nIndex == #self.m_listCardGroups then
    UIUtil.SetActive(self.m_rightGo, false)
  end
end

function AlbumActivityPackWindow:MoveNexGroup(index, direct, bNoSetNewPos, delayTime)
  if not bNoSetNewPos then
    self.m_originCardGroup:UpdateContent(self.m_listCardGroups[index])
  end
  local midTemp = self.m_curcardGroup
  self.m_curcardGroup = self.m_originCardGroup
  self.m_originCardGroup = midTemp
  self:UpdatePage(index)
  self.m_nIndex = index
  local fDelayTime = delayTime or 0.7
  self.m_curcardGroup:UpdateContent(self.m_listCardGroups[self.m_nIndex], true, fDelayTime)
  self:UpdateContent()
  local canvasSize = GM.UIManager:GetCanvasSize()
  if not bNoSetNewPos then
    self.m_curcardGroup.transform.anchoredPosition = Vector2(direct == 1 and -canvasSize.x or canvasSize.x, 0)
  end
  GM.UIManager:SetEventLock(true, self)
  local seq = DOTween.Sequence()
  seq:Append(self.m_curcardGroup.transform:DOAnchorPos(Vector2.zero, fDelayTime))
  seq:Join(self.m_originCardGroup.transform:DOAnchorPos(Vector2(direct == 1 and canvasSize.x or -canvasSize.x, 0), fDelayTime))
  seq:AppendCallback(function()
    GM.UIManager:SetEventLock(false, self)
  end)
end

function AlbumActivityPackWindow:OnDestroy()
  GM.UIManager:RemoveAllEventLocks(self)
  AlbumActivityBaseWindow.OnDestroy(self)
end

function AlbumActivityPackWindow:UpdatePage(index)
  self.m_pageNumText.text = index .. "/" .. #(self.m_listCardGroups or {})
end

function AlbumActivityPackWindow:OnCloseFinish()
  Debug.Log("AlbumActivityPackWindow:OnCloseFinish")
  AlbumActivityBaseWindow.OnCloseFinish(self)
  if self.m_newCardId ~= nil and self.m_model:GetJokerCount() > 0 and not GM.UIManager:IsViewExisting(UIPrefabConfigName.AlbumRemindUseJokerWindow) then
    GM.UIManager:OpenView(UIPrefabConfigName.AlbumRemindUseJokerWindow, self.m_activityType)
  end
end

function AlbumActivityPackWindow:Close()
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
  AlbumActivityBaseWindow.Close(self)
end
