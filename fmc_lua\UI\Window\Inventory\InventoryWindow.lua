ProducerInventoryRewardPrefix = "pdInventory_"
EInventoryCellCode = {
  Empty = 1,
  NewSlot = 2,
  Locked = 3,
  Item = 4,
  ProducerEmpty = 5,
  ProducerLocked = 6
}
EInventoryTabLabel = {Normal = "Normal", Producer = "Producer"}
InventoryWindow = setmetatable({canClickWindowMask = true, itemViewSortingOrderNormal = 11}, BaseWindow)
InventoryWindow.__index = InventoryWindow

function InventoryWindow:Init(bPlayTutorialAnim)
  self.m_bPlayTutorialAnim = bPlayTutorialAnim
  GM.MainBoardModel:SortInventory()
  self.m_codeStateMap = GM.MainBoardModel:GetOrderCodeStateMap()
  self:_InitNormalContent()
  self:_InitProducerContent()
  GM.MainBoardModel:ClearNewStoredItems()
  self:_InitTabButtons()
  self:_ToggleHudHighlight(true)
  if self.m_bPlayTutorialAnim then
    DelayExecuteFuncInView(function()
      self:PlayProducerTutorialAnim()
    end, 0.5, self, true)
  end
end

function InventoryWindow:OnCloseView(...)
  BaseWindow.OnCloseView(self)
  self:_ToggleHudHighlight(false)
end

function InventoryWindow:AddEventListener()
  EventDispatcher.AddListener(EEventType.InventoryNewSlot, self, self._OnNewSlotOpen)
  EventDispatcher.AddListener(EEventType.OrderStateChanged, self, self.UpdateOrderCheck)
end

function InventoryWindow:RemoveEventListener()
  EventDispatcher.RemoveTarget(self)
end

function InventoryWindow:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  if self.m_seq ~= nil then
    self.m_seq:Kill()
    self.m_seq = nil
  end
  if self.m_bEventLocked then
    GM.UIManager:SetEventLock(false)
    self.m_bEventLocked = false
  end
end

function InventoryWindow:_InitTabButtons()
  self.m_mapLabel2TabBtn = {}
  local tbTabBtn
  for _, label in pairs(EInventoryTabLabel) do
    tbTabBtn = self["m_tabButton" .. label]
    tbTabBtn:Init(self)
    self.m_mapLabel2TabBtn[label] = tbTabBtn
  end
  self:OnTabButtonClicked(EInventoryTabLabel.Normal)
end

function InventoryWindow:OnTabButtonClicked(tabLabel)
  if tabLabel == EInventoryTabLabel.Producer and not self.m_bLoadProducerInv then
    self:_LoadProducerContent()
  end
  local bSelect
  for label, tabBtn in pairs(self.m_mapLabel2TabBtn) do
    bSelect = tabLabel == label
    tabBtn:SetHighlightActive(bSelect)
    UIUtil.SetActive(self["m_node" .. label], bSelect)
  end
  EventDispatcher.DispatchEvent(EEventType.InventoryChangeTab)
end

function InventoryWindow:_InitNormalContent()
  local sortingOrder = self:GetSortingOrder()
  InventoryWindow.itemViewSortingOrderNormal = sortingOrder + 1
  self.m_spriteMask.frontSortingOrder = sortingOrder + 2
  self.m_spriteMask.backSortingOrder = sortingOrder
  self:_UpdateNormalContent()
  local _, maxCount = self:GetStoreSlotCount()
  local maxRow = math.ceil(maxCount / INVENTORY_COLUMN_COUNT)
  self.m_loopListView:InitListView(maxRow + 1, function(listView, rowIndex)
    return self:_OnListItemByIndex(listView, rowIndex)
  end)
  self.m_loopListView:SetItemSize(0, 20, 0)
  for i = 1, maxRow do
    self.m_loopListView:SetItemSize(i, 200, i == 1 and 20 or 0)
  end
  self.m_loopListView:UpdateContentSize()
end

function InventoryWindow:_OnListItemByIndex(listView, rowIndex)
  if rowIndex == 0 then
    return listView:NewListViewItem("InventoryRowTransparent")
  end
  if Table.IsEmpty(self.m_cellMatrix[rowIndex]) then
    return
  end
  local rowItem = listView:NewListViewItem("InventoryRow")
  local rowTb = rowItem.gameObject:GetLuaTable()
  local arrCellInfo = self.m_cellMatrix[rowIndex]
  for column, cellInfo in ipairs(arrCellInfo) do
    if not rowTb:HasInited() then
      rowTb:Init(self)
    end
    rowTb:SetCell(column, cellInfo.cellCode, cellInfo.param)
    if cellInfo.cellCode == EInventoryCellCode.Item and cellInfo.param.bNewAdded then
      cellInfo.param.bNewAdded = false
    end
  end
  for column = #arrCellInfo + 1, INVENTORY_COLUMN_COUNT do
    rowTb:SetCell(column, EInventoryCellCode.Empty)
  end
  rowTb:UpdateOrderCheck(self.m_codeStateMap)
  return rowItem
end

function InventoryWindow:UpdateOrderCheck()
  self.m_codeStateMap = GM.MainBoardModel:GetOrderCodeStateMap()
  local visibleItemList = self.m_loopListView.ItemList
  local rowTb
  for i = 0, visibleItemList.Count - 1 do
    rowTb = visibleItemList[i].gameObject:GetLuaTable()
    if rowTb ~= nil then
      rowTb:UpdateOrderCheck(self.m_codeStateMap)
    end
  end
end

function InventoryWindow:_UpdateNormalContent()
  local mapNewAddedItems = GM.MainBoardModel:GetNewStoredItems()
  local slotCount, maxCount = self:GetStoreSlotCount()
  self.m_cellMatrix = {}
  local canBought = self:_SetBuyCells()
  self:_ResetCells(mapNewAddedItems)
  if canBought then
    slotCount = slotCount + 1
  end
  for i = slotCount + 1, maxCount do
    self:_SetCell(i, EInventoryCellCode.Locked)
  end
end

function InventoryWindow:GetStoreSlotCount()
  local maxCount = INVENTORY_MAX_COUNT
  local slotCount, producerNum = GM.MainBoardModel:GetStoreSlotCount()
  slotCount = slotCount - producerNum
  if IsAutoRun() and GM.TestAutoRunModel.useInventory then
    slotCount = math.max(slotCount, maxCount)
    maxCount = slotCount
  end
  return slotCount, maxCount
end

function InventoryWindow:_GetPositionFromIndex(index)
  local row = (index - 1) // INVENTORY_COLUMN_COUNT + 1
  local column = (index - 1) % INVENTORY_COLUMN_COUNT + 1
  return row, column
end

function InventoryWindow:_GetCell(index)
  local row, column = self:_GetPositionFromIndex(index)
  local rowItem = self.m_loopListView:GetShownItemByItemIndex(row)
  if rowItem ~= nil then
    return rowItem.gameObject:GetLuaTable():GetCell(column)
  end
end

function InventoryWindow:_SetCell(index, cellCode, param)
  local row, column = self:_GetPositionFromIndex(index)
  if self.m_cellMatrix[row] == nil then
    self.m_cellMatrix[row] = {}
  end
  self.m_cellMatrix[row][column] = {cellCode = cellCode, param = param}
end

function InventoryWindow:_ResetCells(mapNewAddedItems)
  local slotCount, producerNum = GM.MainBoardModel:GetStoreSlotCount()
  local index = 1
  for i = 1, slotCount do
    if not (not (index > slotCount - producerNum) or IsAutoRun() and GM.TestAutoRunModel.useInventory) then
      break
    end
    local item = GM.MainBoardModel:GetStoredItem(i)
    if item ~= nil then
      if not GM.MainBoardModel:CanStoreItemInProducer(item:GetType()) or self.m_bPlayTutorialAnim then
        self:_SetCell(index, EInventoryCellCode.Item, {
          index = index,
          itemModel = item,
          bNewAdded = mapNewAddedItems and mapNewAddedItems[item]
        })
        index = index + 1
      end
    else
      self:_SetCell(index, EInventoryCellCode.Empty)
      index = index + 1
    end
  end
end

function InventoryWindow:_SetBuyCells()
  local slotConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.InventorySlot)
  local boughtCount = GM.MiscModel:GetInventoryBoughtCapInNumber()
  local inventoryCap = self:GetStoreSlotCount()
  local canBought = false
  if boughtCount < #slotConfig.UnlockCost and inventoryCap < INVENTORY_MAX_COUNT then
    canBought = true
    self:_SetCell(inventoryCap + 1, EInventoryCellCode.NewSlot)
  end
  return canBought
end

function InventoryWindow:_OnNewSlotOpen()
  local slotCount = self:GetStoreSlotCount()
  local cell = self:_GetCell(slotCount)
  if not cell or not cell.OnOpened then
    Log.Error("should not happen")
    return
  end
  DelayExecuteFuncInView(function()
    self:_SetCell(slotCount, EInventoryCellCode.Empty)
    EventDispatcher.DispatchEvent(EEventType.OnInventoryCapacityUpdate)
    if slotCount < INVENTORY_MAX_COUNT then
      self:_SetBuyCells()
    end
    self.m_loopListView:RefreshAllShownItem()
  end, cell:OnOpened(), self, true)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxButtonClick)
end

function InventoryWindow:TryRetrieveItem(cell)
  if GM.MainBoardModel:RetrieveStoredItemByItemModel(cell.itemModel) then
    GM.AudioModel:StopCookEffect()
    if GM.MainBoardModel:CanStoreItemInProducer(cell.itemModel:GetCode()) then
      self:_ResetProducerCells()
      self.m_producerLoopListView:RefreshAllShownItem()
    else
      DelayExecuteFuncInView(function()
        self:_ResetCells()
        self.m_loopListView:RefreshAllShownItem()
      end, cell:OnRetrieved(), self, true)
    end
    return true
  else
    GM.UIManager:ShowPromptWithKey("hint_board_full")
  end
end

function InventoryWindow:_ToggleHudHighlight(on)
  EventDispatcher.DispatchEvent(EEventType.HighlightHud, {
    highlight = on,
    hudKey = ESceneViewHudButtonKey.Inventory,
    enableBtn = true
  })
end

function InventoryWindow:ScrollToTargetRow(rowIndex)
  self.m_loopListView:MovePanelToItemIndex(rowIndex)
  return 0.5
end

function InventoryWindow:_InitProducerContent()
  if not GM.MainBoardModel:IsProducerInventoryOpen() then
    self.m_producerButton.enabled = false
    self.m_producerButtonImg.enabled = false
    return
  end
  local arrProducerConfig = GM.MainBoardModel:GetProducerInventoryConfig()
  local mapNewAddedItems = GM.MainBoardModel:GetNewStoredItems()
  local sortingOrder = self:GetSortingOrder()
  self.m_producerSpriteMask.frontSortingOrder = sortingOrder + 2
  self.m_producerSpriteMask.backSortingOrder = sortingOrder
  self.m_producerCellMatrix = {}
  local rootRect = self.m_producerScrollRect.content
  local maxCount = #arrProducerConfig
  local rowCount = math.max(math.ceil(maxCount / INVENTORY_COLUMN_COUNT), 5)
  self:_ResetProducerCells(mapNewAddedItems)
  for i = maxCount + 1, rowCount * INVENTORY_COLUMN_COUNT do
    self:_SetProducerCell(i, EInventoryCellCode.Locked)
  end
  self.m_producerRowCount = rowCount
end

function InventoryWindow:_LoadProducerContent()
  if not GM.MainBoardModel:IsProducerInventoryOpen() then
    return
  end
  self.m_bLoadProducerInv = true
  local rowCount = self.m_producerRowCount
  self.m_producerLoopListView:InitListView(rowCount + 1, function(listView, rowIndex)
    return self:_OnListProducerItemByIndex(listView, rowIndex)
  end)
  self.m_producerLoopListView:SetItemSize(0, 20, 0)
  for i = 1, rowCount do
    self.m_producerLoopListView:SetItemSize(i, 200, i == 1 and 20 or 0)
  end
  self.m_producerLoopListView:UpdateContentSize()
end

function InventoryWindow:_OnListProducerItemByIndex(listView, rowIndex)
  if rowIndex == 0 then
    return listView:NewListViewItem("InventoryRowTransparent")
  end
  if Table.IsEmpty(self.m_producerCellMatrix[rowIndex]) then
    return
  end
  local rowItem = listView:NewListViewItem("InventoryRow")
  local rowTb = rowItem.gameObject:GetLuaTable()
  for column, cellInfo in ipairs(self.m_producerCellMatrix[rowIndex]) do
    if not rowTb:HasInited() then
      rowTb:Init(self)
    end
    rowTb:SetCell(column, cellInfo.cellCode, cellInfo.param)
    if cellInfo.cellCode == EInventoryCellCode.Item and cellInfo.param.bNewAdded then
      cellInfo.param.bNewAdded = false
    end
  end
  return rowItem
end

function InventoryWindow:_ResetProducerCells(mapNewAddedItems)
  local arrProducerConfig = GM.MainBoardModel:GetProducerInventoryConfig()
  local lockedRow, item
  for index, producerConfig in ipairs(arrProducerConfig) do
    item = GM.MainBoardModel:GetFirstStoredItemByType(producerConfig.type)
    if not GM.MainBoardModel:HasUnlockedPDSlot(producerConfig.day) then
      if lockedRow == nil then
        lockedRow = self:_GetPositionFromIndex(index)
      end
      if lockedRow ~= self:_GetPositionFromIndex(index) then
        self:_SetProducerCell(index, EInventoryCellCode.Locked)
      else
        self:_SetProducerCell(index, EInventoryCellCode.ProducerLocked, producerConfig.day)
      end
    elseif item == nil then
      self:_SetProducerCell(index, EInventoryCellCode.ProducerEmpty, producerConfig.type)
    else
      self:_SetProducerCell(index, EInventoryCellCode.Item, {
        index = index,
        itemModel = item,
        bNewAdded = mapNewAddedItems and mapNewAddedItems[item]
      })
    end
  end
end

function InventoryWindow:_SetProducerCell(index, cellCode, param)
  local row, column = self:_GetPositionFromIndex(index)
  if self.m_producerCellMatrix[row] == nil then
    self.m_producerCellMatrix[row] = {}
  end
  self.m_producerCellMatrix[row][column] = {cellCode = cellCode, param = param}
end

function InventoryWindow:GetProducerTabButtonTrans()
  return self.m_producerButton.transform
end

function InventoryWindow:PlayProducerTutorialAnim()
  local arrRewards = {}
  local arrCellPos = {}
  local firstRowIndex
  for row, rowTb in ipairs(self.m_cellMatrix or {}) do
    for column, info in ipairs(rowTb) do
      if info.cellCode == EInventoryCellCode.Item and info.param.itemModel ~= nil and GM.MainBoardModel:CanStoreItemInProducer(info.param.itemModel:GetType()) then
        if firstRowIndex == nil then
          firstRowIndex = row
        end
        table.insert(arrRewards, {
          [PROPERTY_TYPE] = info.param.itemModel:GetType(),
          [PROPERTY_COUNT] = 1
        })
        table.insert(arrCellPos, {row = row, index = column})
      end
    end
  end
  if firstRowIndex == nil then
    return
  end
  local scrollTime = self:ScrollToTargetRow(firstRowIndex)
  self.m_bEventLocked = true
  GM.UIManager:SetEventLock(true)
  local seq = DOTween.Sequence()
  seq:AppendInterval(scrollTime + 0.2)
  local flyFunc = function()
    local rowCell, itemCell
    for index, reward in ipairs(arrRewards) do
      local customData = {
        {
          endPos = self.m_producerButton.transform.position,
          flyCount = 1,
          spriteKey = GM.ItemDataModel:GetSpriteName(reward[PROPERTY_TYPE]),
          targetButton = self.m_producerIconArea,
          spriteScale = 1
        }
      }
      rowCell = self.m_loopListView:GetShownItemByItemIndex(arrCellPos[index].row)
      if rowCell ~= nil then
        itemCell = rowCell.gameObject:GetLuaTable():GetCell(arrCellPos[index].index)
        if itemCell ~= nil then
          GM.PropertyDataManager:PlayCollectAnimation({reward}, itemCell:GetItemTrans().position, customData)
          itemCell:HideAll()
        end
      end
    end
  end
  seq:AppendCallback(function()
    flyFunc()
  end)
  seq:AppendInterval(1)
  seq:AppendCallback(function()
    self.m_bPlayTutorialAnim = false
    self:_UpdateNormalContent()
    self.m_loopListView:RefreshAllShownItem()
    self.m_seq = nil
    GM.UIManager:SetEventLock(false)
    self.m_bEventLocked = false
  end)
  self.m_seq = seq
end

function InventoryWindow:GetSkipPropBtn()
  return self.m_skipPropBtn
end
