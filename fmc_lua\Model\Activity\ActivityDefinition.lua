ActivityType = {
  BakeOut = "bakeOut",
  CoinRace = "coinRace",
  TreasureDig = "treasureDig",
  PkRace = "pkRace",
  ExtraBoard1 = "extraBoard1",
  ExtraBoard2 = "extraBoard2",
  ExtraBoard3 = "extraBoard3",
  ExtraBoard4 = "extraBoard4",
  ExtraBoard5 = "extraBoard5",
  ExtraBoard6 = "extraBoard6",
  ExtraBoard7 = "extraBoard7",
  ProgressActivity1 = "progress1_new",
  SurpriseChest = "surpriseChest",
  BlindChest1 = "blindChest1",
  BP1 = "bp1",
  BP2 = "bp2",
  BP3 = "bp3",
  BP4 = "bp4",
  BP5 = "bp5",
  BP6 = "bp6",
  BP7 = "bp7",
  Album1 = "album1"
}
ActivityTypeSequence = {
  ActivityType.Album1,
  ActivityType.ExtraBoard1,
  ActivityType.ExtraBoard2,
  ActivityType.ExtraBoard3,
  ActivityType.ExtraBoard4,
  ActivityType.ExtraBoard5,
  ActivityType.ExtraBoard6,
  ActivityType.ExtraBoard7,
  ActivityType.BP1,
  ActivityType.BP2,
  ActivityType.BP3,
  ActivityType.BP4,
  ActivityType.BP5,
  ActivityType.BP6,
  ActivityType.BP7,
  ActivityType.CoinRace,
  ActivityType.PkRace,
  ActivityType.ProgressActivity1,
  ActivityType.TreasureDig,
  ActivityType.BlindChest1,
  ActivityType.BakeOut
}
ActivityState = {
  Preparing = 1,
  Started = 2,
  Ended = 3,
  Released = 4
}
ActivityModelType = {
  Other = 0,
  Pass = 1,
  ExtraBoard = 2
}
