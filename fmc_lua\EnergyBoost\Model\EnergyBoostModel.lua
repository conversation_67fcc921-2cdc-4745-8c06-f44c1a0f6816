EnergyBoostModel = {}
EnergyBoostModel.__index = EnergyBoostModel
local TRIGGER_DURATION_IN_MINS = 30
local TRIGGER_DURATION_IN_SECONDS = TRIGGER_DURATION_IN_MINS * 60

function EnergyBoostModel:Init()
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self.OnLoginFinished)
  EventDispatcher.AddListener(EEventType.OnAddEnergy, self, self.OnAddEnergy)
  EventDispatcher.AddListener(EEventType.BuyEnergySuccess, self, self.OnBuyEnergySuccess)
end

function EnergyBoostModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function EnergyBoostModel:OnLoginFinished()
  local on = GM.ConfigModel:IsEnergyBoostConfigOn()
  if on ~= self.m_bBoostOn then
    self.m_bBoostOn = on
    EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
  end
  local bTriggerOn, triggerCount = GM.ConfigModel:IsEnergyBoostTriggerConfigOn()
  bTriggerOn = bTriggerOn and not self.m_bBoostOn
  if bTriggerOn ~= self.m_bBoostTriggerOn then
    self.m_bBoostTriggerOn = bTriggerOn
  end
  self.m_triggerCount = triggerCount or math.maxinteger
  self:UpdatePerSecond()
end

function EnergyBoostModel:LateInit()
  self.m_mapBoostBonusConfig = {}
  self.m_maxBoostBonusPrice = -1
  local arrConfig = GM.ConfigModel:GetGeneralConfByType(EGeneralConfType.EnergyBoostTriggerBonus, EConfigParamType.IntArray) or {}
  if #arrConfig % 2 ~= 0 then
    Log.Error("买体力送双倍配置格式错误")
    return
  end
  local price = -1
  for i = 2, #arrConfig, 2 do
    price = arrConfig[i - 1]
    self.m_mapBoostBonusConfig[price] = arrConfig[i]
    if price > self.m_maxBoostBonusPrice then
      self.m_maxBoostBonusPrice = price
    else
      Log.Error("买体力送双倍配置顺序错误")
    end
  end
  self.m_bLateInited = true
end

function EnergyBoostModel:GetTimeLimitedConfigDurationInMinutes()
  return TRIGGER_DURATION_IN_MINS
end

function EnergyBoostModel:GetTimeLimitedTriggerCount()
  return self.m_triggerCount
end

function EnergyBoostModel:OnAddEnergy(msg)
  if not self.m_bBoostTriggerOn or msg.type ~= EnergyType.Main then
    return
  end
  local leftTime = self:GetTimeLimitedLeftTime()
  if self.m_triggerCount > 0 and msg.cur >= self.m_triggerCount and leftTime < TRIGGER_DURATION_IN_SECONDS then
    self:_TriggerTimeLimitedEnergyBoost(TRIGGER_DURATION_IN_SECONDS - leftTime)
  end
end

function EnergyBoostModel:OnBuyEnergySuccess(buyEnergyCost)
  local freeBoostInMin = self:GetBoostBonusDurationInMin(buyEnergyCost)
  if 0 < freeBoostInMin then
    self:_TriggerTimeLimitedEnergyBoost(freeBoostInMin * 60)
  end
end

function EnergyBoostModel:GetBoostBonusDurationInMin(buyEnergyCost)
  if not self.m_bBoostTriggerOn then
    return 0
  end
  if not self.m_mapBoostBonusConfig then
    return 0
  end
  local durationInMin = self.m_mapBoostBonusConfig[buyEnergyCost]
  if durationInMin then
    return durationInMin
  end
  if self.m_maxBoostBonusPrice and buyEnergyCost and buyEnergyCost > self.m_maxBoostBonusPrice then
    return self.m_mapBoostBonusConfig[self.m_maxBoostBonusPrice] or 0
  end
  return 0
end

function EnergyBoostModel:UpdatePerSecond()
  if not self.m_bLateInited then
    return
  end
  local isTimeLimitedOn = self:_IsTimeLimitedEnergyBoostOn()
  if isTimeLimitedOn ~= self.m_bTimeLimitedOn then
    self.m_bTimeLimitedOn = isTimeLimitedOn
    EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
  end
end

function EnergyBoostModel:_TriggerTimeLimitedEnergyBoost(duration)
  local currentEndTime = GM.MiscModel:GetEnergyBoostTriggerEndTimeInNumber()
  currentEndTime = math.max(currentEndTime, GM.GameModel:GetServerTime())
  GM.MiscModel:SetEnergyBoostTriggerEndTime(currentEndTime + duration)
  if not self.m_bTimeLimitedOn then
    self:UpdatePerSecond()
  end
  GM.BIManager:LogAction(EBIType.TimeLimitedEnergyBoostTriggered, duration)
end

function EnergyBoostModel:_IsTimeLimitedEnergyBoostOn()
  return GM.GameModel:GetServerTime() < GM.MiscModel:GetEnergyBoostTriggerEndTimeInNumber()
end

function EnergyBoostModel:IsEnergyBoostModeOn()
  return self:IsEnergyBoostConfigOn() and self:IsEnergyBoostUserOn()
end

function EnergyBoostModel:IsEnergyBoostConfigOn()
  return self.m_bBoostOn or self.m_bTimeLimitedOn
end

function EnergyBoostModel:IsSwitchModeOn()
  return self.m_bBoostOn
end

function EnergyBoostModel:IsTimeLimitedOn()
  return self.m_bTimeLimitedOn
end

function EnergyBoostModel:GetTimeLimitedLeftTime()
  local leftTime = GM.MiscModel:GetEnergyBoostTriggerEndTimeInNumber() - GM.GameModel:GetServerTime()
  return leftTime < 0 and 0 or leftTime
end

function EnergyBoostModel:IsEnergyBoostUserOn()
  return GM.MiscModel:GetEnergyBoostUserOnInNumber() == 1
end

function EnergyBoostModel:SetEnergyBoostUserOn(on, byUser)
  GM.MiscModel:SetEnergyBoostUserOn(on and 1 or 0)
  if byUser ~= false then
    GM.BIManager:LogAction(EBIType.EnergyBoostSwitchUserOn, {
      on = on and 1 or 0
    })
  end
  EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
end

function EnergyBoostModel:CanEnergyBoost(type)
  if not self:IsEnergyBoostModeOn() then
    return false
  end
  if not GM.ItemDataModel:IsPd(type) then
    return false
  end
  local modelConfig = GM.ItemDataModel:GetModelConfig(type)
  return modelConfig and modelConfig.UseEnergy == 1
end

function EnergyBoostModel:GetEnergyBoostCostNum()
  return 2
end
