# 母棋子InitialNumber配置优先级分析

## 问题描述

PDItemSPConfig中PD = "pd_1_5"的InitialNumber = 19，ItemModelConfig中Type = "pd_1_5"的InitialNumber = 18，以哪个为准？

## 核心结论

**答案：以PDItemSPConfig中的InitialNumber = 19为准**

当母棋子首次获得时，PDItemSPConfig的特殊配置会覆盖ItemModelConfig的默认配置。

## 详细分析

### 1. 配置加载顺序

**代码位置**: `ItemSpread.lua:32-67`
```lua
function ItemSpread:Init(itemConfig, needUnlockType)
  -- ... 其他初始化代码 ...
  
  -- 第一步：使用ItemModelConfig的默认配置
  self.m_storageRestNumber = itemConfig.InitialNumber  -- 这里是18
  
  -- 第二步：如果是首次获得，应用PDItemSPConfig的特殊配置
  if needUnlockType then
    self:_PDSpecial()  -- 这里会覆盖为19
  end
  
  -- ... 其他初始化代码 ...
end
```

### 2. 特殊配置覆盖机制

**代码位置**: `ItemSpread.lua:89-99`
```lua
function ItemSpread:_PDSpecial()
  local tapeItems, initialNumber = GM.PDItemSPModel:OnPDCreated(self.m_itemConfig.Type)
  if tapeItems then
    self.m_codeWeightPairs = Table.DeepCopy(tapeItems)
    self.m_weightType = ItemSpreadWeightType.Fixed
    self.m_bCanInherit = true
  end
  if initialNumber then
    self.m_storageRestNumber = initialNumber  -- 覆盖为PDItemSPConfig的值
  end
end
```

**关键点**：
- `initialNumber`来自PDItemSPConfig配置
- 只有当`initialNumber`不为nil时才会覆盖
- 覆盖发生在初始化的最后阶段

### 3. 配置对比

#### PDItemSPConfig配置
**代码位置**: `PDItemSPConfig.lua:11-27`
```lua
{
  PD = "pd_1_5",
  Index = 1,
  StartTapeItems = {
    {Code = "it_1_1_2", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_1", Weight = 2},
    {Code = "it_1_1_1", Weight = 3},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_1", Weight = 2},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_1", Weight = 3},
    {Code = "it_1_2_1", Weight = 2},
    {Code = "it_1_1_2", Weight = 1}
  },
  InitialNumber = 19  -- 特殊配置：19个
}
```

#### ItemModelConfig配置
**代码位置**: `ItemModelConfig.lua:79-119`
```lua
{
  Type = "pd_1_5",
  MergedType = "pd_1_6",
  Category = {2},
  BookOrder = 1001,
  BookReward = {
    {Currency = "energy", Amount = 1}
  },
  UnlockPrice = 824,
  UseEnergy = 1,
  TapeItems = {
    {Code = "it_1_1_2", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    -- ... 20个固定序列物品
  },
  GeneratedItems = {
    {Code = "it_1_1_1", Weight = 9},
    {Code = "it_1_1_2", Weight = 6},
    {Code = "it_1_2_1", Weight = 5}
  },
  Cd = 6000,
  InitialNumber = 18,  -- 默认配置：18个
  Frequency = 10,
  Capacity = 30,
  SpeedUpPrice = 6
}
```

### 4. 不同获得次数的InitialNumber

#### 首次获得 (Index = 1)
- **使用**: PDItemSPConfig的InitialNumber = 19
- **原因**: `_PDSpecial()`会覆盖默认配置

#### 第二次获得 (Index = 2)
**代码位置**: `PDItemSPConfig.lua:61-72`
```lua
{
  PD = "pd_1_5",
  Index = 2,
  StartTapeItems = {
    {Code = "it_1_1_2", Weight = 2},
    {Code = "it_1_1_1", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_1", Weight = 1},
    {Code = "it_1_1_2", Weight = 2},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_2", Weight = 2}
  },
  InitialNumber = 10  -- 第二次获得：10个
}
```

#### 第三次获得 (Index = 3)
**代码位置**: `PDItemSPConfig.lua:141-153`
```lua
{
  PD = "pd_1_5",
  Index = 3,
  StartTapeItems = {
    {Code = "it_1_1_1", Weight = 2},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_1", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_1", Weight = 1},
    {Code = "it_1_2_1", Weight = 2},
    {Code = "it_1_1_1", Weight = 1},
    {Code = "it_1_2_1", Weight = 1}
  },
  InitialNumber = 10  -- 第三次获得：10个
}
```

#### 第四次获得 (Index = 4)
**代码位置**: `PDItemSPConfig.lua:203-217`
```lua
{
  PD = "pd_1_5",
  Index = 4,
  StartTapeItems = {
    {Code = "it_1_1_2", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_1", Weight = 1},
    {Code = "it_1_1_2", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_1", Weight = 1},
    {Code = "it_1_1_2", Weight = 1},
    {Code = "it_1_1_1", Weight = 1},
    {Code = "it_1_1_2", Weight = 1},
    {Code = "it_1_1_2", Weight = 1}
  },
  InitialNumber = 10  -- 第四次获得：10个
}
```

#### 第五次及以后获得
- **使用**: ItemModelConfig的InitialNumber = 18
- **原因**: PDItemSPConfig中没有更多Index配置，回退到默认配置

### 5. 优先级规则总结

```lua
-- 优先级判断逻辑
function GetInitialNumber(pdCode, createdCount)
  local pdConfig = PDItemSPConfig[pdCode][createdCount]
  if pdConfig and pdConfig.InitialNumber then
    return pdConfig.InitialNumber  -- 优先使用特殊配置
  else
    return ItemModelConfig[pdCode].InitialNumber  -- 回退到默认配置
  end
end
```

**优先级顺序**：
1. **最高优先级**: PDItemSPConfig中对应Index的InitialNumber
2. **默认优先级**: ItemModelConfig中的InitialNumber

### 6. 实际游戏数据验证

从数据库数据可以看到：
```json
"PDItemSPIndex": {
  "value": "pd_1_6-1;pd_2_4-4;pd_1_5-4;pd_2_6-1;greenbox_1-1;pd_2_5-2;pd_1_4-7;pd_3_4-1"
}
```

这表明pd_1_5已经获得了4次，意味着：
- 第1次：InitialNumber = 19 (PDItemSPConfig Index=1)
- 第2次：InitialNumber = 10 (PDItemSPConfig Index=2)
- 第3次：InitialNumber = 10 (PDItemSPConfig Index=3)
- 第4次：InitialNumber = 10 (PDItemSPConfig Index=4)
- 第5次及以后：InitialNumber = 18 (ItemModelConfig默认)

## 总结

**最终答案**: 对于pd_1_5的InitialNumber，**以PDItemSPConfig中的配置为准**：

- **首次获得**: 19个 (PDItemSPConfig Index=1)
- **第2-4次获得**: 10个 (PDItemSPConfig Index=2-4)
- **第5次及以后**: 18个 (ItemModelConfig默认)

**设计理念**:
- PDItemSPConfig提供新手友好的特殊体验
- 首次获得给予更多库存(19个)，降低新手难度
- 后续获得逐渐减少特殊优待，回归正常游戏平衡
- 最终回退到标准配置，保证长期游戏平衡
