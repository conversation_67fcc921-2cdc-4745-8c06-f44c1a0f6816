EIAPType = {
  getgem_1 = "getgem_1",
  getgem_2 = "getgem_2",
  getgem_3 = "getgem_3",
  getgem_4 = "getgem_4",
  getgem_5 = "getgem_5",
  getgem_6 = "getgem_6",
  starter_bundle_1 = "starter_bundle_1",
  passticket = "vip_ticket_1",
  passticket2 = "vip_ticket_2",
  maxpassticket = "super_vip_ticket_1",
  maxpassticket2 = "super_vip_ticket_2",
  uppassticket = "up_vip_ticket_1",
  uppassticket2 = "up_vip_ticket_2",
  energy_199 = "energy_199",
  energy_399 = "energy_399",
  energy_599 = "energy_599",
  energy_799 = "energy_799",
  energy_999 = "energy_999",
  energy_1999 = "energy_1999",
  rush_order_199 = "rush_order_199",
  rush_order_399 = "rush_order_399",
  rush_order_599 = "rush_order_599",
  rush_order_799 = "rush_order_799",
  rush_order_999 = "rush_order_999",
  rush_order_1999 = "rush_order_1999",
  common_099 = "common_099",
  common_199 = "common_199",
  common_299 = "common_299",
  common_399 = "common_399",
  common_499 = "common_499",
  common_599 = "common_599",
  common_799 = "common_799",
  common_999 = "common_999",
  common_1299 = "common_1299",
  common_1499 = "common_1499",
  common_1799 = "common_1799",
  common_1999 = "common_1999",
  common_2499 = "common_2499",
  finish_order_199 = "finish_order_199",
  finish_order_399 = "finish_order_399",
  finish_order_599 = "finish_order_599",
  finish_order_799 = "finish_order_799",
  finish_order_999 = "finish_order_999",
  finish_order_1999 = "finish_order_1999",
  energytier_199 = "energytier_199",
  energytier_399 = "energytier_399",
  energytier_599 = "energytier_599",
  energytier_799 = "energytier_799",
  energytier_999 = "energytier_999",
  energytier_1599 = "energytier_1599",
  energytier_1999 = "energytier_1999",
  energytier_2599 = "energytier_2599",
  chaingift_199 = "chaingift_199",
  chaingift_299 = "chaingift_299",
  chaingift_399 = "chaingift_399",
  chaingift_499 = "chaingift_499",
  chaingift_699 = "chaingift_699",
  chaingift_1599 = "chaingift_1599",
  chaingift_1999 = "chaingift_1999",
  chaingift_2999 = "chaingift_2999",
  digchain99 = "digchain99",
  digchain199 = "digchain199",
  digchain299 = "digchain299",
  digchain499 = "digchain499",
  digchain599 = "digchain599",
  digchain799 = "digchain799",
  digchain999 = "digchain999",
  digchain1299 = "digchain1299",
  digchain1499 = "digchain1499",
  digchain1799 = "digchain1799",
  digchain1999 = "digchain1999",
  blindchestchain_99 = "blindchestchain_99",
  blindchestchain_199 = "blindchestchain_199",
  blindchestchain_299 = "blindchestchain_299",
  blindchestchain_499 = "blindchestchain_499",
  blindchestchain_599 = "blindchestchain_599",
  blindchestchain_799 = "blindchestchain_799",
  blindchestchain_999 = "blindchestchain_999",
  blindchestchain_1299 = "blindchestchain_1299",
  blindchestchain_1499 = "blindchestchain_1499",
  blindchestchain_1799 = "blindchestchain_1799",
  blindchestchain_1999 = "blindchestchain_1999",
  digoneplus_199 = "digoneplus_199",
  digoneplus_299 = "digoneplus_299",
  digoneplus_499 = "digoneplus_499",
  digoneplus_799 = "digoneplus_799",
  digoneplus_1299 = "digoneplus_1299",
  digoneplus_1799 = "digoneplus_1799",
  chestoneplus_199 = "chestoneplus_199",
  chestoneplus_299 = "chestoneplus_299",
  chestoneplus_499 = "chestoneplus_499",
  chestoneplus_799 = "chestoneplus_799",
  chestoneplus_1299 = "chestoneplus_1299",
  chestoneplus_1799 = "chestoneplus_1799",
  cd_speed_199 = "cd_speed_199",
  cd_speed_399 = "cd_speed_399",
  cd_speed_599 = "cd_speed_599",
  cd_speed_799 = "cd_speed_799",
  cd_speed_999 = "cd_speed_999",
  cd_speed_1999 = "cd_speed_1999"
}
IAPProduct = {}
IAPProduct.__index = IAPProduct
local iapPrefix = DeviceInfo.IsSystemIOS() and "com.mf.cola" or "com.cola.game"

function IAPProduct.GetProducts()
  return {
    [EIAPType.getgem_1] = IAPProduct.Create(EIAPType.getgem_1, 1.99, "USD"),
    [EIAPType.getgem_2] = IAPProduct.Create(EIAPType.getgem_2, 4.99, "USD"),
    [EIAPType.getgem_3] = IAPProduct.Create(EIAPType.getgem_3, 9.99, "USD"),
    [EIAPType.getgem_4] = IAPProduct.Create(EIAPType.getgem_4, 19.99, "USD"),
    [EIAPType.getgem_5] = IAPProduct.Create(EIAPType.getgem_5, 49.99, "USD"),
    [EIAPType.getgem_6] = IAPProduct.Create(EIAPType.getgem_6, 99.99, "USD"),
    [EIAPType.starter_bundle_1] = IAPProduct.Create(EIAPType.starter_bundle_1, 4.99, "USD"),
    [EIAPType.passticket] = IAPProduct.Create(EIAPType.passticket, 4.99, "USD"),
    [EIAPType.passticket2] = IAPProduct.Create(EIAPType.passticket2, 4.99, "USD"),
    [EIAPType.maxpassticket] = IAPProduct.Create(EIAPType.maxpassticket, 7.99, "USD"),
    [EIAPType.uppassticket] = IAPProduct.Create(EIAPType.uppassticket, 2.99, "USD"),
    [EIAPType.maxpassticket2] = IAPProduct.Create(EIAPType.maxpassticket2, 7.99, "USD"),
    [EIAPType.uppassticket2] = IAPProduct.Create(EIAPType.uppassticket2, 2.99, "USD"),
    [EIAPType.energy_199] = IAPProduct.Create(EIAPType.energy_199, 1.99, "USD"),
    [EIAPType.energy_399] = IAPProduct.Create(EIAPType.energy_399, 3.99, "USD"),
    [EIAPType.energy_599] = IAPProduct.Create(EIAPType.energy_599, 5.99, "USD"),
    [EIAPType.energy_799] = IAPProduct.Create(EIAPType.energy_799, 7.99, "USD"),
    [EIAPType.energy_999] = IAPProduct.Create(EIAPType.energy_999, 9.99, "USD"),
    [EIAPType.energy_1999] = IAPProduct.Create(EIAPType.energy_1999, 19.99, "USD"),
    [EIAPType.rush_order_199] = IAPProduct.Create(EIAPType.rush_order_199, 1.99, "USD"),
    [EIAPType.rush_order_399] = IAPProduct.Create(EIAPType.rush_order_399, 3.99, "USD"),
    [EIAPType.rush_order_599] = IAPProduct.Create(EIAPType.rush_order_599, 5.99, "USD"),
    [EIAPType.rush_order_799] = IAPProduct.Create(EIAPType.rush_order_799, 7.99, "USD"),
    [EIAPType.rush_order_999] = IAPProduct.Create(EIAPType.rush_order_999, 9.99, "USD"),
    [EIAPType.rush_order_1999] = IAPProduct.Create(EIAPType.rush_order_1999, 19.99, "USD"),
    [EIAPType.common_099] = IAPProduct.Create(EIAPType.common_099, 0.99, "USD"),
    [EIAPType.common_199] = IAPProduct.Create(EIAPType.common_199, 1.99, "USD"),
    [EIAPType.common_299] = IAPProduct.Create(EIAPType.common_299, 2.99, "USD"),
    [EIAPType.common_399] = IAPProduct.Create(EIAPType.common_399, 3.99, "USD"),
    [EIAPType.common_499] = IAPProduct.Create(EIAPType.common_499, 4.99, "USD"),
    [EIAPType.common_599] = IAPProduct.Create(EIAPType.common_599, 5.99, "USD"),
    [EIAPType.common_799] = IAPProduct.Create(EIAPType.common_799, 7.99, "USD"),
    [EIAPType.common_999] = IAPProduct.Create(EIAPType.common_999, 9.99, "USD"),
    [EIAPType.common_1299] = IAPProduct.Create(EIAPType.common_1299, 12.99, "USD"),
    [EIAPType.common_1499] = IAPProduct.Create(EIAPType.common_1499, 14.99, "USD"),
    [EIAPType.common_1799] = IAPProduct.Create(EIAPType.common_1799, 17.99, "USD"),
    [EIAPType.common_1999] = IAPProduct.Create(EIAPType.common_1999, 19.99, "USD"),
    [EIAPType.common_2499] = IAPProduct.Create(EIAPType.common_2499, 24.99, "USD"),
    [EIAPType.finish_order_199] = IAPProduct.Create(EIAPType.finish_order_199, 1.99, "USD"),
    [EIAPType.finish_order_399] = IAPProduct.Create(EIAPType.finish_order_399, 3.99, "USD"),
    [EIAPType.finish_order_599] = IAPProduct.Create(EIAPType.finish_order_599, 5.99, "USD"),
    [EIAPType.finish_order_799] = IAPProduct.Create(EIAPType.finish_order_799, 7.99, "USD"),
    [EIAPType.finish_order_999] = IAPProduct.Create(EIAPType.finish_order_999, 9.99, "USD"),
    [EIAPType.finish_order_1999] = IAPProduct.Create(EIAPType.finish_order_1999, 19.99, "USD"),
    [EIAPType.energytier_199] = IAPProduct.Create(EIAPType.energytier_199, 1.99, "USD"),
    [EIAPType.energytier_399] = IAPProduct.Create(EIAPType.energytier_399, 3.99, "USD"),
    [EIAPType.energytier_599] = IAPProduct.Create(EIAPType.energytier_599, 5.99, "USD"),
    [EIAPType.energytier_799] = IAPProduct.Create(EIAPType.energytier_799, 7.99, "USD"),
    [EIAPType.energytier_999] = IAPProduct.Create(EIAPType.energytier_999, 9.99, "USD"),
    [EIAPType.energytier_1599] = IAPProduct.Create(EIAPType.energytier_1599, 15.99, "USD"),
    [EIAPType.energytier_1999] = IAPProduct.Create(EIAPType.energytier_1999, 19.99, "USD"),
    [EIAPType.energytier_2599] = IAPProduct.Create(EIAPType.energytier_2599, 25.99, "USD"),
    [EIAPType.chaingift_199] = IAPProduct.Create(EIAPType.chaingift_199, 1.99, "USD"),
    [EIAPType.chaingift_299] = IAPProduct.Create(EIAPType.chaingift_299, 2.99, "USD"),
    [EIAPType.chaingift_399] = IAPProduct.Create(EIAPType.chaingift_399, 3.99, "USD"),
    [EIAPType.chaingift_499] = IAPProduct.Create(EIAPType.chaingift_499, 4.99, "USD"),
    [EIAPType.chaingift_699] = IAPProduct.Create(EIAPType.chaingift_699, 6.99, "USD"),
    [EIAPType.chaingift_1599] = IAPProduct.Create(EIAPType.chaingift_1599, 15.99, "USD"),
    [EIAPType.chaingift_1999] = IAPProduct.Create(EIAPType.chaingift_1999, 19.99, "USD"),
    [EIAPType.chaingift_2999] = IAPProduct.Create(EIAPType.chaingift_2999, 29.99, "USD"),
    [EIAPType.digchain99] = IAPProduct.Create(EIAPType.digchain99, 0.99, "USD"),
    [EIAPType.digchain199] = IAPProduct.Create(EIAPType.digchain199, 1.99, "USD"),
    [EIAPType.digchain299] = IAPProduct.Create(EIAPType.digchain299, 2.99, "USD"),
    [EIAPType.digchain499] = IAPProduct.Create(EIAPType.digchain499, 4.99, "USD"),
    [EIAPType.digchain599] = IAPProduct.Create(EIAPType.digchain599, 5.99, "USD"),
    [EIAPType.digchain799] = IAPProduct.Create(EIAPType.digchain799, 7.99, "USD"),
    [EIAPType.digchain999] = IAPProduct.Create(EIAPType.digchain999, 9.99, "USD"),
    [EIAPType.digchain1299] = IAPProduct.Create(EIAPType.digchain1299, 12.99, "USD"),
    [EIAPType.digchain1499] = IAPProduct.Create(EIAPType.digchain1499, 14.99, "USD"),
    [EIAPType.digchain1799] = IAPProduct.Create(EIAPType.digchain1799, 17.99, "USD"),
    [EIAPType.digchain1999] = IAPProduct.Create(EIAPType.digchain1999, 19.99, "USD"),
    [EIAPType.blindchestchain_99] = IAPProduct.Create(EIAPType.blindchestchain_99, 0.99, "USD"),
    [EIAPType.blindchestchain_199] = IAPProduct.Create(EIAPType.blindchestchain_199, 1.99, "USD"),
    [EIAPType.blindchestchain_299] = IAPProduct.Create(EIAPType.blindchestchain_299, 2.99, "USD"),
    [EIAPType.blindchestchain_499] = IAPProduct.Create(EIAPType.blindchestchain_499, 4.99, "USD"),
    [EIAPType.blindchestchain_599] = IAPProduct.Create(EIAPType.blindchestchain_599, 5.99, "USD"),
    [EIAPType.blindchestchain_799] = IAPProduct.Create(EIAPType.blindchestchain_799, 7.99, "USD"),
    [EIAPType.blindchestchain_999] = IAPProduct.Create(EIAPType.blindchestchain_999, 9.99, "USD"),
    [EIAPType.blindchestchain_1299] = IAPProduct.Create(EIAPType.blindchestchain_1299, 12.99, "USD"),
    [EIAPType.blindchestchain_1499] = IAPProduct.Create(EIAPType.blindchestchain_1499, 14.99, "USD"),
    [EIAPType.blindchestchain_1799] = IAPProduct.Create(EIAPType.blindchestchain_1799, 17.99, "USD"),
    [EIAPType.blindchestchain_1999] = IAPProduct.Create(EIAPType.blindchestchain_1999, 19.99, "USD"),
    [EIAPType.digoneplus_199] = IAPProduct.Create(EIAPType.digoneplus_199, 1.99, "USD"),
    [EIAPType.digoneplus_299] = IAPProduct.Create(EIAPType.digoneplus_299, 2.99, "USD"),
    [EIAPType.digoneplus_499] = IAPProduct.Create(EIAPType.digoneplus_499, 4.99, "USD"),
    [EIAPType.digoneplus_799] = IAPProduct.Create(EIAPType.digoneplus_799, 7.99, "USD"),
    [EIAPType.digoneplus_1299] = IAPProduct.Create(EIAPType.digoneplus_1299, 12.99, "USD"),
    [EIAPType.digoneplus_1799] = IAPProduct.Create(EIAPType.digoneplus_1799, 17.99, "USD"),
    [EIAPType.chestoneplus_199] = IAPProduct.Create(EIAPType.chestoneplus_199, 1.99, "USD"),
    [EIAPType.chestoneplus_299] = IAPProduct.Create(EIAPType.chestoneplus_299, 2.99, "USD"),
    [EIAPType.chestoneplus_499] = IAPProduct.Create(EIAPType.chestoneplus_499, 4.99, "USD"),
    [EIAPType.chestoneplus_799] = IAPProduct.Create(EIAPType.chestoneplus_799, 7.99, "USD"),
    [EIAPType.chestoneplus_1299] = IAPProduct.Create(EIAPType.chestoneplus_1299, 12.99, "USD"),
    [EIAPType.chestoneplus_1799] = IAPProduct.Create(EIAPType.chestoneplus_1799, 17.99, "USD"),
    [EIAPType.cd_speed_199] = IAPProduct.Create(EIAPType.cd_speed_199, 1.99, "USD"),
    [EIAPType.cd_speed_399] = IAPProduct.Create(EIAPType.cd_speed_399, 3.99, "USD"),
    [EIAPType.cd_speed_599] = IAPProduct.Create(EIAPType.cd_speed_599, 5.99, "USD"),
    [EIAPType.cd_speed_799] = IAPProduct.Create(EIAPType.cd_speed_799, 7.99, "USD"),
    [EIAPType.cd_speed_999] = IAPProduct.Create(EIAPType.cd_speed_999, 9.99, "USD"),
    [EIAPType.cd_speed_1999] = IAPProduct.Create(EIAPType.cd_speed_1999, 19.99, "USD")
  }
end

function IAPProduct.Create(eType, fixedMoney, currency)
  local instance = setmetatable({}, IAPProduct)
  instance:_Init(eType, fixedMoney, currency)
  return instance
end

function IAPProduct:_Init(eType, fixedMoney, currency)
  self.eType = eType
  self.productId = iapPrefix .. "." .. self.eType
  self.fixedMoney = fixedMoney
  self.realMoney = fixedMoney
  self.currency = currency
  self.localizedPrice = (self.currency == "USD" and "US$ " or "￥ ") .. string.format("%.2f", fixedMoney)
end

function IAPProduct:UpdateInfo(localizedPrice, realMoney, currency)
  self.localizedPrice = localizedPrice
  if 0 < realMoney then
    self.realMoney = realMoney
    self.currency = currency
  end
end
