AlbumActivityTakeSetRewardWindow = setmetatable({
  bCloseByStateChanged = false,
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, AlbumActivityBaseWindow)
AlbumActivityTakeSetRewardWindow.__index = AlbumActivityTakeSetRewardWindow

function AlbumActivityTakeSetRewardWindow:Init(activityType, openListSetID, bAllAlbum)
  AlbumActivityBaseWindow.Init(self, activityType, false)
  self.m_arrSet = openListSetID
  self.m_setID = openListSetID[1]
  self.m_bAllAlbum = bAllAlbum
  table.remove(self.m_arrSet, 1)
  UIUtil.UpdateSortingOrder(self.m_rewardContent.gameObject, self:GetSortingOrder() - 1)
  UIUtil.UpdateSortingOrder(self.m_effectGo, self:GetSortingOrder() - 1)
  self:UpdateContent()
  local checkRect = self.m_checkGo.transform
  UIUtil.SetLocalScale(checkRect, 0, 0)
  DOTween.Sequence():AppendInterval(0.25):Append(checkRect:DOScale(Vector3(1.4, 1.4, 1), 0.2)):Append(checkRect:DOScale(Vector3(1.2, 1.2, 1), 0.1))
end

function AlbumActivityTakeSetRewardWindow:UpdateContent()
  local setId = self.m_setID
  SpriteUtil.SetImage(self.m_setImg, setId)
  self.m_setNameText.text = GM.GameTextModel:GetText(setId .. "_name")
  local rewards = self.m_model:GetCardSetRewards(setId)
  self.m_rewardContent:Init(rewards)
end

function AlbumActivityTakeSetRewardWindow:OnCloseView()
  AlbumActivityBaseWindow.OnCloseView(self)
  self.m_rewardContent:PlayRewardAnimation(false)
  local bExecute = false
  local continueFunc = function()
    if bExecute then
      return
    end
    bExecute = true
    GM.UIManager:SetEventLock(false, self.m_model)
    GM.UIManager:RemoveAllEventLocks(self.m_model)
    if #self.m_arrSet > 0 then
      GM.UIManager:OpenView(self.m_activityDefinition.TakeSetRewardPrefabName, self.m_model:GetType(), self.m_arrSet, self.m_bAllAlbum)
    elseif self.m_bAllAlbum then
      GM.UIManager:OpenView(self.m_activityDefinition.TakeFinishRewardPrefabName, self.m_model:GetType())
    end
  end
  GM.UIManager:SetEventLock(true, self.m_model, function()
    continueFunc()
  end)
  DelayExecuteFunc(function()
    continueFunc()
  end, 1.5)
end

AlbumActivityTakeFinishRewardWindow = setmetatable({
  bCloseByStateChanged = false,
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, AlbumActivityBaseWindow)
AlbumActivityTakeFinishRewardWindow.__index = AlbumActivityTakeFinishRewardWindow

function AlbumActivityTakeFinishRewardWindow:Init(activityType)
  AlbumActivityBaseWindow.Init(self, activityType, false)
  UIUtil.UpdateSortingOrder(self.m_rewardContent.gameObject, self:GetSortingOrder() - 1)
  UIUtil.UpdateSortingOrder(self.m_effectGo, self:GetSortingOrder() - 1)
  self:InitRewards()
  UIUtil.SetLocalScale(self.m_effectLightGo.transform, 0, 0)
  self.m_effectLightGo.transform:DOScale(Vector3(100, 100, 1), 0.2):SetDelay(0.35)
end

function AlbumActivityTakeFinishRewardWindow:InitRewards()
  local rewards = self.m_model:GetAlbumConfig().reward
  self.m_rewardContent:Init(rewards)
end

function AlbumActivityTakeFinishRewardWindow:OnCloseView()
  AlbumActivityBaseWindow.OnCloseView(self)
  self.m_rewardContent:PlayRewardAnimation()
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
end
