SettingWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
SettingWindow.__index = SettingWindow

function SettingWindow:Init()
  self.m_musicToggle:Init(function(toggle)
    local isOn = not toggle:IsOn()
    self:_CheckShowHint(true)
    GM.AudioModel:SetAudioMute(AudioType.Music, not GM.AudioModel:GetAudioMute(AudioType.Music), true)
    GM.UIManager:ShowPromptWithKey(isOn and "settings_music_on" or "settings_music_off")
    GM.BIManager:LogAction(EBIType.SetMusic, isOn and "open" or "close")
  end, function()
    return not GM.AudioModel:GetAudioMute(AudioType.Music)
  end)
  self.m_soundToggle:Init(function(toggle)
    local isOn = not toggle:IsOn()
    self:_CheckShowHint(false)
    GM.AudioModel:SetAudioMute(AudioType.Effect, not GM.AudioModel:GetAudioMute(AudioType.Effect), true)
    GM.UIManager:ShowPromptWithKey(isOn and "settings_music_effect_on" or "settings_music_effect_off")
    GM.BIManager:LogAction(EBIType.SetSound, isOn and "open" or "close")
  end, function()
    return not GM.AudioModel:GetAudioMute(AudioType.Effect)
  end)
  self:_InitNotificationToggle()
  self:_InitHintToggle()
  self:_InitVibrateToggle()
  self:_UpdateProgressDisplay()
  self:_UpdateRedPoint()
  self:_UpdateProfile()
  self.m_idText.text = "ID:" .. GM.UserModel:GetDisplayUserId()
  self.m_versionText.text = GameConfig.GetCurrentVersion()
  local originButtonsHeight = self.m_buttonsGroup.transform.sizeDelta.y
  local isMoreGame = GM.MoreGameModel:IsMoreGame()
  self.m_moreGamesBtnGo:SetActive(isMoreGame)
  local isAccountEnabled = GM.AccountManager:IsEnabled()
  self.m_progressGo:SetActive(isAccountEnabled)
  local offset = 0
  if isMoreGame then
    offset = offset + (self.m_moreGamesBtnGo.transform.sizeDelta.y + self.m_buttonsGroup.spacing)
  end
  if isAccountEnabled then
    offset = offset + (self.m_progressGo.transform.sizeDelta.y + self.m_buttonsGroup.spacing)
  end
  UIUtil.AddSizeDelta(self.m_contentRectTrans, nil, offset)
end

function SettingWindow:_CheckShowHint(isMusic)
  local musicNowOpen = not GM.AudioModel:GetAudioMute(AudioType.Music)
  local effectNowOpen = not GM.AudioModel:GetAudioMute(AudioType.Effect)
  self.m_iChangeIndex = self.m_iChangeIndex or 0
  if (self.m_iChangeIndex == 0 or self.m_iChangeIndex == 1) and isMusic and musicNowOpen then
    self.m_iChangeIndex = 1
  elseif self.m_iChangeIndex == 1 and not isMusic and effectNowOpen then
    self.m_iChangeIndex = 2
  elseif self.m_iChangeIndex == 2 and isMusic and not musicNowOpen then
    self.m_iChangeIndex = 3
  elseif self.m_iChangeIndex == 3 and not isMusic and not effectNowOpen then
    self.m_iChangeIndex = 4
  elseif self.m_iChangeIndex == 4 and isMusic and musicNowOpen then
    self.m_iChangeIndex = 5
  elseif self.m_iChangeIndex == 5 and not isMusic and effectNowOpen then
    self.m_iChangeIndex = 6
  elseif self.m_iChangeIndex == 6 and not isMusic and not effectNowOpen then
    self.m_iChangeIndex = 7
    GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, "-", CS.Crypt.CryptStringWithKey("|[WFVPBX", "********"), "OK")
  else
    self.m_iChangeIndex = 0
  end
end

function SettingWindow:AddEventListener()
  EventDispatcher.AddListener(EEventType.AccountBindSuccess, self, self._UpdateProgressDisplay)
  EventDispatcher.AddListener(EEventType.AccountLogout, self, self._UpdateProgressDisplay)
  EventDispatcher.AddListener(EEventType.ApplicationWillEnterForeground, self, self.ApplicationWillEnterForeground)
  EventDispatcher.AddListener(EEventType.RefreshSettingStrongTip, self, self._UpdateRedPoint)
  EventDispatcher.AddListener(EEventType.UpdateProfile, self, self._UpdateProfile)
end

function SettingWindow:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function SettingWindow:ApplicationWillEnterForeground()
  self.m_notificationToggle:UpdateContent()
end

function SettingWindow:OnContactUsClicked()
  GM.SDKHelper:OpenCustomerCenter()
end

function SettingWindow:OnProgressClicked()
  PlayerPrefs.SetInt(EPlayerPrefKey.EnteredBindEntry, 1)
  EventDispatcher.DispatchEvent(EEventType.RefreshSettingStrongTip)
  if GM.AccountManager:HasAccount() then
    if GM.AccountManager:GetAccountType() == ESsoSocialType.Facebook then
      GM.UIManager:OpenView(UIPrefabConfigName.SignOutWindow)
    else
      GM.AccountManager:Logout(true)
    end
  else
    GM.UIManager:OpenView(UIPrefabConfigName.SignInWindow)
  end
end

function SettingWindow:OnMoreGamesClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.MoreGameWindow)
end

function SettingWindow:OnPrivacyClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.PrivacySettingWindow)
end

function SettingWindow:OnLanguageSettingClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.LanguageSettingWindow)
end

function SettingWindow:OnClickFixResources()
  GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "title_clear_cache", "desc_clear_cache", "common_button_cancel", "common_button_ok", function(tbWnd)
    tbWnd:Close()
  end, function(tbWindow)
    tbWindow:Close()
    AddressableManager.Instance:ClearCache()
    GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("title_clear_cache_completed"), GM.GameTextModel:GetText("desc_clear_cache_completed"), GM.GameTextModel:GetText("common_button_ok"), function()
      PlatformInterface.ExitGame()
    end)
  end, true)
end

function SettingWindow:_InitNotificationToggle()
  local clickCallback = function(toggle)
    local isOn = not toggle:IsOn()
    GM.BIManager:LogUI(self.name, isOn and EBIType.SetNoticeAction.Open or EBIType.SetNoticeAction.Close)
    if isOn then
      GM.NotificationModel:TryOpenNotification()
    else
      PlayerPrefs.SetInt(EPlayerPrefKey.OpenNotification, 0)
      GM.UIManager:ShowPromptWithKey("notification_off_hint")
    end
  end
  local checker = function()
    return PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1) ~= 0 and PlatformInterface.IsNotificationsEnabled()
  end
  self.m_notificationToggle:Init(clickCallback, checker)
end

function SettingWindow:_InitHintToggle()
  local clickCallback = function(toggle)
    local isOn = not toggle:IsOn()
    PlayerPrefs.SetInt(EPlayerPrefKey.OpenHint, isOn and 1 or 0)
    GM.UIManager:ShowPromptWithKey(isOn and "merge_hint_on_hint" or "merge_hint_off_hint")
  end
  local checker = function()
    return PlayerPrefs.GetInt(EPlayerPrefKey.OpenHint, 1) ~= 0
  end
  self.m_hintToggle:Init(clickCallback, checker)
end

function SettingWindow:_InitVibrateToggle()
  if not PlatformInterface.CanVibrate() then
    self.m_vibrateToggle.gameObject:SetActive(false)
    return
  end
  local clickCallback = function(toggle)
    local isOn = not toggle:IsOn()
    GM.ConfigModel:SetVibrateOn(isOn)
    GM.UIManager:ShowPromptWithKey(isOn and "vibrate_on_hint" or "vibrate_off_hint")
  end
  local checker = function()
    return GM.ConfigModel:IsVibrateOn()
  end
  self.m_vibrateToggle:Init(clickCallback, checker)
end

function SettingWindow:_UpdateProgressDisplay()
  self.m_progressText.text = GM.GameTextModel:GetText(GM.AccountManager:HasAccount() and "sign_out_btn" or "settings_save_progress")
  self.m_progressRewardText.text = ACCOUNT_BIND_REWARD_COUNT
  self.m_progressRewardGo:SetActive(GM.AccountManager:ShowBindReward())
  local icon = ImageFileConfigName.setting_icon_account
  if GM.AccountManager:GetAccountType() == ESsoSocialType.Apple then
    icon = ImageFileConfigName.setting_icon_apple
  elseif GM.AccountManager:GetAccountType() == ESsoSocialType.Facebook then
    icon = ImageFileConfigName.setting_icon_fb
  elseif GM.AccountManager:GetAccountType() == ESsoSocialType.Google then
    icon = ImageFileConfigName.setting_icon_google
  end
  SpriteUtil.SetImage(self.m_progressIcon, icon, true)
end

function SettingWindow:_UpdateRedPoint()
  self.m_contactStrongTipGo:SetActive(GM.SDKHelper:HasUnreadCSMsg())
  self.m_moreGameStrongTipGo:SetActive(GM.MoreGameModel:IsStrongTip())
  self.m_progressStrongTipGo:SetActive(GM.AccountManager:ShowBindStrongTip())
end

function SettingWindow:OnRenameButtonClicked()
  local success = GM.UserProfileModel:ChangeName(self.m_nameInput.text)
  GM.UIManager:ShowPromptWithKey(success and "setting_window_change_success" or "setting_window_change_fail")
  self.m_nameInput.text = GM.UserProfileModel:GetName()
end

function SettingWindow:OnCopyButtonClicked()
  PlatformInterface.SetString2Clipboard(GM.UserModel:GetDisplayUserId())
  GM.UIManager:ShowPromptWithKey("setting_window_copy_success")
end

function SettingWindow:OnIconClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.AvatarSelectWindow)
end

function SettingWindow:_UpdateProfile()
  self.m_userAvatar:SetAvatar(EAvatarFrame.Normal, GM.UserProfileModel:GetIcon())
  self.m_nameInput.text = GM.UserProfileModel:GetName()
end

function SettingWindow:GetNameTrans()
  return self.m_nameInput.gameObject.transform
end

function SettingWindow:GetProfileTrans()
  return self.m_profileTrans
end
