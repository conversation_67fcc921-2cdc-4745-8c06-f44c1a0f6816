SpreadFailedReason = {
  ItemClosed = 1,
  ItemOpening = 2,
  ItemRecharging = 3,
  BoardFull = 4
}
ItemSpreadEventType = {
  StateChanged = 1,
  UpdateBoostHint = 2,
  AddItemBoost = 3,
  PlaySkipTimeAnim = 4,
  UpdateFire = 5,
  HideFire = 6
}
ItemSpreadState = {
  Initializing = 4,
  Closed = 1,
  Opening = 2,
  Opened = 3,
  OpenFinish = 5
}
ItemSpreadWeightType = {Fixed = 1, Decremental = 2}
ItemSpread = setmetatable({}, BaseItemComponent)
ItemSpread.__index = ItemSpread

function ItemSpread.Create(itemConfig, needUnlockType)
  local itemSpread = setmetatable({}, ItemSpread)
  itemSpread:Init(itemConfig, needUnlockType)
  return itemSpread
end

function ItemSpread:Init(itemConfig, needUnlockType)
  self.event = PairEvent.Create(self)
  self.m_itemConfig = itemConfig
  self.m_auto = itemConfig.SpreadAuto
  self.m_initDuration = itemConfig.SpreadInitDuration
  self.m_openDuration = itemConfig.NeedUnlock == 1 and itemConfig.Cd or nil
  self.m_costEnergy = itemConfig.UseEnergy
  self.m_storageMaxNumber = itemConfig.Capacity
  self.m_storageRecoverNumber = itemConfig.Frequency
  self.m_storageRecoverDuration = itemConfig.Cd
  self.m_speedUpMaxCost = itemConfig.SpeedUpPrice
  self.m_dropsTotal = itemConfig.DropsTotal
  self.m_bDisposable = itemConfig.Category and Table.ListContain(itemConfig.Category, EItemCategory.DisposableGenerator) and itemConfig.DropsTotal ~= nil
  self.m_startTimer = -1
  local forcedDuration = TutorialBoardHelper.GetForcedTimerDuration()
  if forcedDuration ~= nil then
    self.m_initDuration = self.m_initDuration and forcedDuration
    self.m_openDuration = self.m_openDuration and forcedDuration
    self.m_storageRecoverDuration = self.m_storageRecoverDuration or forcedDuration
  end
  if Table.ListContain(itemConfig.Category, EItemCategory.NoRecycleableSpreadItem) then
    if GameConfig.IsTestMode() and itemConfig.Cd ~= nil then
      Log.Error("棋子信息配置错误, Category为8的棋子无需配置Cd: itemName:" .. self.m_itemConfig.Type)
    end
    self.m_storageRecoverDuration = -1
  end
  if itemConfig.StartTapeItems then
    self.m_codeWeightPairs = Table.DeepCopy(itemConfig.StartTapeItems)
    self.m_weightType = ItemSpreadWeightType.Fixed
  else
    self:_GenNextSpreadItems()
  end
  self.m_storageRestNumber = itemConfig.InitialNumber
  if needUnlockType then
    self:_PDSpecial()
  end
  self.m_spreadCount = 0
  self.m_spreadAddItem = 0
  self.m_spreadItemBoxChain = {}
  if self.m_initDuration ~= nil then
    self.m_state = ItemSpreadState.Initializing
  elseif self.m_openDuration ~= nil then
    self.m_state = ItemSpreadState.Closed
  else
    self.m_state = ItemSpreadState.Opened
  end
  EventDispatcher.AddListener(EEventType.FlambeTimeChanged, self, self._OnFlambeTimeChanged)
end

function ItemSpread:Destroy()
  BaseItemComponent.Destroy(self)
  if self.m_codeNextTime then
    self.m_codeNextTime = nil
    self:_LogNextCode()
  end
end

function ItemSpread:_PDSpecial()
  local tapeItems, initialNumber = GM.PDItemSPModel:OnPDCreated(self.m_itemConfig.Type)
  if tapeItems then
    self.m_codeWeightPairs = Table.DeepCopy(tapeItems)
    self.m_weightType = ItemSpreadWeightType.Fixed
    self.m_bCanInherit = true
  end
  if initialNumber then
    self.m_storageRestNumber = initialNumber
  end
end

function ItemSpread:_GenNextSpreadItems()
  local codeWeightPairs, weightType
  if self.m_itemConfig.TapeItems then
    codeWeightPairs = self.m_itemConfig.TapeItems
    weightType = ItemSpreadWeightType.Fixed
  else
    codeWeightPairs = self.m_itemConfig.GeneratedItems
    weightType = ItemSpreadWeightType.Decremental
  end
  self.m_codeWeightPairs = Table.DeepCopy(codeWeightPairs)
  self.m_weightType = weightType
  self.m_bCanInherit = false
end

function ItemSpread:FromSerialization(dbTable)
  if not dbTable.spreadState then
    self:_PDSpecial()
    return
  end
  self.m_state = dbTable.spreadState
  self.m_startTimer = dbTable.spreadStartTimer
  self.m_storageRestNumber = dbTable.spreadStorageRestNumber
  self.m_spreadCount = dbTable.spreadCount
  self.m_codeWeightPairs = ItemUtility.StringToCodeWeightPairs(dbTable.spreadCodeWeightPairs)
  self.m_weightType = dbTable.spreadWeightType
  self.m_spreadAddItem = dbTable.spreadAddItem or self.m_spreadAddItem
  self.m_bCanInherit = dbTable.spreadInherit == 1
  self.m_spreadItemBoxChain = StringUtil.Split(dbTable.spreadItemBoxChain, ";")
end

function ItemSpread:ToSerialization(dbTable)
  dbTable.spreadState = self.m_state
  dbTable.spreadStartTimer = self.m_startTimer
  dbTable.spreadStorageRestNumber = self.m_storageRestNumber
  dbTable.spreadCount = self.m_spreadCount
  dbTable.spreadCodeWeightPairs = ItemUtility.CodeWeightPairsToString(self.m_codeWeightPairs)
  dbTable.spreadWeightType = self.m_weightType
  dbTable.spreadEnergyFree = 0
  dbTable.spreadAddItem = self.m_spreadAddItem
  dbTable.spreadTierUpCount = 0
  dbTable.spreadTierUpLevel = 0
  dbTable.spreadInherit = self.m_bCanInherit and 1 or 0
  dbTable.spreadItemBoxChain = table.concat(self.m_spreadItemBoxChain or {}, ";")
end

function ItemSpread:_Save()
  self.m_itemModel:GetBoardModel():SaveItemProperty(self.m_itemModel)
end

function ItemSpread:Update()
  if self:NeedUpdate() then
    while self:_TrySpread(true) do
    end
  end
end

function ItemSpread:NeedUpdate()
  return self:IsAutoSpread()
end

function ItemSpread:IsFlambeTime()
  return self.m_state == ItemSpreadState.Opened and GM.FlambeTimeModel:IsFlambeTimeSpreader(self.m_itemModel:GetCode())
end

function ItemSpread:_OnFlambeTimeChanged()
  self:UpdatePerSecond()
  self.event:Call(ItemSpreadEventType.UpdateFire)
end

function ItemSpread:HideFire()
  self.event:Call(ItemSpreadEventType.HideFire)
end

function ItemSpread:TryShowFire()
  self:_OnFlambeTimeChanged()
end

function ItemSpread:UpdatePerSecond()
  if self.m_startTimer == -1 then
    if self.m_state == ItemSpreadState.Initializing or self.m_state == ItemSpreadState.Opening or self.m_state == ItemSpreadState.Opened and self.m_storageRestNumber < self.m_storageMaxNumber then
      self.m_startTimer = GM.GameModel:GetServerTime()
      self:_Save()
      self.event:Call(ItemSpreadEventType.StateChanged)
      if self:GetTimerAmount() == 1 then
        self:UpdatePerSecond()
      end
    end
    return
  end
  local itemAccelerateTime = self.m_itemModel:GetComponent(ItemAccelerateTime)
  if itemAccelerateTime ~= nil and self:CanAccelerate() and itemAccelerateTime:IsAccelerated() then
    local endTime = math.min(itemAccelerateTime:GetTime(), GM.GameModel:GetServerTime())
    self.m_startTimer = self.m_startTimer + itemAccelerateTime:GetLastUpdateTime() - endTime
    self:_Save()
  end
  if self:GetTimerAmount() == 1 or self:IsFlambeTime() then
    self:_UpdateRecoveryState()
    self:_Save()
  end
  self.event:Call(ItemSpreadEventType.StateChanged)
end

function ItemSpread:CanAccelerate()
  return self.m_startTimer ~= -1
end

function ItemSpread:ShowAcceleratedCountDownAnim()
  local itemAccelerateTime = self.m_itemModel:GetComponent(ItemAccelerateTime)
  return itemAccelerateTime ~= nil and self:CanAccelerate() and itemAccelerateTime:IsAccelerated()
end

function ItemSpread:ShowCountDown(getRealState)
  if self.m_bSpeeding and getRealState ~= true then
    return true
  end
  local notShow = self:GetStorageRestNumber() > 0 and self:GetState() == ItemSpreadState.Opened or self:_ShouldDispose() or self:GetState() == ItemSpreadState.OpenFinish or not self:IsRecyclable()
  return self.m_startTimer ~= -1 and not notShow
end

function ItemSpread:CanTutorialCD()
  return self.m_state == ItemSpreadState.Opened and self:ShowCountDown()
end

function ItemSpread:SetTutorialCD()
  self.m_bInTutorial = true
  EventDispatcher.DispatchEvent(EEventType.ItemTutorial, {
    item = self.m_itemModel
  })
end

function ItemSpread:_UpdateRecoveryState()
  if self.m_state == ItemSpreadState.Initializing or self.m_state == ItemSpreadState.Opening then
    self.m_startTimer = -1
    self.m_state = ItemSpreadState.Opened
    EventDispatcher.DispatchEvent(EEventType.ItemOpened, {
      item = self.m_itemModel
    })
    self.m_itemModel:GetBoardModel():UpdateOpeningItem()
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxOpenBox)
    return
  end
  if self.m_storageRestNumber < self.m_storageMaxNumber then
    self.m_storageRestNumber = self.m_storageRestNumber + self.m_storageRecoverNumber
    if self.m_storageRestNumber >= self.m_storageMaxNumber then
      self.m_storageRestNumber = self.m_storageMaxNumber
      self.m_startTimer = -1
    else
      self.m_startTimer = self.m_startTimer + self.m_storageRecoverDuration
      self:UpdatePerSecond()
    end
    EventDispatcher.DispatchEvent(EEventType.UpdateStorage, {
      item = self.m_itemModel
    })
    return
  end
  self.m_startTimer = -1
  Log.Error("状态错误，无法恢复！")
end

function ItemSpread:OnTap()
  if self.m_state == ItemSpreadState.OpenFinish then
    return
  end
  local spreadCount = 1
  if GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.TestSuperTap, 0) == 1 then
    spreadCount = 10
  end
  for i = 1, spreadCount do
    local canContinue = self:_TrySpread()
    if not canContinue then
      break
    end
  end
  if not self:IsDisposable() and self:ShowCountDown() and self:GetStorageRestNumber() == 0 then
    if GM.TutorialModel:IsTutorialFinished(ETutorialId.PDCD1) and GM.TutorialModel:IsTutorialFinished(ETutorialId.PDCD2) then
      local restDuration = self:GetTimerDuration() + self:GetStartTimer() - GM.GameModel:GetServerTime()
      if 1200 <= restDuration then
        GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.ItemCoolDown)
      end
    end
    GM.BundleManager:OnItemRecharge(self.m_itemModel)
  end
end

function ItemSpread:OnOpen()
  self.m_state = ItemSpreadState.Opening
  self:UpdatePerSecond()
  EventDispatcher.DispatchEvent(EEventType.ItemOpening)
  self.m_itemModel:GetBoardModel():UpdateOpeningItem()
  GM.BIManager:LogAction(EBIType.OpenItem, self.m_itemModel:GetCode())
end

function ItemSpread:OnSpeedUp(isFree)
  if self.m_bInTutorial then
    isFree = true
    self.m_bInTutorial = nil
  end
  self:_SpeedUp(isFree)
  EventDispatcher.DispatchEvent(EEventType.OnSpeedUp)
end

function ItemSpread:_SpeedUp(isFree)
  if not isFree then
    local cost = self:GetSpeedUpCost()
    local gemNumber = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
    if cost > gemNumber then
      local boardModel = self.m_itemModel:GetBoardModel()
      boardModel.event:Call(BoardEventType.LackGem, {
        LackNumber = cost - gemNumber
      })
      return
    end
    GM.PropertyDataManager:Consume(EPropertyType.Gem, cost, EBIType.SpeedUp, false, self.m_itemModel:GetCode())
  end
  if self.m_startTimer ~= -1 then
    local remainTime = (1 - self:GetTimerAmount()) * self:GetTimerDuration()
    remainTime = math.ceil(remainTime)
    self:_SkipTime(remainTime, isFree and "ad" or "gem")
  end
end

function ItemSpread:CanSpeederEffect()
  return self.m_state == ItemSpreadState.Opened and self.m_startTimer ~= -1 and not self:IsDisposable()
end

function ItemSpread:SkipTimeFromSpeeder(duration, source)
  if self:CanSpeederEffect() then
    self.m_bSpeeding = true
    self:_SkipTime(duration, source)
  end
end

function ItemSpread:PlaySkipTimeAnim()
  self.event:Call(ItemSpreadEventType.PlaySkipTimeAnim)
  GM.UIManager:SetEventLock(true)
  local dt = self:ShowCountDown(true) and 1.2 or 1.6
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    self.m_bSpeeding = nil
    self:UpdatePerSecond()
    self.event:Call(ItemSpreadEventType.StateChanged)
  end, dt, self)
end

function ItemSpread:_SkipTime(duration, source)
  if self.m_startTimer ~= -1 then
    Log.Assert(source ~= nil, "ItemSpread:_SkipTime no source " .. self.m_itemModel:GetType())
    if duration < 0 then
      duration = self.m_storageMaxNumber / self.m_storageRecoverNumber * self.m_storageRecoverDuration
      duration = math.ceil(duration)
    end
    self.m_startTimer = self.m_startTimer - duration
    self:_Save()
    self:UpdatePerSecond()
    GM.BIManager:LogAction(EBIType.SpeedUp, GM.BIManager:TableToString({
      t = self.m_itemModel:GetType(),
      d = duration,
      s = source
    }))
  end
end

function ItemSpread:IsAutoSpread()
  return self.m_auto ~= nil and self.m_auto >= 1
end

function ItemSpread:IsAutoSpreadFullBoard()
  return self.m_auto == 2
end

function ItemSpread:GetOpenDuration()
  return self.m_openDuration
end

function ItemSpread:CostEnergy()
  local forcedCostEnergy = TutorialBoardHelper.GetForcedCostEnergy()
  if forcedCostEnergy ~= nil then
    return forcedCostEnergy
  end
  return self.m_costEnergy == 1
end

function ItemSpread:GetCodeWeightPairs()
  return self.m_codeWeightPairs
end

function ItemSpread:GetStorageRestNumber(includeAdded)
  if includeAdded == false then
    return self.m_storageRestNumber
  end
  return self.m_storageRestNumber + self.m_spreadAddItem
end

function ItemSpread:GetSpreadCount()
  return self.m_spreadCount
end

function ItemSpread:GetState()
  return self.m_state
end

function ItemSpread:GetStartTimer()
  return self.m_startTimer
end

function ItemSpread:GetRechargeCount()
  return self.m_storageRecoverNumber
end

function ItemSpread:GetRechargeTime()
  return self.m_storageRecoverDuration
end

function ItemSpread:IsFull()
  return self.m_storageRestNumber >= self.m_storageMaxNumber
end

function ItemSpread:IsDisposable()
  return self.m_bDisposable
end

function ItemSpread:_ShouldDispose()
  return self:IsDisposable() and self.m_spreadCount >= self.m_dropsTotal
end

function ItemSpread:IsRecyclable()
  return self.m_storageRecoverDuration and self.m_storageRecoverDuration >= 0
end

function ItemSpread:IsSpreadFinish()
  if self:IsDisposable() then
    return self:_ShouldDispose()
  end
  if not self:IsRecyclable() then
    return self:GetState() == ItemSpreadState.OpenFinish
  end
end

function ItemSpread:GetNotSpreadItems()
  local items = {}
  local remainNum = 0
  if Table.ListContain(self.m_itemConfig.Category, EItemCategory.NoRecycleableSpreadItem) and self.m_state ~= ItemSpreadState.OpenFinish then
    remainNum = self.m_storageRestNumber
  elseif Table.ListContain(self.m_itemConfig.Category, EItemCategory.DisposableGenerator) and self.m_dropsTotal ~= nil then
    remainNum = self.m_dropsTotal - self.m_spreadCount
  end
  local code
  for i = 1, remainNum do
    code = self:_GenerateItemCode()
    if code ~= nil then
      table.insert(items, code)
    end
  end
  return items
end

function ItemSpread:GetNotSpreadRewards()
  local items = self:GetNotSpreadItems()
  local arrRewards = {}
  if not Table.IsEmpty(items) then
    local rewardType, itemConfig
    for _, itemCode in ipairs(items) do
      rewardType = nil
      if StringUtil.StartWith(itemCode, ItemCodePrefix.RewardBubble) then
        rewardType = string.sub(itemCode, 4)
      else
        itemConfig = GM.ItemDataModel:GetModelConfig(itemCode)
        if not Table.IsEmpty(itemConfig.CollectRewards) then
          Table.ListAppend(arrRewards, itemConfig.CollectRewards)
        else
          rewardType = itemCode
        end
      end
      if rewardType ~= nil then
        table.insert(arrRewards, {
          [PROPERTY_TYPE] = rewardType,
          [PROPERTY_COUNT] = 1
        })
      end
    end
  end
  return arrRewards
end

function ItemSpread:GetTimerAmount()
  if self.m_startTimer == -1 then
    return 1
  end
  local elapsedTime = GM.GameModel:GetServerTime() - self.m_startTimer
  local amount = self:_GetTimerAmount(elapsedTime)
  if self.m_bInTutorial then
    return math.min(0.9, amount)
  end
  return amount
end

function ItemSpread:GetNextTimerAmount()
  if self.m_startTimer == -1 then
    return 1
  end
  local nextElapsedTime = GM.GameModel:GetServerTime() - self.m_startTimer + 1
  local amount = self:_GetTimerAmount(nextElapsedTime)
  if self.m_bInTutorial then
    return math.min(0.9, amount)
  end
  return amount
end

function ItemSpread:_GetTimerAmount(elapsedTime)
  local duration = self:GetTimerDuration()
  if duration == nil then
    local data = {
      init = tostring(self.m_initDuration),
      open = tostring(self.m_openDuration),
      storageRestNumber = self.m_storageRestNumber,
      state = self.m_state,
      type = self.m_itemModel:GetType()
    }
    Log.Error("duration is nil:" .. json.encode(data))
    self.m_startTimer = -1
    return 1
  end
  if duration == 0 then
    return 1
  end
  return math.min(elapsedTime / duration, 1)
end

function ItemSpread:GetTimerDuration()
  if self.m_state == ItemSpreadState.Initializing then
    return self.m_initDuration
  elseif self.m_state == ItemSpreadState.Opening then
    return self.m_openDuration
  elseif self.m_state == ItemSpreadState.Opened then
    return self.m_storageRecoverDuration
  else
    Log.Assert(false, "ItemSpread:GetTimerDuration unknown state " .. tostring(self.m_state))
    return 0
  end
end

function ItemSpread:GetSpeedUpCost()
  if self.m_bInTutorial then
    return 0
  end
  local speedUpCost = self.m_speedUpMaxCost
  if speedUpCost == 0 then
    return 0
  end
  local timerAmount = self:GetTimerAmount()
  speedUpCost = (1 - timerAmount) * speedUpCost
  speedUpCost = math.ceil(speedUpCost)
  return math.max(speedUpCost, 1)
end

function ItemSpread:IsChestUsedOnce()
  return self.m_openDuration ~= nil and self.m_spreadCount > 0
end

function ItemSpread:_UpdateStorage()
  self.m_spreadCount = self.m_spreadCount + 1
  if self.m_spreadAddItem > 0 then
    self.m_spreadAddItem = self.m_spreadAddItem - 1
    self.event:Call(ItemSpreadEventType.StateChanged)
    self:_Save()
    EventDispatcher.DispatchEvent(EEventType.UpdateStorage, {
      item = self.m_itemModel
    })
    return
  end
  self.m_storageRestNumber = self.m_storageRestNumber - 1
  self:UpdatePerSecond()
  EventDispatcher.DispatchEvent(EEventType.UpdateStorage, {
    item = self.m_itemModel
  })
  self.event:Call(ItemSpreadEventType.StateChanged)
  self:_Save()
  if self.m_state == ItemSpreadState.Opened and self:ShowCountDown() then
    GM.BIManager:LogAction(EBIType.ItemCD, GM.BIManager:TableToString({
      i = self.m_itemModel and self.m_itemModel:GetType() or nil,
      cd = self.m_storageRecoverDuration
    }))
  end
end

function ItemSpread:_GenerateItemCode(energyBoost)
  local code = GM.TutorialModel:GetForceSpreadItemCode()
  if code ~= nil then
    return code
  end
  if self.m_codeNextTime then
    local code = self.m_codeNextTime
    self.m_codeNextTime = nil
    self:_LogNextCode()
    return code
  end
  if self.m_weightType == ItemSpreadWeightType.Fixed then
    local firstPair = self.m_codeWeightPairs[1]
    code = firstPair.Code
    firstPair.Weight = firstPair.Weight - 1
    if firstPair.Weight == 0 then
      table.remove(self.m_codeWeightPairs, 1)
      if #self.m_codeWeightPairs == 0 then
        self:_GenNextSpreadItems()
      end
    end
    self:_Save()
    return self:_GetUpgradedItemCode(code, energyBoost)
  end
  code = Table.ListWeightSelectOne(self.m_codeWeightPairs).Code
  for index, item in ipairs(self.m_codeWeightPairs) do
    if code == item.Code then
      item.Weight = item.Weight - 1
      if item.Weight == 0 then
        table.remove(self.m_codeWeightPairs, index)
        if #self.m_codeWeightPairs == 0 then
          self:_GenNextSpreadItems()
        end
      end
      self:_Save()
      break
    end
  end
  return self:_GetUpgradedItemCode(code, energyBoost)
end

function ItemSpread:_GetUpgradedItemCode(code, energyBoost)
  if not energyBoost then
    return ItemSpreadHelper.ReplaceSpreadItem(self, code)
  end
  if not GM.EnergyBoostModel:CanEnergyBoost(self.m_itemModel:GetCode()) then
    return code
  end
  local itemDataModel = GM.ItemDataModel
  local boardModel = self.m_itemModel:GetBoardModel()
  local itemConfig = itemDataModel:IsItemExist(code) and itemDataModel:GetModelConfig(code)
  if not itemConfig or not itemConfig.MergedType then
    return code
  end
  local directResult, indirectResult, filledResult, codeCountMap = boardModel:GetUnfilledOrderRequirementsSeparately(false)
  local lackCount = (directResult[code] or 0) + (indirectResult[code] or 0)
  if 0 < lackCount then
    self.m_codeNextTime = 1 < lackCount and code or nil
    if self.m_codeNextTime then
      self:_LogNextCode()
    end
    return code
  end
  if itemDataModel:GetChainLevel(code) == 1 and boardModel:GetBoardItemCountByType(code) == 1 and (0 < lackCount or (filledResult[code] or 0) <= 0) then
    Log.Info("[双倍体力优化] " .. self.m_itemModel:GetCode() .. " " .. tostring(self.m_itemModel:GetPosition()) .. " 因为场上只有一个 " .. code)
    return code
  end
  local doubleCode = itemConfig.MergedType
  return doubleCode, code, 1
end

function ItemSpread:_LogNextCode()
  local info = "[双倍体力优化] " .. self.m_itemModel:GetCode() .. " " .. tostring(self.m_itemModel:GetPosition())
  if self.m_codeNextTime then
    Log.Info(info .. " 下次必喷 " .. self.m_codeNextTime)
  else
    Log.Info(info .. " 必喷结束")
  end
end

function ItemSpread:_TrySpread(isAuto)
  local canSpread, position, replace, energyBoost, costNum = self:_CanSpread(isAuto)
  if not canSpread then
    return false
  end
  local needConfirm, desc
  if not isAuto and not IsAutoRun() then
    needConfirm, desc = ItemSpreadHelper.NeedConfirmBeforeTransform(self, self.m_itemConfig)
  end
  if needConfirm then
    GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "transformtip_title", desc, "btn_common_continue", "common_button_cancel", function(tbWindow)
      tbWindow:Close()
      local canSpread, position, replace, energyBoost, costNum = self:_CanSpread(isAuto)
      if not canSpread then
        return
      end
      self:_RealSpread(position, replace, energyBoost, costNum)
    end, nil, false)
    return false
  end
  return self:_RealSpread(position, replace, energyBoost, costNum)
end

function ItemSpread:_CanSpread(isAuto)
  if self.m_state == ItemSpreadState.Closed then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.ItemClosed)
    return false
  end
  if self.m_state == ItemSpreadState.Opening then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.ItemOpening)
    return false
  end
  if self.m_state == ItemSpreadState.Initializing or self:GetStorageRestNumber() == 0 then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.ItemRecharging)
    return false
  end
  local boardModel = self.m_itemModel:GetBoardModel()
  if not isAuto and self:IsAutoSpread() then
    local autoSpreadPosition = self:_GetAutoSpreadPosition(boardModel)
    if autoSpreadPosition ~= nil then
      return false
    end
  end
  local position
  local replace = false
  if self:IsDisposable() and self.m_spreadCount >= self.m_dropsTotal - 1 and self.m_itemModel:GetComponent(ItemTransform) == nil and self.m_itemConfig.DropOnSpot == 1 then
    position = self.m_itemModel:GetPosition()
    replace = true
  elseif isAuto then
    position = self:_GetAutoSpreadPosition(boardModel)
  else
    position = boardModel:FindEmptyPositionInSpreadOrder(self.m_itemModel:GetPosition())
  end
  if position == nil then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.BoardFull)
    if not isAuto then
      PlatformInterface.Vibrate(EVibrationType.Heavy)
    end
    return false
  end
  local energyBoost = false
  local costNum = 0
  if not isAuto and self:CostEnergy() then
    costNum, energyBoost = ItemSpread.GetCostEnergyNum(self.m_itemModel:GetType())
    if not GM.EnergyModel:HasEnoughEnergy(EnergyModel.PropertyType2EnergyType(EPropertyType.Energy), costNum) then
      boardModel.event:Call(BoardEventType.LackSpreadEnergy, {
        type = EPropertyType.Energy
      })
      return false
    end
  end
  return true, position, replace, energyBoost, costNum
end

function ItemSpread:_RealSpread(position, replace, energyBoost, costNum)
  local newItemCode, originalCode, boostLevelSpan = self:_GenerateItemCode(energyBoost)
  local boostEnergySpared = 0
  if costNum ~= 0 then
    local realCostNum = 1
    if originalCode and boostLevelSpan and originalCode ~= newItemCode then
      realCostNum = math.modf(2 ^ boostLevelSpan)
    end
    if realCostNum ~= costNum then
      boostEnergySpared = costNum - realCostNum
      costNum = realCostNum
      if realCostNum == 1 then
        boostLevelSpan = nil
      end
      Log.Assert(0 <= boostEnergySpared, "不应该发生这种情况")
      if 0 < boostEnergySpared then
        local message = {
          property = {
            [PROPERTY_TYPE] = EPropertyType.Energy,
            [PROPERTY_COUNT] = boostEnergySpared
          }
        }
        GM.PropertyDataManager:PlayPropertyDecreaseAnimation(message)
      end
    end
    GM.PropertyDataManager:Consume(EPropertyType.Energy, costNum, EBIType.SpreadItem, false, self.m_itemModel:GetCode())
    EnergyModel.OnEnergyConsumed(self.m_itemModel, costNum, self:GetGameMode())
    EventDispatcher.DispatchEvent(EEventType.ProducerItemClick, {
      pos = self.m_itemModel:GetPosition()
    })
  end
  local boardModel = self.m_itemModel:GetBoardModel()
  if replace then
    local transformAudioName = self.m_itemModel:GetTransformAudio()
    if transformAudioName ~= nil then
      GM.AudioModel:PlayEffect(transformAudioName)
    end
    local newItem = boardModel:ReplaceItem(self.m_itemModel, newItemCode, self:_GetNewItemCost(costNum))
    local eventInfo = {
      Source = self.m_itemModel,
      New = newItem
    }
    boardModel.event:Call(BoardEventType.TransformItem, eventInfo)
  else
    boardModel:SpreadItem(self.m_itemModel, position, newItemCode, true, self:_GetNewItemCost(costNum), boostLevelSpan, boostEnergySpared)
    self:_UpdateStorage()
  end
  PlatformInterface.Vibrate(EVibrationType.Light)
  if self:_ShouldDispose() then
    if self.m_itemModel:GetComponent(ItemTransform) ~= nil then
      self.m_itemModel:GetComponent(ItemTransform):Transform(self:_GetNewItemCost(0), true)
    elseif not replace then
      boardModel:RemoveItem(self.m_itemModel)
      boardModel.event:Call(BoardEventType.CollapseItem, {
        Source = self.m_itemModel
      })
    end
    return false
  elseif self.m_storageRestNumber == 0 and not self:IsRecyclable() then
    self.m_state = ItemSpreadState.OpenFinish
    self.m_startTimer = -1
    self:_Save()
    self.event:Call(ItemSpreadEventType.StateChanged)
    return false
  end
  return not replace
end

function ItemSpread:_GetNewItemCost(nCostEnergy)
  local cost
  if self:IsDisposable() then
    local itemCount = self.m_dropsTotal
    if self.m_itemModel:GetComponent(ItemTransform) ~= nil then
      itemCount = itemCount + 1
    end
    cost = ItemModelHelper.SplitCost(self.m_itemModel, itemCount)
    cost.costEnergyCurDay = cost.costEnergyCurDay + nCostEnergy
  else
    cost = {costEnergyCurDay = nCostEnergy}
  end
  return cost
end

function ItemSpread:_GetAutoSpreadPosition(boardModel)
  if self:IsAutoSpreadFullBoard() then
    return boardModel:FindEmptyPositionInSpreadOrder(self.m_itemModel:GetPosition())
  else
    return boardModel:FindEmptyPositionInCircleOrder(self.m_itemModel:GetPosition())
  end
end

function ItemSpread:_NotifySpreadFailed(isAuto, reason)
  if isAuto then
    return
  end
  local boardModel = self.m_itemModel:GetBoardModel()
  boardModel.event:Call(BoardEventType.SpreadFailed, {
    Item = self.m_itemModel,
    Reason = reason
  })
  EventDispatcher.DispatchEvent(EEventType.ItemSpreadFailed, {
    reason = reason,
    gameMode = self:GetGameMode()
  })
end

function ItemSpread.GetCostEnergyNum(itemType)
  local nCostNum = 1
  local bEnergyBoost = false
  if itemType == nil then
    if GM.EnergyBoostModel:IsEnergyBoostModeOn() then
      nCostNum = GM.EnergyBoostModel:GetEnergyBoostCostNum()
      bEnergyBoost = true
    end
  elseif GM.EnergyBoostModel:CanEnergyBoost(itemType) then
    nCostNum = GM.EnergyBoostModel:GetEnergyBoostCostNum()
    bEnergyBoost = true
  end
  return nCostNum, bEnergyBoost
end

function ItemSpread:GetGeneratedItems()
  return self.m_itemConfig.GeneratedItems
end

function ItemSpread:AddAddItemBoost(count)
  self.m_spreadAddItem = self.m_spreadAddItem + count
  self:_Save()
  self.event:Call(ItemSpreadEventType.AddItemBoost)
end

function ItemSpread:GetAddItemCount()
  return self.m_spreadAddItem
end

function ItemSpread:InheritFromMergeSource(item1, item2)
  local spread1 = item1:GetComponent(ItemSpread)
  local spread2 = item2:GetComponent(ItemSpread)
  if not spread1 and not spread2 then
    return
  end
  self.m_spreadAddItem = (spread1 and spread1:GetAddItemCount() or 0) + (spread2 and spread2:GetAddItemCount() or 0)
  local number = (spread1 and spread1.m_storageRestNumber or 0) + (spread2 and spread2.m_storageRestNumber or 0)
  self.m_storageRestNumber = self.m_storageRestNumber + number
  if self.m_storageRestNumber >= self.m_storageMaxNumber and self.m_state == ItemSpreadState.Opened and self.m_startTimer ~= -1 then
    self.m_startTimer = -1
  end
  local spreadItems = {}
  if spread1 and spread1.m_bCanInherit then
    Table.ListAppend(spreadItems, spread1.m_codeWeightPairs)
  end
  if spread2 and spread2.m_bCanInherit then
    Table.ListAppend(spreadItems, spread2.m_codeWeightPairs)
  end
  if next(spreadItems) ~= nil then
    if self.m_bCanInherit and self.m_weightType == ItemSpreadWeightType.Fixed then
      Table.ListAppend(spreadItems, self.m_codeWeightPairs)
      self.m_codeWeightPairs = spreadItems
    else
      self.m_codeWeightPairs = spreadItems
      self.m_weightType = ItemSpreadWeightType.Fixed
    end
    self.m_bCanInherit = true
  end
  self:_Save()
end

function ItemSpread:GetCurItemBoxSpreadChain()
  return self.m_spreadItemBoxChain
end

function ItemSpread:SetItemBoxSpreadChain(chainArr)
  self.m_spreadItemBoxChain = chainArr
  self:_Save()
end
