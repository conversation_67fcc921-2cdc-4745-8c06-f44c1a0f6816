local Step = {
  WindowDialog = "1",
  ClickToggle = "2",
  CloseWindow = "3",
  HighlightPds = "4",
  HighlightEntry = "5"
}
local EStep2TextKey = {
  [Step.WindowDialog] = "double_energy_t1_1",
  [Step.ClickToggle] = "double_energy_t1_2",
  [Step.HighlightPds] = "double_energy_t2_1",
  [Step.HighlightEntry] = "double_energy_t3_1"
}
local EStep2TextAnchorPercent = {
  [Step.WindowDialog] = 82,
  [Step.ClickToggle] = 50,
  [Step.HighlightPds] = 15,
  [Step.HighlightEntry] = 35
}
local Executer = setmetatable({AddToAutoPopup = false}, TutorialExecuter)
Executer.__index = Executer

function Executer:OnStart()
  EventDispatcher.AddListener(EEventType.OnViewWillClose, self, self._OnViewWillClose)
  EventDispatcher.AddListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddListener(EEventType.EnergyBoostModeChanged, self, self._OnEnergyBoostModeChanged)
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self._OnChangeGameModeFinished)
end

function Executer:_Finish()
  self:Finish(self.m_gesture, self.m_arrow)
  if self.m_boostHighlighted then
    TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.EnergyBoost)
    self.m_boostHighlighted = false
  end
  if self.m_mainBoardHighlighted then
    TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.MainBoard)
    self.m_mainBoardHighlighted = false
  end
end

function Executer:_OnChangeGameModeFinished()
  if not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and self:IsInStrongTutorial() and GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    self:_Finish(self.m_gesture)
  elseif self.m_strOngoingDatas == Step.CloseWindow and GM.SceneManager:GetGameMode() == EGameMode.Board then
    self:ExecuteStep4()
  end
end

function Executer:_OnOpenView(message)
  if StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and not GM.TutorialModel:HasAnyStrongTutorialOngoing() and message.name == UIPrefabConfigName.EnergyBoostSettingWindow then
    self:ExecuteStep1()
  end
end

function Executer:ExecuteStep1()
  self.m_strOngoingDatas = Step.WindowDialog
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  self:SetStrongTutorial(true)
  self.m_model:SetTutorialFinished(self:GetTutorialId())
  TutorialHelper.WholeMask(function()
    self:ExecuteStep2()
  end)
  TutorialHelper.SetMaskAlphaOnce(0)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

function Executer:ExecuteStep2()
  self.m_strOngoingDatas = Step.ClickToggle
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  local window = GM.UIManager:GetOpenedTopView()
  if not window or window.name ~= UIPrefabConfigName.EnergyBoostSettingWindow then
    self:_Finish()
    return
  end
  if GM.EnergyBoostModel:IsEnergyBoostUserOn() then
    self:ExecuteStep3()
    return
  end
  local trans = window:GetToggleRectTrans()
  TutorialHelper.UpdateMask(trans.position, trans.sizeDelta, nil, true)
  self.m_gesture = TutorialHelper.TapOnCustomRectTrans(trans)
  self.m_gesture:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

function Executer:ExecuteStep3()
  self.m_strOngoingDatas = Step.CloseWindow
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.HideDialog()
  TutorialHelper.HideTutorialLayer(self.m_gesture)
  self.m_gesture = nil
  local window = GM.UIManager:GetOpenedTopView()
  if not window or window.name ~= UIPrefabConfigName.EnergyBoostSettingWindow then
    self:_Finish()
    return
  end
  local trans = window:GetCloseBtnTrans()
  TutorialHelper.UpdateMask(trans.position, trans.sizeDelta, nil, true)
  self.m_arrow = TutorialHelper.AddArrow2CustomRectTrans(trans, 135)
  self.m_arrow:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
end

function Executer:_OnViewWillClose(message)
  if self.m_strOngoingDatas == Step.CloseWindow and message.name == UIPrefabConfigName.EnergyBoostSettingWindow then
    TutorialHelper.HideDialog()
    TutorialHelper.HideTutorialLayer(self.m_gesture, self.m_arrow)
    self.m_gesture = nil
    if GM.SceneManager:GetGameMode() == EGameMode.Board then
      self:ExecuteStep4()
    elseif GM.SceneManager:GetGameMode() == EGameMode.Main then
      TutorialHelper.HighlightHudButton(ESceneViewHudButtonKey.MainBoard, false)
      self.m_mainBoardHighlighted = true
      TutorialHelper.WholeMask()
      self.m_arrow = TutorialHelper.AddArrow2HudButton(ESceneViewHudButtonKey.MainBoard, 0)
      self.m_arrow:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
    end
  end
end

function Executer:_OnEnergyBoostModeChanged()
  if self.m_strOngoingDatas == Step.ClickToggle and GM.UIManager:GetOpenedTopView() and GM.UIManager:GetOpenedTopView().name == UIPrefabConfigName.EnergyBoostSettingWindow and GM.EnergyBoostModel:IsEnergyBoostConfigOn() and GM.EnergyBoostModel:IsEnergyBoostUserOn() then
    self:ExecuteStep3()
  elseif not StringUtil.IsNilOrEmpty(self.m_strOngoingDatas) and not GM.EnergyBoostModel:IsEnergyBoostConfigOn() then
    self:_Finish()
  end
end

function Executer:ExecuteStep4()
  self.m_strOngoingDatas = Step.HighlightPds
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.HideTutorialLayer(self.m_gesture, self.m_arrow)
  self.m_arrow = nil
  if self.m_mainBoardHighlighted then
    TutorialHelper.DehighlightHudButton(ESceneViewHudButtonKey.MainBoard)
    self.m_mainBoardHighlighted = false
  end
  TutorialHelper.WholeMask(function()
    self:ExecuteStep5()
  end)
  local energyBoostModel = GM.EnergyBoostModel
  local itemDatasArr = {}
  for item, _ in pairs(GM.MainBoardModel:GetAllBoardItems(false)) do
    if energyBoostModel:CanEnergyBoost(item:GetCode()) then
      table.insert(itemDatasArr, item)
    end
  end
  TutorialHelper.HighlightItems(itemDatasArr)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

function Executer:ExecuteStep5()
  self.m_strOngoingDatas = Step.HighlightEntry
  self:LogTutorialStepFinish(self.m_strOngoingDatas)
  TutorialHelper.UnHighlightItems()
  TutorialHelper.HighlightHudButton(ESceneViewHudButtonKey.EnergyBoost, true)
  self.m_boostHighlighted = true
  TutorialHelper.WholeMask(function()
    self:_Finish()
  end)
  self.m_arrow = TutorialHelper.AddArrow2HudButton(ESceneViewHudButtonKey.EnergyBoost, 180)
  self.m_arrow:UpdateSortingOrder(ESpecialViewSortingOrder.TutorialHighlight + 1)
  TutorialHelper.ShowDialog(GM.GameTextModel:GetText(EStep2TextKey[self.m_strOngoingDatas]), EStep2TextAnchorPercent[self.m_strOngoingDatas])
end

return function(tutorialId, strDatas)
  return TutorialExecuter.CreateExecuter(Executer, tutorialId, strDatas)
end
