ProgressActivityMainWindow = setmetatable({}, ProgressActivityBaseWindow)
ProgressActivityMainWindow.__index = ProgressActivityMainWindow

function ProgressActivityMainWindow:Init(eActivityType, bAutoPop)
  ProgressActivityBaseWindow.Init(self, eActivityType, bAutoPop)
  self.m_SliderLua:Init(self.m_model)
  self.m_nextStateTime = self.m_model:GetNextStateTime()
  self:UpdatePerSecond()
  self:UpdateContent()
  self.m_model:SetWindowOpened()
  local descText = self.m_definition.WindowDescKey
  local iconKey = self.m_definition.ActivityTokenIconName
  local totalTokenNum = self.m_model:GetTotalRequire()
  self.m_descText.text = GM.GameTextModel:GetText(descText, iconKey, iconKey, totalTokenNum)
  self:_UpdateRewards()
  self:TryPlayProgressAnimation()
  GM.AudioModel:PlayEffectLoop(AudioFileConfigName.SfxProgressActivityCat, self)
  RewardApi.AddFilterRewardEventListener(self, self._UpdateRewards)
end

function ProgressActivityMainWindow:_UpdateRewards()
  local maxLevel = self.m_model:GetLevelCount()
  local finalReward = self.m_model:GetLevelReward(maxLevel)
  local arrRewards = RewardApi.FilterRewards({finalReward})
  if not Table.IsEmpty(arrRewards) then
    UIUtil.SetActive(self.m_finalPrizeItem.gameObject, true)
    self.m_finalPrizeItem:Init(arrRewards[1])
  else
    UIUtil.SetActive(self.m_finalPrizeItem.gameObject, false)
  end
end

function ProgressActivityMainWindow:OnDestroy()
  ProgressActivityBaseWindow.OnDestroy(self)
  EventDispatcher.RemoveTarget(self)
  GM.UIManager:RemoveAllEventLocks(self)
  GM.AudioModel:StopEffectLoop(self)
end

function ProgressActivityMainWindow:OnStateChangedEvent()
  self.m_bStateChanged = true
  if self.m_bPlayingAnimation then
    return
  end
  self:Close()
end

function ProgressActivityMainWindow:TryPlayProgressAnimation()
  if self.m_model:GetState() == ActivityState.Released then
    self:Close()
    return
  end
  if self.m_model:CanAcquireReward() then
    self.m_model:AcquireReward()
  end
  local lastLevel = self.m_model:GetLastDisplayLevel()
  local lastScore = self.m_model:GetLastDisplayScore()
  local nowLevel = self.m_model:GetLevel()
  local nowScore = self.m_model:GetActivityTokenNumber()
  if lastLevel == nowLevel and lastScore == nowScore then
    return
  end
  local levelConfig = self.m_model:GetLevelConfigs()[nowLevel]
  local finishAll = levelConfig == nil
  self:_PlayProgressAnimation(lastLevel, lastScore, nowLevel, nowScore, finishAll)
  self.m_model:SetLastDisplayLevel(nowLevel)
  self.m_model:SetLastDisplayScore(nowScore)
end

function ProgressActivityMainWindow:_PlayProgressAnimation(lastLevel, lastScore, nowLevel, nowScore, finishAll)
  if self.m_bClosed then
    return
  end
  self.m_SliderLua:UpdateContent(lastLevel, lastScore)
  if lastLevel == nowLevel then
    self.m_SliderLua:PlayProgressAnimation(nowScore, function()
      self.m_bPlayingAnimation = false
      if self.m_bStateChanged or self.m_model:GetState() == ActivityState.Ended or self.m_model:GetState() == ActivityState.Released then
        self:Close()
      end
    end)
  else
    GM.UIManager:SetEventLock(true, self)
    self.m_bPlayingAnimation = true
    local nEndScore = self.m_model:GetLevelRequire(lastLevel)
    self.m_SliderLua:PlayProgressAnimation(nEndScore, function()
      GM.UIManager:SetEventLock(false, self)
      local rewards = self.m_model:GetLevelReward(lastLevel)
      GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, {rewards}, nil, true, nil, function()
        local level = self.m_model:GetLevel()
        if self.m_model:GetLevelConfigs()[nowLevel] == nil and lastLevel + 1 == nowLevel or level < lastLevel or level < nowLevel or self.m_model:GetState() == ActivityState.Released then
          self:Close()
          local windowName = finishAll and self.m_definition.FinishSuccessWindowPrefabName or self.m_definition.FinishFailWindowPrefabName
          GM.UIManager:OpenView(windowName, self.m_activityType)
          EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
          return
        end
        self:_PlayProgressAnimation(lastLevel + 1, 0, nowLevel, nowScore)
      end)
    end)
  end
end

function ProgressActivityMainWindow:UpdateContent()
  self.m_SliderLua:UpdateContent()
  self.m_nCurLevel = self.m_model:GetLevel()
end

function ProgressActivityMainWindow:UpdatePerSecond()
  if self.m_model and self.m_model:GetState() == ActivityState.Ended then
    self.m_countdownText.text = GM.GameTextModel:GetText("countdown_finished")
  elseif self.m_nextStateTime ~= nil then
    local delta = math.max(0, self.m_nextStateTime - GM.GameModel:GetServerTime())
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function ProgressActivityMainWindow:GetSliderTrans()
  return self.m_SliderLua.transform
end

function ProgressActivityMainWindow:GetSliderBgTrans()
  return self.m_sliderBgTrans
end

function ProgressActivityMainWindow:GetFinalPrizeTrans()
  return self.m_finalPrizeTrans
end

function ProgressActivityMainWindow:OnGoClicked()
  self:Close()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    GM.SceneManager:ChangeGameMode(EGameMode.Board)
  end
end
