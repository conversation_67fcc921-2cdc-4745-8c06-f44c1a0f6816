TestAutoRunModel = {}
TestAutoRunModel.__index = TestAutoRunModel

function TestAutoRunModel:Init()
  self.autoRun = false
  self.finishCurrent = false
  self.ignoreBuild = false
  self.useInventory = true
  self.consumedEnergy = 0
  self.lastConsumedEnergy = 0
  self.stopConsumedEnergy = nil
  self.stopDay = nil
  
  function self.m_logCallback(message, stackTrace, type)
    self:_LogCallback(message, stackTrace, type)
  end
  
  EventDispatcher.AddListener(EEventType.PropertyConsumed, self, self._OnPropertyConsumed)
end

function TestAutoRunModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function TestAutoRunModel:_OnPropertyConsumed(message)
  if message.property and message.property[PROPERTY_TYPE] == EPropertyType.Energy then
    local count = message.property[PROPERTY_COUNT]
    self.consumedEnergy = self.consumedEnergy + count
    if self.stopConsumedEnergy and self.consumedEnergy >= self.stopConsumedEnergy then
      self:StopAutoRun()
      GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, "Tip", "已自动跑完" .. self.stopConsumedEnergy .. "体力", "OK")
    end
  end
end

function TestAutoRunModel:StartAutoRun()
  self:ResetInterval()
  GM.TutorialModel:TestFinishAllTutorials()
  TutorialHelper.GetTutorialLayer():HideAll()
  PlayerPrefs.SetInt(EPlayerPrefKey.TestSuperTap, 0)
  for _, order in pairs(GM.MainBoardModel:GetOrderModel():GetOrders()) do
    order:TryUnlockAllCookCmp()
  end
  GM.AudioModel:SetAudioMute(AudioType.Effect, true, true)
  GM.AudioModel:SetAudioMute(AudioType.Music, true, true)
  local win = GM.UIManager:GetOpenedTopViewByType(EViewType.Window)
  while win do
    win:Close()
    win = GM.UIManager:GetOpenedTopViewByType(EViewType.Window)
  end
  if GM.TimelineManager:IsPlayingTimeline() then
    GM.TimelineManager:_OnTimelineFinished()
  end
  self.autoRun = true
  self.lastConsumedEnergy = self.consumedEnergy or 0
  self.consumedEnergy = 0
  Application.logMessageReceived("+", self.m_logCallback)
  EventDispatcher.DispatchEvent(EEventType.RefreshPrompt)
  EventDispatcher.DispatchEvent(EEventType.OnInventoryCapacityUpdate)
end

function TestAutoRunModel:StopAutoRun()
  self.autoRun = false
  Application.logMessageReceived("-", self.m_logCallback)
  self.m_currentTask = nil
  CS.UnityEngine.Time.timeScale = 1
  DOTween.timeScale = 1
  MainBoardView.GetInstance():GetCamera().enabled = true
  GM.ModeViewController:GetChapterCam().enabled = true
  EventDispatcher.DispatchEvent(EEventType.OnInventoryCapacityUpdate)
end

function TestAutoRunModel:AutoRun()
  if self.m_currentTask ~= nil and self.m_currentTask == self:GetNextAutoRunTask() then
    return
  end
  self.m_currentTask = self:GetNextAutoRunTask()
  if self.m_currentTask == nil then
    GM.TaskManager:TryStartNewChapter()
    self.m_currentTask = self:GetNextAutoRunTask()
  end
  local eGameMode = GM.SceneManager:GetGameMode()
  if self.m_currentTask ~= nil and eGameMode == EGameMode.Main then
    if GM.TaskManager:CanFinishOngoingTask() then
      GM.TaskManager:GoFinishTask(self.m_currentTask)
    else
      GM.SceneManager:ChangeGameMode(EGameMode.Board)
    end
  elseif not eGameMode == EGameMode.Board then
    self:StopAutoRun()
  end
end

function TestAutoRunModel:GetNextAutoRunTask()
  local tasks = GM.TaskManager:GetOngoingTasks()
  return tasks and tasks[#tasks]
end

function TestAutoRunModel:GetRunningTask()
  return self.m_currentTask
end

function TestAutoRunModel:_LogCallback(message, stackTrace, type)
  if type == CS.UnityEngine.LogType.Log or type == CS.UnityEngine.LogType.Warning then
    return
  end
end

function TestAutoRunModel:AddInterval()
  if self.interval then
    self.interval = self.interval + 0.2
  end
end

function TestAutoRunModel:ResetInterval()
  self.interval = 0.2
end
