ServiceFloat = setmetatable({sortingOrder = 32000}, BaseView)
ServiceFloat.__index = ServiceFloat

function ServiceFloat:Awake()
  if GM.UIManager then
    self.gameObject.transform:SetParent(GM.UIManager:GetCanvasRoot(), false)
  end
  self:SetSortingOrder(self.sortingOrder)
  if ScreenFitter.NeedNotch() then
    local notchWidth = ScreenFitter.GetNotchHeight()
    self.gameObject.transform.sizeDelta = Vector2(0, -notchWidth * 2)
  end
  self.showDelay = 10
  Log.Info("ServiceFloat delay is " .. tostring(self.showDelay))
  self.m_passedTime = 0
  self.m_lastPercent = 0
  self.m_buttonImg.gameObject:SetActive(false)
end

function ServiceFloat:Update(dt)
  if self.m_buttonImg.gameObject.activeSelf then
    return
  end
  if not self.m_slider or self.m_slider:IsNull() or not self.m_slider.gameObject.activeSelf then
    return
  end
  local currentPercent = self.m_slider.value or 0
  if currentPercent ~= self.m_lastPercent then
    self.m_passedTime = 0
  end
  self.m_lastPercent = currentPercent
  self.m_passedTime = self.m_passedTime + dt
  if self.m_passedTime >= self.showDelay then
    self.m_buttonImg.gameObject:SetActive(true)
  end
end

function ServiceFloat:OpenCustomerCenter()
  GM.SDKHelper:OpenCustomerCenter("E002")
end
