local buttonType = typeof(CS.UnityEngine.UI.Button)
local rectTransformType = typeof(RectTransform)
local EHudAnimType = {Hide = true, Show = false}
local ESideBarNodeName = {
  Left = "m_sideBarLeft",
  Right = "m_sideBarRight",
  Bottom = "m_sideBarBottom",
  BottomLeft = "m_sideBarBottomLeft"
}
ESceneViewHudButtonKey = {
  Map = "Map",
  MainBoard = "MainBoard",
  MainBoardHighlight = "MainBoardHighlight",
  Task = "Task",
  Shop = "Shop",
  Setting = "Setting",
  Notice = "Notice",
  Inventory = "Inventory",
  Room = "Room",
  Discoveries = "Discoveries",
  Day = "Day",
  Coin = "Coin",
  Energy = "Energy",
  Gem = "Gem",
  SkipProp = "SkipProp",
  BakeOut = "BakeOut",
  BP1 = "BP1",
  BP2 = "BP2",
  BP3 = "BP3",
  BP4 = "BP4",
  BP5 = "BP5",
  BP6 = "BP6",
  BP7 = "BP7",
  CoinRace = "CoinRace",
  PkRace = "PkRace",
  ExtraBoard1 = "ExtraBoard1",
  ExtraBoard2 = "ExtraBoard2",
  ExtraBoard3 = "ExtraBoard3",
  ExtraBoard4 = "ExtraBoard4",
  ExtraBoard5 = "ExtraBoard5",
  ExtraBoard6 = "ExtraBoard6",
  ExtraBoard7 = "ExtraBoard7",
  Album1 = "Album1",
  TreasureDig = "TreasureDig",
  ProgressActivity1 = "ProgressActivity1",
  BlindChest1 = "BlindChest1",
  EnergyBoost = "EnergyBoost"
}
SceneViewHud = {
  MinScale = 0.74,
  TopHeight = 290,
  BottomHeight = 370,
  LeftWidth = 455,
  RightWidth = 455,
  Duration = 0.2
}
SceneViewHud.__index = SceneViewHud

function SceneViewHud:Awake()
  self.m_mapTweens = {}
  self.m_mapAnchorRoot = {}
  self.m_mapOriginScale = {}
  self.m_mapAnimType = {}
  self.m_mapHightlightHudButton = {}
  self.m_passActivityEntryCount = 0
  self.m_eventActivityEntryCount = 0
  self.m_spreeActivityEntryCount = 0
  local transform = self.gameObject.transform
  for k = 0, transform.childCount - 1 do
    local child = transform:GetChild(k)
    local name = child.name
    if EHudAnchorType[name] ~= nil then
      self.m_mapAnchorRoot[EHudAnchorType[name]] = child
      self.m_mapOriginScale[EHudAnchorType[name]] = child.localScale
    end
  end
  self.m_mapAnchorRootShowCount = {}
  for _, anchorType in pairs(EHudAnchorType) do
    self.m_mapAnchorRootShowCount[anchorType] = 1
  end
  self:FitScreenSize()
  self:UpdateViewHeight()
  EventDispatcher.AddListener(EEventType.UpdateSceneViewHud, self, self._OnUpdateSceneViewHud)
  EventDispatcher.AddListener(EEventType.HighlightHud, self, self.OnHighlightHud)
  local barTb
  for _, nodeName in pairs(ESideBarNodeName) do
    barTb = self[nodeName]
    barTb:Init(self)
  end
  self:_LoadAllActivityEntry()
end

function SceneViewHud:Init()
  local components = self.gameObject:GetComponentsInChildren(typeof(LayoutGroup), true)
  for i = 0, components.Length - 1 do
    LayoutRebuilder.ForceRebuildLayoutImmediate(components[i].transform)
  end
  for _, v in pairs(ESceneViewHudButtonKey) do
    local button = self:GetHudButton(v)
    if button ~= nil then
      button:SetFlyTargetPosition()
    end
  end
  self:_InitTweens()
  if GM.TutorialModel:IsTutorialOnGoing(ETutorialId.Timeline) or GM.TimelineLayer:IsPlayingVideo() then
    self:LateUpdate()
    self:Hide(nil, true)
  end
end

function SceneViewHud:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  for animType, mapTweens in pairs(self.m_mapTweens) do
    for _, tween in pairs(mapTweens) do
      tween:Kill()
    end
  end
  self.m_mapTweens = nil
end

function SceneViewHud:_LoadAllActivityEntry()
  local models = GM.ActivityManager:GetModels()
  local barTb, entryRootName
  for _, model in pairs(models) do
    if model.GetMapEntryShowConfig then
      local entryConfig = model:GetMapEntryShowConfig()
      entryRootName = GetEntryRootName(entryConfig.eEntryRootKey)
      barTb = nil
      for _, nodeName in pairs(ESideBarNodeName) do
        if self[nodeName] ~= nil and self[nodeName]:IsExistEntryNodeRect(entryRootName) then
          barTb = self[nodeName]
        end
      end
      if barTb ~= nil then
        barTb:ActivityEntryCodeCreator(entryConfig, {model, self})
      else
        Log.Error("[SceneViewHud] 活动入口挂载节点缺失, entryRootName: " .. entryRootName)
      end
    end
  end
  self.m_bUpdateFlyTargetPos = true
end

function SceneViewHud:_OnUpdateSceneViewHud(msg)
  if msg.Show then
    self:Show(msg.AnchorTypes, msg.IgnoreAnim)
  else
    self:Hide(msg.AnchorTypes, msg.IgnoreAnim)
  end
end

function SceneViewHud:GetBoardEntryPrefab()
  return self.m_boardEntryGo
end

function SceneViewHud:GetBoardEntryAnchor()
  return self.m_boardEntryAnchor
end

function SceneViewHud:UpdateViewHeight()
  local offset = ScreenFitter.GetSceneViewSizeOffset()
  self.TopHeight = self.TopHeight + offset.x
  self.BottomHeight = self.BottomHeight + offset.y
  self.LeftWidth = self.LeftWidth
  self.RightWidth = self.RightWidth
end

function SceneViewHud:FitScreenSize(customScale)
  self.m_curScale = 1
end

function SceneViewHud:_InitTweens()
  self:Hide()
  self:LateUpdate()
  self:Show()
end

function SceneViewHud:Hide(arrAnchorType, ignoreAnim)
  self:_Toggle(false, arrAnchorType, ignoreAnim)
end

function SceneViewHud:Show(arrAnchorType, ignoreAnim)
  self:_Toggle(true, arrAnchorType, ignoreAnim)
end

function SceneViewHud:_Toggle(toShow, arrAnchorType, ignoreAnim)
  local delta = toShow and 1 or -1
  if arrAnchorType == nil or arrAnchorType[1] == EHudAnchorType.All then
    for _, type in pairs(EHudAnchorType) do
      self.m_mapAnchorRootShowCount[type] = self.m_mapAnchorRootShowCount[type] + delta
    end
  else
    for _, type in ipairs(arrAnchorType) do
      self.m_mapAnchorRootShowCount[type] = self.m_mapAnchorRootShowCount[type] + delta
    end
  end
  self.m_bDirty = true
  self.m_bIgnoreAnim = ignoreAnim
end

function SceneViewHud:LateUpdate()
  if self.m_bDirty then
    local duration = self:_UpdateAnimation(self.m_bIgnoreAnim)
    DelayExecuteFuncInView(function()
      self:_OnHudAnchorAnimFinished()
    end, duration, self)
    self.m_bDirty = nil
    self.m_bIgnoreAnim = nil
  end
end

function SceneViewHud:_UpdateAnimation(ignoreAnim)
  local duration = 0
  for _, type in pairs(EHudAnchorType) do
    local animType = 0 >= self.m_mapAnchorRootShowCount[type] and EHudAnimType.Hide or EHudAnimType.Show
    duration = math.max(duration, self:_Animation(animType, type, ignoreAnim))
  end
  return duration
end

function SceneViewHud:_OnHudAnchorAnimFinished()
  if self.m_bUpdateFlyTargetPos then
    self.m_bUpdateFlyTargetPos = false
    if not Table.IsEmpty(self.m_mapAttachHudKey) then
      local hudButton
      for key, _ in pairs(self.m_mapAttachHudKey) do
        hudButton = self:_GetHudButton(key)
        if hudButton ~= nil then
          hudButton:SetFlyTargetPosition()
        end
      end
    end
  end
end

function SceneViewHud:GetHudButton(hudKey)
  return self:_GetHudButton(hudKey)
end

function SceneViewHud:_GetHudButton(hudKey, backup)
  return self[self:_GetHudButtonKey(hudKey, backup)]
end

function SceneViewHud:_GetHudButtonKey(hudKey, backup)
  return "m_hud" .. hudKey .. "Button" .. (backup and "Backup" or "")
end

function SceneViewHud:AttachHudButton(hudKey, btn)
  self.m_mapAttachHudKey = self.m_mapAttachHudKey or {}
  if self:_GetHudButton(hudKey, true) ~= nil then
    Log.Error("[SceneViewHud]duplicate attach button on " .. tostring(hudKey))
    return false
  end
  self[self:_GetHudButtonKey(hudKey, true)] = self[self:_GetHudButtonKey(hudKey)]
  self[self:_GetHudButtonKey(hudKey)] = btn
  self.m_mapAttachHudKey[hudKey] = true
  return true
end

function SceneViewHud:DetachHudButton(hudKey)
  self.m_mapAttachHudKey = self.m_mapAttachHudKey or {}
  if self:GetHudButton(hudKey) == nil then
    Log.Error("[SceneViewHud]detach a nil button on " .. tostring(hudKey))
  end
  self[self:_GetHudButtonKey(hudKey)] = self[self:_GetHudButtonKey(hudKey, true)]
  self[self:_GetHudButtonKey(hudKey, true)] = nil
  self.m_mapAttachHudKey[hudKey] = nil
  return true
end

function SceneViewHud:GetHighlightRoot()
  return self.m_highlightRoot
end

function SceneViewHud:OnHighlightHud(msg)
  if msg.highlight then
    self:_HighlightHudButton(msg.hudKey, msg.enableBtn)
  else
    self:_DehighlightHudButton(msg.hudKey)
  end
end

function SceneViewHud:_HighlightHudButton(hudKey, enableBtn)
  local hudButton = self:GetHudButton(hudKey)
  if hudButton == nil then
    return false
  end
  if self.m_mapHightlightHudButton[hudKey] ~= nil then
    return true
  end
  local buttonTransform = hudButton.transform
  local newPlaceHolder = GameObject(hudKey .. " placeHolder"):AddComponent(rectTransformType)
  newPlaceHolder.localPosition = buttonTransform.localPosition
  newPlaceHolder.sizeDelta = buttonTransform.sizeDelta
  newPlaceHolder.gameObject:SetActive(buttonTransform.gameObject.activeSelf)
  newPlaceHolder:SetParent(buttonTransform.parent, true)
  newPlaceHolder:SetSiblingIndex(buttonTransform:GetSiblingIndex())
  self.m_mapHightlightHudButton[hudKey] = newPlaceHolder
  buttonTransform:SetParent(self.m_highlightRoot, true)
  local buttonComp = hudButton.gameObject:GetComponent(buttonType)
  if buttonComp ~= nil and enableBtn ~= true then
    buttonComp.enabled = false
  end
  hudButton:OnHighlight(true)
  return true
end

function SceneViewHud:_DehighlightHudButton(hudKey)
  if self.m_mapHightlightHudButton[hudKey] == nil then
    return false
  end
  local hudButton = self:GetHudButton(hudKey)
  local buttonTransform = hudButton.transform
  buttonTransform:SetParent(self.m_mapHightlightHudButton[hudKey].parent, true)
  buttonTransform:SetSiblingIndex(self.m_mapHightlightHudButton[hudKey]:GetSiblingIndex())
  local buttonComp = hudButton.gameObject:GetComponent(buttonType)
  if buttonComp ~= nil then
    buttonComp.enabled = true
  end
  self.m_mapHightlightHudButton[hudKey].gameObject:RemoveSelf()
  self.m_mapHightlightHudButton[hudKey] = nil
  hudButton:OnHighlight(false)
  return true
end

function SceneViewHud:HasAnyAnchorShown()
  for _, animType in pairs(self.m_mapAnimType) do
    if animType == EHudAnimType.Show then
      return true
    end
  end
  return false
end

function SceneViewHud:HasAnyAnchorHidden()
  for _, animType in pairs(self.m_mapAnimType) do
    if animType == EHudAnimType.Hide then
      return true
    end
  end
  return false
end

function SceneViewHud:_Animation(animType, anchorType, ignoreAnim)
  if self.m_mapAnimType[anchorType] ~= nil and self.m_mapTweens[self.m_mapAnimType[anchorType]] and self.m_mapTweens[self.m_mapAnimType[anchorType]][anchorType] then
    self.m_mapTweens[self.m_mapAnimType[anchorType]][anchorType]:Pause()
  end
  local duration
  if ignoreAnim then
    self:_UpdateAnchorRootStatus(animType, anchorType)
    duration = 0
  else
    if self.m_mapTweens[animType] == nil then
      self.m_mapTweens[animType] = {}
    end
    if self.m_mapTweens[animType][anchorType] == nil then
      self.m_mapTweens[animType][anchorType] = self:_CreateTween(animType, anchorType)
    else
      local targetDuration = self.Duration * self:_GetAnimProgress(animType, anchorType)
      self.m_mapTweens[animType][anchorType]:Goto(targetDuration, true)
    end
    duration = self.Duration
  end
  self.m_mapAnimType[anchorType] = animType
  return duration
end

function SceneViewHud:_UpdateAnchorRootStatus(animType, anchorType)
  local factor, animKey = self:_GetAnimFactor(animType, anchorType)
  if animKey == "posY" then
    self.m_mapAnchorRoot[anchorType].anchoredPosition = Vector2(self.m_mapAnchorRoot[anchorType].anchoredPosition.x, factor)
  elseif animKey == "posX" then
    self.m_mapAnchorRoot[anchorType].anchoredPosition = Vector2(factor, self.m_mapAnchorRoot[anchorType].anchoredPosition.y)
  elseif animKey == "scale" then
    self.m_mapAnchorRoot[anchorType].localScale = factor
  end
end

function SceneViewHud:_CreateTween(animType, anchorType)
  local fromFactor, _ = self:_GetAnimFactor(not animType, anchorType)
  local factor, animKey = self:_GetAnimFactor(animType, anchorType)
  if animKey == "posY" then
    self.m_mapAnchorRoot[anchorType].anchoredPosition = Vector2(self.m_mapAnchorRoot[anchorType].anchoredPosition.x, fromFactor)
    return self.m_mapAnchorRoot[anchorType]:DOAnchorPosY(factor, self.Duration):SetAutoKill(false)
  elseif animKey == "posX" then
    self.m_mapAnchorRoot[anchorType].anchoredPosition = Vector2(fromFactor, self.m_mapAnchorRoot[anchorType].anchoredPosition.y)
    return self.m_mapAnchorRoot[anchorType]:DOAnchorPosX(factor, self.Duration):SetAutoKill(false)
  elseif animKey == "scale" then
    self.m_mapAnchorRoot[anchorType].localScale = fromFactor
    return self.m_mapAnchorRoot[anchorType]:DOScale(factor, self.Duration):SetAutoKill(false)
  end
end

function SceneViewHud:_GetAnimFactor(animType, anchorType)
  if anchorType == EHudAnchorType.TopLeft or anchorType == EHudAnchorType.TopRight or anchorType == EHudAnchorType.TopCenter then
    return animType == EHudAnimType.Hide and self.TopHeight * self.m_curScale or 0, "posY"
  elseif anchorType == EHudAnchorType.BottomCenter then
    return animType == EHudAnimType.Hide and -(self.BottomHeight * self.m_curScale) or 0, "posY"
  elseif anchorType == EHudAnchorType.CenterLeft or anchorType == EHudAnchorType.BottomLeft or anchorType == EHudAnchorType.BottomLeft then
    return animType == EHudAnimType.Hide and -(self.LeftWidth * self.m_curScale) or 0, "posX"
  elseif anchorType == EHudAnchorType.CenterRight or anchorType == EHudAnchorType.BottomRight or anchorType == EHudAnchorType.BottomRight then
    return animType == EHudAnimType.Hide and self.RightWidth * self.m_curScale or 0, "posX"
  elseif anchorType == EHudAnchorType.Center then
    return animType == EHudAnimType.Hide and Vector3.zero or self.m_mapOriginScale[anchorType] * self.m_curScale, "scale"
  end
end

function SceneViewHud:_GetAnimProgress(animType, anchorType)
  local fromFactor, animKey = self:_GetAnimFactor(not animType, anchorType)
  local toFactor, _ = self:_GetAnimFactor(animType, anchorType)
  if animKey == "posY" then
    return math.max(0, (fromFactor - self.m_mapAnchorRoot[anchorType].anchoredPosition.y) / (fromFactor - toFactor))
  elseif animKey == "posX" then
    return math.max(0, (fromFactor - self.m_mapAnchorRoot[anchorType].anchoredPosition.x) / (fromFactor - toFactor))
  elseif animKey == "scale" then
    return math.max(0, (fromFactor.x - self.m_mapAnchorRoot[anchorType].localScale.x) / (fromFactor.x - toFactor.x))
  end
  return 0
end

function SceneViewHud.GetMainBoardHighlightHudKey()
  return GM.UIManager:GetOpenedViewCountByType(EViewType.Window) > 0 and ESceneViewHudButtonKey.MainBoardHighlight or ESceneViewHudButtonKey.MainBoard
end
