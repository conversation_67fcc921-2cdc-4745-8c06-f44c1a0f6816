RewardApi = {}
RewardApi.DefaultViewData = {eventLock = true}

function RewardApi.SplitRewards(arrRewards)
  if Table.IsEmpty(arrRewards) then
    return {}
  end
  local splitedRewardDatas = {}
  local properties = {}
  local items = {}
  local avatars = {}
  if arrRewards and not arrRewards[1] then
    arrRewards = {arrRewards}
  end
  local propertyDataManager = GM.PropertyDataManager
  local reward
  for i = 1, #arrRewards do
    reward = arrRewards[i]
    if propertyDataManager:IsPropertyType(reward[PROPERTY_TYPE]) then
      properties[#properties + 1] = reward
    elseif GM.UserProfileModel:IsAvatarReward(reward[PROPERTY_TYPE]) then
      avatars[#avatars + 1] = reward[PROPERTY_TYPE]
    elseif GM.ItemDataModel:GetModelConfig(reward[PROPERTY_TYPE], true) ~= nil then
      items[#items + 1] = reward
    else
      Log.Error("不存在的奖励类型：" .. tostring(reward[PROPERTY_TYPE]))
    end
  end
  if not Table.IsEmpty(properties) then
    splitedRewardDatas.properties = properties
  end
  if not Table.IsEmpty(items) then
    splitedRewardDatas.items = items
  end
  if not Table.IsEmpty(avatars) then
    splitedRewardDatas.avatars = avatars
  end
  return splitedRewardDatas
end

local checkDefinitions
local getCheckDefinitions = function()
  return {
    DigActivityDefinition,
    ProgressActivityDefinition
  }
end

function RewardApi.CheckRewardTypeValid(rewardType)
  if StringUtil.IsNilOrEmpty(rewardType) then
    return false
  end
  if checkDefinitions == nil then
    checkDefinitions = getCheckDefinitions()
  end
  for _, definitions in pairs(checkDefinitions) do
    for actType, definition in pairs(definitions) do
      if definition.ActivityTokenPropertyType == rewardType then
        local model = GM.ActivityManager:GetModel(actType)
        if model == nil or model:GetState() ~= ActivityState.Started then
          return false
        end
      end
    end
  end
  for actType, definition in pairs(BlindChestDefinition) do
    if definition.ActivityTokenPropertyType == rewardType then
      local model = GM.ActivityManager:GetModel(actType)
      if model == nil or not model:CheckActivityTokenValid() then
        return false
      end
    end
  end
  if AlbumActivityModel.IsAlbumPackReward(rewardType) then
    return AlbumActivityModel.CheckAlbumPackValid(rewardType)
  elseif AlbumActivityModel.IsAlbumJokerCard(rewardType) then
    return AlbumActivityModel.CheckJokerCardValid()
  end
  return true
end

function RewardApi.GetReplacedInvalidReward(reward)
  local mapReplaceRewards = GM.ConfigModel:GetRewardReplaceConfig()
  if Table.IsEmpty(mapReplaceRewards) or mapReplaceRewards[reward[PROPERTY_TYPE]] == nil then
    return
  end
  local newReward = Table.ShallowCopy(mapReplaceRewards[reward[PROPERTY_TYPE]])
  newReward[PROPERTY_COUNT] = newReward[PROPERTY_COUNT] * reward[PROPERTY_COUNT]
  RewardApi.CryptOneReward(newReward)
  return newReward
end

function RewardApi.AddFilterRewardEventListener(target, targetFunc)
  if checkDefinitions == nil then
    checkDefinitions = getCheckDefinitions()
  end
  for _, definitions in pairs(checkDefinitions) do
    for actType, definition in pairs(definitions) do
      EventDispatcher.AddListener(definition.StateChangedEvent, target, targetFunc, true)
    end
  end
  for _, definition in pairs(AlbumActivityDefinition) do
    EventDispatcher.AddListener(definition.StateChangedEvent, target, targetFunc, true)
  end
end

function RewardApi.GetReplacedCommonReward(reward)
  if Table.IsEmpty(reward) then
    return reward
  end
  local rewardType = reward[PROPERTY_TYPE]
  local rewardCount = reward[PROPERTY_COUNT]
  local bReplaced = false
  if ExtraBoardActivityModel.IsExtraBoardCommonItem(rewardType) then
    rewardType = ExtraBoardActivityModel.GetRealExtraBoardItem(rewardType)
    bReplaced = true
  end
  if not bReplaced then
    return reward
  elseif rewardType ~= nil then
    local newReward = {
      [PROPERTY_TYPE] = rewardType,
      [PROPERTY_COUNT] = rewardCount
    }
    RewardApi.CryptOneReward(newReward)
    return newReward
  end
end

function RewardApi.FilterRewards(arrRewards, weightKey)
  if Table.IsEmpty(arrRewards) then
    return arrRewards
  end
  local result = {}
  local replacedReward
  for _, v in ipairs(arrRewards) do
    if RewardApi.CheckRewardTypeValid(v[PROPERTY_TYPE]) then
      replacedReward = RewardApi.GetReplacedCommonReward(v)
      if replacedReward ~= nil then
        if weightKey ~= nil and v[weightKey] ~= nil then
          replacedReward[weightKey] = v[weightKey]
        end
        table.insert(result, replacedReward)
      end
    else
      GM.UIManager:ShowTestPrompt(v[PROPERTY_TYPE] .. " 未通过奖励有效性检查")
      local replacedInvalidReward = RewardApi.GetReplacedInvalidReward(v)
      if replacedInvalidReward ~= nil then
        if weightKey ~= nil and v[weightKey] ~= nil then
          replacedInvalidReward[weightKey] = v[weightKey]
        end
        table.insert(result, replacedInvalidReward)
      end
    end
  end
  if GameConfig.IsTestMode() and Table.IsEmpty(result) then
    Log.Error("剔除非法奖励后，奖励为空，请检查")
  end
  return result
end

function RewardApi.IsImmediateEffectItem(type, ignoreGameMode)
  local config = GM.ItemDataModel:GetModelConfig(type, true)
  if config and config.BoosterType == "NewDay" and (ignoreGameMode or GM.SceneManager:GetGameMode() == EGameMode.Board) then
    return true, config
  end
  return false
end

function RewardApi.AcquireRewards(formattedRewards, source, scene, viewData, targetGameMode, cacheItemType, sourceGameMode)
  if formattedRewards == nil then
    return
  end
  RewardApi.AcquireRewardsLogic(formattedRewards, source, scene, targetGameMode, cacheItemType, sourceGameMode)
  RewardApi.AcquireRewardsInView(formattedRewards, viewData)
end

function RewardApi.AcquireRewardsLogic(formattedRewards, source, scene, targetGameMode, cacheItemType, sourceGameMode)
  local arrRewards = RewardApi.FilterRewards(formattedRewards)
  local splitedRewardDatas = RewardApi.SplitRewards(arrRewards)
  RewardApi._AcquireRewardsLogic(splitedRewardDatas, source, scene, targetGameMode, cacheItemType, sourceGameMode)
end

function RewardApi._AcquireRewardsLogic(splitedRewardDatas, source, scene, targetGameMode, cacheItemType, sourceGameMode)
  if not Table.IsEmpty(splitedRewardDatas.items) then
    local mapItemsByGameModes = RewardApi._SeparateItemRewardsByGameModes(splitedRewardDatas.items, targetGameMode)
    for eGameMode, arrItems in pairs(mapItemsByGameModes) do
      RewardApi._AcquireItemRewardsLogic(arrItems, source, scene, eGameMode, cacheItemType)
    end
  end
  if not Table.IsEmpty(splitedRewardDatas.avatars) then
    for _, avatar in ipairs(splitedRewardDatas.avatars) do
      GM.UserProfileModel:AcquireAvatar(avatar)
      GM.BIManager:LogAcquire(avatar, 1, scene, source == EPropertySource.Give)
    end
  end
  if not Table.IsEmpty(splitedRewardDatas.properties) then
    GM.PropertyDataManager:Acquire(splitedRewardDatas.properties, source, scene, sourceGameMode)
  end
end

function RewardApi._AcquireItemRewardsLogic(items, source, scene, targetGameMode, cacheItemType)
  if not Table.IsEmpty(items) then
    local arrItems = {}
    local costList = {}
    for _, itemData in ipairs(items) do
      local oneItemCost
      if itemData.cost then
        oneItemCost = ItemModelHelper.SplitCost(itemData.cost, itemData[PROPERTY_COUNT])
      end
      local isImmediate, config = RewardApi.IsImmediateEffectItem(itemData[PROPERTY_TYPE])
      for i = 1, itemData[PROPERTY_COUNT] do
        if isImmediate then
          ItemSpeeder.TakeEffect(GM.MainBoardModel, config.Effect, itemData[PROPERTY_TYPE])
        else
          arrItems[#arrItems + 1] = itemData[PROPERTY_TYPE]
          costList[#costList + 1] = oneItemCost or {}
        end
      end
    end
    local boardModel = BoardModelHelper.GetModelByGameMode(targetGameMode)
    boardModel:CacheItems(arrItems, cacheItemType, costList, true)
    for _, itemData in ipairs(items) do
      GM.BIManager:LogAcquire(itemData[PROPERTY_TYPE], itemData[PROPERTY_COUNT], scene, source == EPropertySource.Give, targetGameMode)
    end
    GM.ItemDataModel:SetRewardsLocked(items)
  end
end

function RewardApi._SeparateItemRewardsByGameModes(arrItemRewards, forcedGameMode)
  local mapItemsByGameModes = {}
  for _, itemReward in ipairs(arrItemRewards) do
    local gameMode = ItemUtility.GetModeByCode(itemReward[PROPERTY_TYPE], forcedGameMode)
    if gameMode then
      local arrItemsRewards = mapItemsByGameModes[gameMode]
      if not arrItemsRewards then
        arrItemsRewards = {}
        mapItemsByGameModes[gameMode] = arrItemsRewards
      end
      arrItemsRewards[#arrItemsRewards + 1] = itemReward
    end
  end
  return mapItemsByGameModes
end

local arrDefaultUIWolrdPos = {
  {
    V3Zero
  },
  {
    Vector3(-80, 0, 0),
    Vector3(80, 0, 0)
  },
  {
    Vector3(-150, 0, 0),
    V3Zero,
    Vector3(150, 0, 0)
  }
}

function RewardApi.AcquireRewardsInView(arrRewards, viewData)
  arrRewards = RewardApi.FilterRewards(arrRewards)
  viewData = viewData or Table.ShallowCopy(RewardApi.DefaultViewData)
  if not viewData.arrWorldPos then
    viewData.arrWorldPos = arrDefaultUIWolrdPos[#arrRewards] or Table.Empty
  end
  if not viewData.arrNoDiffusion then
    viewData.arrNoDiffusion = Table.Empty
  end
  if not viewData.arrFlyPause then
    viewData.arrFlyPause = Table.Empty
  end
  if not viewData.arrFlyStartScale then
    viewData.arrFlyStartScale = Table.Empty
  end
  viewData.targetGameMode = viewData.targetGameMode or Table.Empty
  local baseSceneView = GM.UIManager:GetOpenedViewByName(UIPrefabConfigName.BaseSceneView)
  local itemFlyEndPosition = baseSceneView:GetFlyCachedItemTargetPosition()
  local itemFlyEndPositionInOtherGameMode = baseSceneView:GetFlyCachedItemTargetPosition(false)
  local curGameMode = GM.SceneManager:GetGameMode()
  local flyDelay = 0
  for i, rewardData in ipairs(arrRewards) do
    local uiWorldPosition = viewData.arrWorldPos[i] or V3Zero
    local noDiffusion = viewData.arrNoDiffusion[i] or false
    local flyPause = viewData.arrFlyPause[i] or 0
    local flyStartScale = viewData.arrFlyStartScale[i] or 1
    local toCacheRoot = not viewData.simpleCacheRoot and (viewData.targetGameMode[i] or curGameMode) == curGameMode and curGameMode ~= EGameMode.Main
    local endPosition = (toCacheRoot or viewData.simpleCacheRoot) and itemFlyEndPosition or itemFlyEndPositionInOtherGameMode
    local rewardType = rewardData[PROPERTY_TYPE]
    if not (not GM.PropertyDataManager:IsPropertyType(rewardType) or AlbumActivityModel.IsAlbumPackReward(rewardType)) or ExtraBoardActivityModel.IsExtraBoardActivityItem(rewardType) then
      if rewardType == EPropertyType.BakeOutToken and not GM.ConfigModel:UseNewOrderRewardAnimation() then
        if GM.SceneManager:GetGameMode() == EGameMode.Board then
          local boardView = MainBoardView.GetInstance()
          local bakeOutBubble = boardView:GetOrderArea():GetIconAreaByActivityType(ActivityType.BakeOut)
          RewardApi._PlayAcquireTokenAnimationWithTwoPrefab(UIPrefabConfigName.effect_UI_xingxing_1, UIPrefabConfigName.effect_UI_xingxing_mz, uiWorldPosition, boardView, bakeOutBubble, rewardData)
        end
      else
        local customData = {
          {
            flyDelay = flyDelay,
            eventLock = viewData.eventLock,
            alwaysInMiddle = viewData.alwaysInMiddle,
            floatLabel = viewData.floatLabel,
            floatUp = viewData.floatUp,
            floatLarge = viewData.floatLarge,
            flyCount = viewData.flyCount,
            noDiffusion = noDiffusion,
            flyPause = flyPause,
            spriteScale = viewData.spriteScale,
            flyStartScale = flyStartScale
          }
        }
        if viewData.simpleCollect then
          GM.PropertyDataManager:PlayPropertyIncreaseAnimation({rewardData}, customData)
        else
          GM.PropertyDataManager:PlayCollectAnimation({rewardData}, uiWorldPosition, customData)
        end
      end
    elseif StringUtil.StartWith(rewardType, ProducerInventoryRewardPrefix) then
      local customData = {
        {
          flyCount = 1,
          eventLock = viewData.eventLock
        }
      }
      GM.PropertyDataManager:PlayCollectAnimation({rewardData}, uiWorldPosition, customData)
    elseif not viewData.simpleCollect and rewardType ~= ItemType.Lollipop01 then
      local customData = {
        {
          endPos = endPosition,
          flyCount = 1,
          spriteKey = GM.ItemDataModel:GetSpriteName(rewardType),
          flyDelay = flyDelay,
          eventLock = viewData.eventLock,
          targetButton = viewData.targetButton,
          spriteScale = viewData.spriteScale,
          toCacheRoot = toCacheRoot,
          floatLabel = viewData.floatLabel,
          simpleCacheRoot = viewData.simpleCacheRoot,
          inverseOrder = viewData.inverseOrder
        }
      }
      if (GM.ItemDataModel:GetModelConfig(rewardType, true) ~= nil or AlbumActivityModel.IsAlbumPackReward(rewardType)) and not RewardApi.IsImmediateEffectItem(rewardType) then
        EventDispatcher.DispatchEvent(EEventType.ViewCacheItems, flyDelay)
      end
      GM.PropertyDataManager:PlayCollectAnimation({rewardData}, uiWorldPosition, customData)
    end
    if viewData.interval then
      flyDelay = flyDelay + viewData.interval
    end
  end
end

local starCount = 6
local singleDelay = 0.15
local jumpPowerInterval = 45
local flyDuration = 1.05
MAX_BAKEOUT_TOKEN_FLY_TIME = singleDelay * (starCount - 1) + flyDuration

function RewardApi._PlayAcquireTokenAnimationWithTwoPrefab(flyPrefabName, targetPrefabName, uiWorldPosition, boardView, hudButton, reward)
  if hudButton == nil then
    return
  end
  local targetWorldPosition = PositionUtil.UICameraScreen2World(boardView:ConvertWorldPositionToScreenPosition(hudButton:GetScaleTrans().position))
  uiWorldPosition.z = 0
  targetWorldPosition.z = 0
  local jumpPower = {}
  for i = 1, starCount do
    jumpPower[i] = math.random(0, jumpPowerInterval) + jumpPowerInterval * (i - starCount / 2)
  end
  for i = 1, starCount do
    local power = Table.ListRandomSelectOne(jumpPower)
    Table.ListRemove(jumpPower, power)
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(flyPrefabName), GM.UIManager:GetCanvasRoot(), uiWorldPosition, function(go)
      if go:IsNull() then
        return
      end
      local transform = go.transform
      local sequence = DOTween.Sequence()
      sequence:AppendInterval((i - 1) * singleDelay)
      sequence:Append(transform:DOJump(targetWorldPosition, power, 1, flyDuration):SetEase(Ease.InOutCubic))
      sequence:AppendCallback(function()
        GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(targetPrefabName), GM.UIManager:GetCanvasRoot(), targetWorldPosition, function(go)
          DelayExecuteFunc(function()
            if not go:IsNull() then
              go:RemoveSelf()
            end
          end, 1)
        end)
        if hudButton and not hudButton.gameObject:IsNull() then
          if hudButton.IconScaleAnimation ~= nil then
            hudButton:IconScaleAnimation(false, reward[PROPERTY_TYPE])
          end
          if hudButton.UpdateTextAnimation ~= nil then
            hudButton:UpdateTextAnimation(reward[PROPERTY_TYPE], reward[PROPERTY_COUNT] / starCount, false)
          end
        end
      end)
      sequence:OnComplete(function()
        go:SetActive(false)
        go:RemoveSelf()
        if i == starCount then
          local bakeoutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
          bakeoutModel:TryUpdateDisplayRank()
        end
      end)
    end)
  end
end

function RewardApi._PlayAcquireTokenAnimationWithPrefab(flyPrefabName, uiWorldPosition, boardView, boardBubble, callback)
  if boardBubble == nil then
    return
  end
  local targetWorldPosition = PositionUtil.UICameraScreen2World(boardView:ConvertWorldPositionToScreenPosition(boardBubble.transform.position))
  uiWorldPosition.z = 0
  targetWorldPosition.z = 0
  local jumpPower = {}
  for i = 1, starCount do
    jumpPower[i] = math.random(0, jumpPowerInterval) + jumpPowerInterval * (i - starCount / 2)
  end
  for i = 1, starCount do
    local power = Table.ListRandomSelectOne(jumpPower)
    Table.ListRemove(jumpPower, power)
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(flyPrefabName), GM.UIManager:GetCanvasRoot(), uiWorldPosition, function(go)
      if go:IsNull() then
        return
      end
      local transform = go.transform
      local sequence = DOTween.Sequence()
      sequence:AppendInterval((i - 1) * singleDelay)
      sequence:Append(transform:DOJump(targetWorldPosition, power, 1, flyDuration):SetEase(Ease.InOutCubic))
      sequence:AppendCallback(function()
        if i == starCount and callback then
          callback()
        end
      end)
      sequence:OnComplete(function()
        go:SetActive(false)
        go:RemoveSelf()
      end)
    end)
  end
end

function RewardApi.GetRewardIconNameAndIsSetNativeSize(rewardType)
  if GM.PropertyDataManager:IsPropertyType(rewardType) then
    return EPropertySpriteBig[rewardType], true
  elseif GM.UserProfileModel:IsAvatarReward(rewardType) then
    return GM.UserProfileModel:GetAvatarImage(rewardType), false
  elseif StringUtil.StartWith(rewardType, ProducerInventoryRewardPrefix) then
    return GM.ItemDataModel:GetSpriteName(string.sub(rewardType, string.len(ProducerInventoryRewardPrefix) + 1)), false
  else
    return GM.ItemDataModel:GetSpriteName(rewardType), true
  end
end

function RewardApi.CryptOneReward(reward)
  if not reward then
    return
  end
  reward[PROPERTY_CRYPT] = Crypt.CryptCurrency(reward[PROPERTY_COUNT])
end

function RewardApi.CryptRewards(arrRewards, override)
  if not arrRewards then
    return
  end
  for _, reward in ipairs(arrRewards) do
    if reward[PROPERTY_CRYPT] and not override then
      if GameConfig.IsTestMode() then
        Log.Error("禁止重复加密奖励！")
      end
    else
      reward[PROPERTY_CRYPT] = Crypt.CryptCurrency(reward[PROPERTY_COUNT])
    end
  end
end

function RewardApi.GetMergedRewards(arrRewards)
  local arrMergedRewards = {}
  if not arrRewards then
    return arrMergedRewards
  end
  local mapTypeIndex = {}
  local rewardType, index
  local count = 0
  for _, reward in ipairs(arrRewards) do
    rewardType = reward[PROPERTY_TYPE]
    index = mapTypeIndex[rewardType]
    if index == nil then
      count = count + 1
      arrMergedRewards[count] = {
        [PROPERTY_TYPE] = rewardType,
        [PROPERTY_COUNT] = reward[PROPERTY_COUNT]
      }
      mapTypeIndex[rewardType] = count
    else
      arrMergedRewards[index][PROPERTY_COUNT] = arrMergedRewards[index][PROPERTY_COUNT] + reward[PROPERTY_COUNT]
    end
  end
  return arrMergedRewards
end
