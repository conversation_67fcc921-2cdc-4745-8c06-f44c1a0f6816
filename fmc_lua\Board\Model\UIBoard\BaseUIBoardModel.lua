BaseUIBoardModel = setmetatable({}, BaseInteractiveBoardModel)
BaseUIBoardModel.__index = BaseUIBoardModel

function BaseUIBoardModel.Create(gameMode, itemDataTable, itemLayerDataTable, itemCacheDataTable, initCodeMap, width, height, activityType)
  local boardModel = setmetatable({}, BaseUIBoardModel)
  boardModel:Init(itemDataTable, itemLayerDataTable, itemCacheDataTable, initCodeMap, width, height, activityType)
  return boardModel
end

function BaseUIBoardModel:Init(gameMode, itemDataTable, itemLayerDataTable, itemCacheDataTable, initCodeMap, width, height, activityType)
  self.m_itemDataTable = itemDataTable
  self.m_itemLayerDataTable = itemLayerDataTable
  self.m_itemCacheDataTable = itemCacheDataTable
  self.m_initCodeMap = initCodeMap
  self.m_activityType = activityType
  local itemManager = ItemManager.Create(itemDataTable, self)
  itemManager:OnSyncDataFinished()
  local itemCacheModel = ItemCacheModel.Create(itemCacheDataTable, itemManager:GetIdGenerator())
  itemCacheModel:OnSyncDataFinished()
  BaseInteractiveBoardModel.Init(self, gameMode, itemManager, itemCacheModel, width, height)
end

function BaseUIBoardModel:DropData()
  self.m_itemDataTable:Drop()
  self.m_itemLayerDataTable:Drop()
  self.m_itemCacheDataTable:Drop()
  self.m_itemCacheModel:ResetVar()
  self.m_itemManager:ResetVar()
end

function BaseUIBoardModel:_CreateItemLayerModel()
  return BaseUIItemLayerModel.Create(self, self.m_itemLayerDataTable, self.m_itemManager, self.m_initCodeMap)
end

function BaseUIBoardModel:_MergeItem(item, targetItem, targetPosition)
  local bItemSpreadFinished = true
  for _, v in pairs({item, targetItem}) do
    local itemSpread = v:GetComponent(ItemSpread)
    if itemSpread ~= nil and not itemSpread:IsSpreadFinish() then
      bItemSpreadFinished = false
      break
    end
  end
  if not bItemSpreadFinished then
    GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "extraboard_tip1_title", "extraboard_tip1_desc", "common_button_ok", "common_button_cancel", function(window)
      if self:CheckItemStillInPosition(item) and self:CheckItemStillInPosition(targetItem) then
        BaseInteractiveBoardModel._MergeItem(self, item, targetItem, targetPosition)
      elseif self:CheckItemStillInPosition(item) then
        item:SetPosition(item:GetPosition())
      end
      window:Close()
    end, function(window)
      if self:CheckItemStillInPosition(item) then
        item:SetPosition(item:GetPosition())
      end
      window:Close()
    end, false)
  else
    BaseInteractiveBoardModel._MergeItem(self, item, targetItem, targetPosition)
  end
end

function BaseUIBoardModel:CheckItemStillInPosition(itemModel)
  if not itemModel or self:GetItem(itemModel:GetPosition()) ~= itemModel then
    return false
  end
  return true
end

function BaseUIBoardModel:GetMergeAllIgnoreItems()
  local mergeAllIgnoredItems = BaseInteractiveBoardModel.GetMergeAllIgnoreItems(self)
  local filter = function(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    return itemSpread ~= nil and not itemSpread:IsSpreadFinish()
  end
  local arrItems = self:FilterItems(filter)
  for _, item in ipairs(arrItems or {}) do
    mergeAllIgnoredItems[item] = true
  end
  return mergeAllIgnoredItems
end

function BaseUIBoardModel:GetActivityType()
  return self.m_activityType
end

function BaseUIBoardModel:CanShowDeleteButton()
  local hasCollectable = not Table.IsEmpty(self:FilterItems(function(item)
    return item:GetComponent(ItemCollectable) ~= nil or item:GetComponent(ItemRewardBubble) ~= nil
  end))
  local hasMergePair = not Table.IsEmpty(self:FindMergePair({}))
  return self:IsBoardFull() and not hasCollectable and not hasMergePair
end
