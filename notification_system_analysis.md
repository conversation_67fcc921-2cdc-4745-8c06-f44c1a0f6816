# Notification System 通知系统详细分析文档

## 概述

Notification System（通知系统）是游戏中的推送通知管理模块，负责处理本地推送通知的注册、调度和权限管理。该系统包含多种通知类型，支持智能的弹窗触发机制，并提供完整的用户权限管理功能。

## 核心架构

### 1. 通知类型定义

通知系统定义了多种通知类型，每种类型都有不同的优先级和用途。

```lua
-- 通知类型枚举（按优先级递增）
NotificationType = {
  ComeBack = 1,              -- 回归通知（最高优先级）
  EnergyFull = 2,            -- 体力满通知
  CashDash = 3,              -- 金币冲刺活动通知
  Lollipop = 4,              -- 棒棒糖活动通知
  Coconut = 5,               -- 椰子活动通知
  Pinata = 6,                -- 皮纳塔活动通知
  ItemSpreadRecovery = 7     -- 物品冷却恢复通知（最低优先级）
}
```

**优先级机制**：
- `MaxPriority = 1`：最高优先级（回归通知）
- `MinPriority = 7`：最低优先级（物品冷却恢复）
- 当通知数量超限时，优先保留高优先级通知

### 2. 通知场景定义

系统定义了各种通知触发场景，用于匹配不同的游戏状态。

```lua
-- 通知场景枚举
NotificationScene = {
  EnergyRefill = "EnergyRefill",        -- 体力恢复
  ItemCooldown = "ItemCooldown",        -- 物品冷却
  CashDashStart = "CashDashStart",      -- 金币冲刺开始
  CashDashEnd = "CashDashEnd",          -- 金币冲刺结束
  ComeBack = "ComeBack",                -- 回归提醒
  LollipopStart = "LollipopStart",      -- 棒棒糖活动开始
  LollipopEnd = "LollipopEnd",          -- 棒棒糖活动结束
  CoconutStart = "CoconutStart",        -- 椰子活动开始
  CoconutEnd = "CoconutEnd",            -- 椰子活动结束
  PinataStart = "PinataStart",          -- 皮纳塔活动开始
  PinataEnd = "PinataEnd"               -- 皮纳塔活动结束
}
```

### 3. 弹窗描述键定义

用于标识不同场景下的弹窗提示内容。

```lua
-- 弹窗场景描述键
ENotiSceneDescKey = {
  EnergyEmpty = "notification_desc_energy",        -- 体力不足
  CashDash = "notification_desc_cash_dash",        -- 金币冲刺
  ItemCoolDown = "notification_desc_item_cooldown" -- 物品冷却
}
```

## 通知助手系统

### 1. 基础通知助手

所有通知助手都继承自 `BaseNotificationHelper`，提供统一的接口规范。

```lua
-- 基础通知助手接口
function BaseNotificationHelper.IsSceneExist(strScene)
  -- 子类必须实现此方法，判断是否支持指定场景
  Log.Assert(false, "通知需要继承IsSceneExist判断自己是否支持配置的strScene")
  return false
end
```

**设计思路**：
- 使用策略模式，每种通知类型对应一个助手类
- 统一的接口规范，便于扩展新的通知类型
- 通过场景匹配机制，实现灵活的通知生成

### 2. 回归通知助手

处理玩家离线一定时间后的回归提醒通知。

```lua
-- 回归通知助手实现
function ComeBackNotificationHelper.IsSceneExist(strScene)
  if string.find(strScene, NotificationScene.ComeBack) ~= nil then
    return true
  end
  return false
end

function ComeBackNotificationHelper.GetHours(strScene)
  local strSplit = StringUtil.Split(strScene, "_")
  if strSplit[2] ~= nil then
    return tonumber(strSplit[2]) * 3600  -- 转换为秒
  end
  Log.Assert(false, "ComeBack 配置错误")
  return Sec2Day  -- 默认24小时
end

function ComeBackNotificationHelper.Generate(strScene)
  local results = {}
  local llTimeSec = ComeBackNotificationHelper.GetHours(strScene)
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  strTileKey = strTileKey ~= "" and strTileKey or "push_come_back_title"
  strDescKey = strDescKey ~= "" and strDescKey or "push_come_back_desc"
  
  table.insert(results, {
    Type = NotificationType.ComeBack,
    Title = GM.GameTextModel:GetText(strTileKey),
    Message = GM.GameTextModel:GetText(strDescKey),
    Delay = llTimeSec
  })
  return results
end
```

**功能特点**：
- 支持配置化的回归时间（通过场景名称中的数字指定小时数）
- 场景格式：`ComeBack_24`（表示24小时后发送回归通知）
- 自动获取本地化文本内容

### 3. 体力满通知助手

当玩家体力即将恢复满时发送通知。

```lua
-- 体力满通知助手实现
function EnergyFullNotificationHelper.Generate(strScene)
  if GM.EnergyModel:GetEnergy(EnergyType.Main) >= 50 then
    return {}  -- 体力已满，不需要通知
  end
  
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  strTileKey = strTileKey ~= "" and strTileKey or "push_energy_title"
  strDescKey = strDescKey ~= "" and strDescKey or "push_energy_desc"
  
  local notification = {
    Type = NotificationType.EnergyFull,
    Title = GM.GameTextModel:GetText(strTileKey),
    Message = GM.GameTextModel:GetText(strDescKey),
    Delay = GM.EnergyModel:GetEnergyFullDuration(EnergyType.Main)
  }
  
  if notification.Delay == nil or notification.Delay <= 0 then
    return {}  -- 延迟时间无效，不发送通知
  end
  
  return {notification}
end
```

**智能判断机制**：
- 只有在体力不满（小于50）时才生成通知
- 计算精确的体力恢复满时间作为延迟
- 如果计算出的延迟时间无效，则不发送通知

### 4. 物品冷却恢复通知助手

为棋盘上冷却中的物品生成恢复通知。

```lua
-- 物品冷却恢复通知助手实现
function ItemSpreadRecoveryNotificationHelper.Generate(strScene)
  local results = {}
  local strTileKey, strDescKey = GM.NotificationModel:GetTextTileAndDesc(strScene)
  strTileKey = strTileKey ~= "" and strTileKey or "push_cooldown_title"
  strDescKey = strDescKey ~= "" and strDescKey or "push_cooldown_desc"
  
  for itemModel, _ in pairs(GM.MainBoardModel:GetAllBoardItems()) do
    local itemSpread = itemModel and itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and 
       itemSpread:GetStorageRestNumber() == 0 and 
       itemSpread:GetState() ~= ItemSpreadState.OpenFinish then
      
      local startTimer = itemSpread:GetStartTimer()
      local delay = startTimer + itemSpread:GetTimerDuration() - GM.GameModel:GetServerTime()
      
      if 1200 <= delay then  -- 至少20分钟才发送通知
        local itemName = GM.GameTextModel:GetText(ItemNameDefinition.GetName(itemModel:GetType()))
        table.insert(results, {
          Type = NotificationType.ItemSpreadRecovery,
          Title = GM.GameTextModel:GetText(strTileKey),
          Message = GM.GameTextModel:GetText(strDescKey, itemName),
          Delay = delay
        })
      end
    end
  end
  return results
end
```

**智能筛选机制**：
- 只为存储数量为0且未完成的物品生成通知
- 只有冷却时间超过20分钟（1200秒）的物品才发送通知
- 在通知消息中包含具体的物品名称

### 5. 活动通知助手

为各种限时活动生成开始和结束通知。

```lua
-- 活动通知助手基类
function DashActivityNotificationHelper._GenerateDashStartNotification(results, pDashModel, eNotiType, strTextTitle, strTextDesc)
  table.insert(results, {
    Type = eNotiType,
    Title = GM.GameTextModel:GetText(strTextTitle),
    Message = GM.GameTextModel:GetText(strTextDesc),
    Delay = pDashModel:GetNextStateTime() - GM.GameModel:GetServerTime()
  })
end

function DashActivityNotificationHelper._GenerateDashEndNotification(results, pDashModel, eNotiType, strTextTitle, strTextDesc, nEndtimeDelay)
  if nEndtimeDelay == nil then
    nEndtimeDelay = 18000  -- 默认提前5小时提醒
  end
  
  local level = pDashModel:GetLevel()
  local levelConfigs = pDashModel:GetLevelConfigs()
  if levelConfigs[level] == nil then
    return  -- 等级配置不存在
  end
  
  if level == 1 and pDashModel:GetScore() == 0 then
    return  -- 1级且无分数，不发送结束通知
  end
  
  local delay = pDashModel:GetNextStateTime() - GM.GameModel:GetServerTime() - nEndtimeDelay
  if delay <= 0 then
    return  -- 延迟时间无效
  end
  
  table.insert(results, {
    Type = eNotiType,
    Title = GM.GameTextModel:GetText(strTextTitle),
    Message = GM.GameTextModel:GetText(strTextDesc),
    Delay = delay
  })
end
```

**活动通知特点**：
- 开始通知：在活动即将开始时发送
- 结束通知：在活动即将结束前一定时间发送（可配置）
- 智能过滤：只为有意义的活动状态发送通知

## 通知注册与调度系统

### 1. 通知注册流程

通知注册是一个复杂的多步骤过程，涉及权限检查、通知生成、时间分组、优先级过滤等环节。

```lua
-- 通知注册主函数
function NotificationModel:RegisterAll()
  if not self.m_inited then
    return  -- 系统未初始化
  end

  -- 权限检查
  if self.m_config == nil or
     PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1) == 0 or
     not PlatformInterface.IsNotificationsEnabled() then
    return  -- 配置缺失、用户关闭或系统权限未开启
  end

  self:UnregisterAll()  -- 清除所有现有通知

  -- 遍历服务器配置的通知组
  for _, oOneCfg in pairs(self.m_config) do
    local notificationList = self:_GenerateList(oOneCfg)
    local intervaleMap = self._SplitListByInterval(notificationList, oOneCfg)
    for _, list in pairs(intervaleMap) do
      local selectedList = self._FilterList(list, oOneCfg)
      for _, notification in ipairs(selectedList) do
        self:_Schedule(notification)
      end
    end
  end
end
```

**注册前置条件**：
- 系统已完成初始化（`m_inited = true`）
- 服务器配置已加载（`m_config` 不为空）
- 用户未关闭通知功能（`OpenNotification = 1`）
- 系统通知权限已开启（`PlatformInterface.IsNotificationsEnabled()`）

### 2. 通知生成机制

根据服务器配置和当前游戏状态生成具体的通知列表。

```lua
-- 通知生成函数
function NotificationModel:_GenerateList(oConfig)
  local results = {}

  -- 遍历配置的场景，使用对应的助手生成通知
  for _, strScene in pairs(oConfig.scene) do
    for _, helper in ipairs(self.m_helpers) do
      if helper.IsSceneExist(strScene) then
        Table.ListAppend(results, helper.Generate(strScene))
      end
    end
  end

  -- 处理推送时间限制
  if oConfig.pushtimetype == 1 and
     type(oConfig.pushtime) == "table" and
     oConfig.pushtime[1] ~= nil and
     oConfig.pushtime[2] ~= nil then
    self:_CalculateLocalTime(results, oConfig.pushtime)
  end

  -- 按延迟时间排序
  table.sort(results, function(a, b)
    return a.Delay < b.Delay
  end)

  return results
end
```

**生成流程**：
1. 遍历配置中的所有场景
2. 为每个场景找到对应的通知助手
3. 调用助手的 `Generate` 方法生成通知
4. 应用推送时间限制（如果配置了时间窗口）
5. 按延迟时间排序

### 3. 推送时间计算

当配置了推送时间窗口时，系统会调整通知的发送时间到指定的时间范围内。

```lua
-- 推送时间计算函数
function NotificationModel:_CalculateLocalTime(results, pushtime)
  local nLeftHour = pushtime[1]   -- 时间窗口开始小时
  local nRightHour = pushtime[2]  -- 时间窗口结束小时
  local llLocalTime = os.time()

  local GetZeroTime = function(llCurTime)
    local date_table = os.date("*t", llCurTime)
    date_table.hour = 0
    date_table.min = 0
    date_table.sec = 0
    return os.time(date_table)
  end

  for _, pushInfo in pairs(results) do
    local llPushTime = llLocalTime + pushInfo.Delay
    local strHour = os.date("%H", llPushTime)
    local hour = tonumber(strHour)
    local llCurZeroTime = GetZeroTime(llPushTime)
    local llNextZeroTime = llCurZeroTime + 86400

    if nLeftHour > hour then
      -- 如果推送时间早于时间窗口，延迟到当天的窗口开始时间
      pushInfo.Delay = llCurZeroTime + nLeftHour * 60 * 60 - llLocalTime
    end
    if nRightHour < hour then
      -- 如果推送时间晚于时间窗口，延迟到第二天的窗口开始时间
      pushInfo.Delay = llNextZeroTime + nLeftHour * 60 * 60 - llLocalTime
    end
  end
end
```

**时间窗口机制**：
- 配置格式：`[开始小时, 结束小时]`，如 `[9, 21]` 表示9点到21点
- 如果计算出的推送时间在窗口外，会自动调整到下一个合适的时间
- 确保通知不会在用户休息时间发送，提升用户体验

### 4. 时间间隔分组

将通知按时间间隔分组，避免在短时间内发送过多通知。

```lua
-- 时间间隔分组函数
function NotificationModel._SplitListByInterval(list, oOneConfig)
  local results = {}
  for _, notification in ipairs(list) do
    local index = notification.Delay // oOneConfig.interval
    if results[index] == nil then
      results[index] = {}
    end
    table.insert(results[index], notification)
  end
  return results
end
```

**分组原理**：
- 根据配置的时间间隔（`interval`）将通知分组
- 同一时间段内的通知会被归为一组
- 为后续的数量限制和优先级过滤做准备

### 5. 优先级过滤机制

当某个时间段内的通知数量超过限制时，按优先级选择要发送的通知。

```lua
-- 优先级过滤函数
function NotificationModel._FilterList(list, oOneConfig)
  local number = oOneConfig.maxNum
  if number >= #list then
    return list  -- 数量未超限，全部保留
  end

  local results = {}

  -- 按优先级从高到低选择通知
  for type = NotificationType.MaxPriority, NotificationType.MinPriority do
    if number == 0 then
      break
    end
    for index, notification in ipairs(list) do
      if notification.Type == type then
        table.insert(results, notification)
        table.remove(list, index)
        number = number - 1
        break
      end
    end
  end

  -- 如果还有剩余配额，按原顺序填充
  while number ~= 0 and #list ~= 0 do
    table.insert(results, list[1])
    table.remove(list, 1)
    number = number - 1
  end

  return results
end
```

**过滤策略**：
1. 优先保留高优先级通知（数值小的优先级高）
2. 每个优先级最多选择一个通知
3. 如果高优先级通知数量不足，按原顺序补充低优先级通知
4. 确保不超过配置的最大数量限制

### 6. 通知调度执行

将过滤后的通知提交给系统进行实际调度。

```lua
-- 通知调度函数
function NotificationModel:_Schedule(notification)
  CSNotificationManager:ScheduleNotifi(notification.Title, notification.Message, notification.Delay)
end
```

**调度特点**：
- 调用原生平台接口进行通知调度
- 支持标题、消息内容和延迟时间
- 由系统负责在指定时间发送通知

## 弹窗触发规则系统

### 1. 弹窗触发核心函数

`TryOpenNotificationWindow` 是弹窗触发的核心函数，包含了完整的触发条件判断逻辑。

```lua
-- 弹窗触发主函数
function NotificationModel:TryOpenNotificationWindow(eNotiSceneDescKey)
  -- 1. 自动运行模式检查
  if IsAutoRun() then
    return false  -- 自动运行模式下不显示弹窗
  end

  -- 2. 系统权限检查
  if PlatformInterface.IsNotificationsEnabled() then
    return  -- 系统通知已开启，无需弹窗提醒
  end

  -- 3. 用户设置检查
  if PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1) == 0 then
    return  -- 用户已关闭通知功能
  end

  -- 4. 特定场景条件检查
  if eNotiSceneDescKey == ENotiSceneDescKey.EnergyEmpty then
    local energy = GM.EnergyModel:GetEnergy(EnergyType.Main)
    if energy >= ItemSpread.GetCostEnergyNum() then
      return  -- 体力充足，无需提醒开启通知
    end
  end

  -- 5. 时间间隔限制检查
  local serTime = GM.GameModel:GetServerTime()
  local lastTime = PlayerPrefs.GetInt(EPlayerPrefKey.LastNotifiWindowPopTime, 0)
  if serTime - lastTime < 259200 then  -- 72小时 = 259200秒
    return  -- 距离上次弹窗时间不足72小时
  end

  -- 6. 弹窗次数限制检查
  local popTimes = PlayerPrefs.GetInt(EPlayerPrefKey.NotifiWindowPopTimes, 0)
  if 3 <= popTimes then
    return  -- 弹窗次数已达上限（3次）
  end

  -- 7. 重复弹窗检查
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.NotificationOpenWindow) then
    return  -- 通知窗口已存在，避免重复弹出
  end

  -- 8. 执行弹窗
  GM.UIManager:OpenViewWhenIdle(UIPrefabConfigName.NotificationOpenWindow, eNotiSceneDescKey)
end
```

### 2. 弹窗触发条件详解

#### 2.1 基础条件检查

**自动运行模式检查**：
- 在自动运行或测试模式下不显示弹窗
- 避免影响自动化测试和演示

**系统权限检查**：
- 如果系统通知权限已开启，则不需要弹窗提醒
- 直接返回，避免不必要的用户打扰

**用户设置检查**：
- 检查用户是否主动关闭了通知功能
- 尊重用户的选择，不强制弹窗

#### 2.2 场景特定条件

**体力不足场景**：
```lua
if eNotiSceneDescKey == ENotiSceneDescKey.EnergyEmpty then
  local energy = GM.EnergyModel:GetEnergy(EnergyType.Main)
  if energy >= ItemSpread.GetCostEnergyNum() then
    return  -- 体力充足时不提醒
  end
end
```

- 只有在体力真正不足时才提醒开启通知
- 避免在体力充足时的无效提醒
- `ItemSpread.GetCostEnergyNum()` 获取当前操作所需的体力值

#### 2.3 时间和频率限制

**72小时冷却机制**：
- 两次弹窗之间至少间隔72小时（259200秒）
- 防止频繁打扰用户
- 通过 `LastNotifiWindowPopTime` 记录上次弹窗时间

**3次弹窗上限**：
- 每个用户最多弹出3次通知开启提醒
- 通过 `NotifiWindowPopTimes` 记录弹窗次数
- 避免对拒绝开启通知的用户造成持续骚扰

#### 2.4 重复检查机制

**窗口存在性检查**：
- 检查通知开启窗口是否已经显示
- 避免同时弹出多个相同窗口
- 确保用户界面的整洁性

### 3. 弹窗触发时机

系统在多个关键时机尝试触发通知开启弹窗：

#### 3.1 体力不足时触发

```lua
-- 在ItemSpread:OnTap()中的触发逻辑
if not self:IsDisposable() and self:ShowCountDown() and self:GetStorageRestNumber() == 0 then
  if GM.TutorialModel:IsTutorialFinished(ETutorialId.PDCD1) and
     GM.TutorialModel:IsTutorialFinished(ETutorialId.PDCD2) then
    local restDuration = self:GetTimerDuration() + self:GetStartTimer() - GM.GameModel:GetServerTime()
    if 1200 <= restDuration then  -- 冷却时间超过20分钟
      GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.ItemCoolDown)
    end
  end
end
```

**触发条件**：
- 物品不是一次性的
- 显示倒计时
- 存储数量为0（已用完）
- 教程已完成
- 剩余冷却时间超过20分钟

#### 3.2 购买体力窗口关闭时触发

```lua
-- 在BuyEnergyWindow:OnCloseFinish()中的触发逻辑
if self.Type == EnergyType.Main then
  GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.EnergyEmpty)
  if self.m_popupForLackingEnergy and not self.m_free2Refill then
    GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.LackEnergy)
  end
end
```

**触发时机**：
- 主体力购买窗口关闭时
- 用户可能因为体力不足而需要通知提醒

#### 3.3 活动结束窗口关闭时触发

```lua
-- 在活动窗口关闭时的触发逻辑
function DashActivityNoticeWindow:OnCloseFinish()
  DashActivityBaseWindow.OnCloseFinish(self)
  GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.CashDash)
end
```

**触发时机**：
- 各种限时活动的通知窗口关闭时
- 提醒用户开启通知以便及时了解活动信息

### 4. 弹窗重置机制

系统提供了弹窗计数重置机制，允许在特定条件下重新开始弹窗提醒。

```lua
-- 弹窗计数重置逻辑
function NotificationModel:_UpdateAuthRequestTimesInfo()
  local nLocalTimesKey = PlayerPrefs.GetInt(EPlayerPrefKey.NotificationClearTimesKey, 0)
  local nServerTimesKey = GM.ConfigModel:GetGeneralConfByType(EGeneralConfType.NotifyRemindTimes)
  if nServerTimesKey ~= nil and nServerTimesKey ~= nLocalTimesKey then
    PlayerPrefs.SetInt(EPlayerPrefKey.NotificationClearTimesKey, nServerTimesKey)
    PlayerPrefs.SetInt(EPlayerPrefKey.NotifiWindowPopTimes, 0)  -- 重置弹窗次数
  end
end
```

**重置机制**：
- 服务器可以通过配置触发弹窗计数重置
- 当服务器的 `NotifyRemindTimes` 值发生变化时，重置本地弹窗次数
- 允许在游戏更新或特殊情况下重新提醒用户开启通知

## 权限管理系统

### 1. 通知权限请求流程

系统提供了完整的通知权限请求和管理机制。

```lua
-- 权限请求主函数
function NotificationModel:TryOpenNotification(direct2Setting)
  PlayerPrefs.SetInt(EPlayerPrefKey.OpenNotification, 1)  -- 标记用户意图开启通知

  if not PlatformInterface.IsNotificationsEnabled() then
    if not self:IsSystemNotiRequsted() then
      -- 首次请求权限
      self:RequestAuthorization()
    elseif direct2Setting then
      -- 直接跳转到系统设置
      PlatformInterface.OpenAppSettings()
    else
      -- 显示教程窗口
      GM.UIManager:OpenView(UIPrefabConfigName.NotificationTutorialWindow)
    end
  else
    -- 权限已开启，显示成功提示
    GM.UIManager:ShowPromptWithKey("notification_on_hint")
  end
end

-- 权限请求函数
function NotificationModel:RequestAuthorization()
  PlayerPrefs.SetInt(EPlayerPrefKey.SystemNotiRequsted, 1)  -- 标记已请求过权限
  CSNotificationManager:RequestAuthorization(function(text)
    Log.Info("RequestAuthorization response " .. text, "NotificationModel")
    local granted = string.sub(text, 1, 1) == "1"
    local token = string.sub(text, 3)
    if granted and DeviceInfo.IsSystemIOS() and GM ~= nil then
      GM.PushTokenModel:UpdatePushToken(token)  -- iOS设备更新推送令牌
    end
  end)
end
```

### 2. 权限状态管理

系统维护多个状态来跟踪通知权限的各个方面。

**权限状态类型**：
- **用户意图状态**：`OpenNotification`（用户是否想要开启通知）
- **系统权限状态**：`PlatformInterface.IsNotificationsEnabled()`（系统是否授予权限）
- **请求历史状态**：`SystemNotiRequsted`（是否已经请求过权限）

**状态组合处理**：
```lua
-- 权限状态检查逻辑
local userWantsNotification = PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1) == 1
local systemPermissionGranted = PlatformInterface.IsNotificationsEnabled()
local hasRequestedBefore = self:IsSystemNotiRequsted()

if userWantsNotification and not systemPermissionGranted then
  if not hasRequestedBefore then
    -- 首次请求：直接调用系统权限请求
    self:RequestAuthorization()
  else
    -- 已请求过但被拒绝：引导用户到设置页面
    if direct2Setting then
      PlatformInterface.OpenAppSettings()
    else
      GM.UIManager:OpenView(UIPrefabConfigName.NotificationTutorialWindow)
    end
  end
end
```

### 3. 推送令牌管理

`PushTokenModel` 负责管理设备的推送令牌，用于服务器推送。

```lua
-- 推送令牌更新函数
function PushTokenModel:UpdatePushToken(token)
  local userId = GM.UserModel:GetUserId()
  if self.m_pushToken ~= token or self.m_pushTokenUserId ~= userId then
    self.m_pushToken = token
    PlayerPrefs.SetString(EPlayerPrefKey.PushToken, token)
    self.m_pushTokenUserId = userId
    PlayerPrefs.SetInt(EPlayerPrefKey.PushTokenUserId, userId)

    local tokenKey = DeviceInfo.IsSystemIOS() and LogPushTokenKey.IOS or LogPushTokenKey.Android
    GM.BIManager:LogPushToken(tokenKey, token)  -- 记录到数据分析系统
    Log.Info("Notification PushToken[New]: token " .. token .. " userId " .. userId)
  else
    Log.Info("Notification PushToken[Same]: token " .. token .. " userId " .. userId)
  end
end

-- iOS推送令牌获取
function PushTokenModel:_TryUpdatePushTokenIOS()
  if GM.NotificationModel:IsSystemNotiRequsted() or PlatformInterface.IsNotificationsEnabled() then
    GM.NotificationModel:RequestAuthorization()  -- 通过权限请求获取令牌
  end
end

-- Android推送令牌获取
function PushTokenModel:_TryUpdatePushTokenAndroid()
  local func = function()
    local pushToken = DeviceInfo.GetAndroidPushToken()
    if pushToken ~= "" then
      self:UpdatePushToken(pushToken)
      Scheduler.UnscheduleTarget(self)  -- 获取成功后停止轮询
    end
  end
  Scheduler.Schedule(func, self, 1, 10)  -- 每秒轮询，最多10次
end
```

**令牌管理特点**：
- **平台差异处理**：iOS和Android使用不同的令牌获取方式
- **用户绑定**：令牌与用户ID绑定，支持多用户场景
- **变更检测**：只有在令牌或用户发生变化时才更新
- **数据上报**：将令牌信息上报到数据分析系统

### 4. 权限状态监控

系统定期监控和记录通知权限状态，用于数据分析。

```lua
-- 权限状态记录函数
function NotificationModel:LogStateBI()
  local lastTime = PlayerPrefs.GetInt(EPlayerPrefKey.NotifiStateKey, 0)
  local serTime = GM.GameModel:GetServerTime()

  -- 每24小时记录一次状态
  if serTime // NotificationModel.NotifyInterval > lastTime // NotificationModel.NotifyInterval then
    local gameSw = PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1)  -- 游戏内开关状态
    local systemSw = PlatformInterface.IsNotificationsEnabled() and 1 or 0  -- 系统权限状态
    PlayerPrefs.SetInt(EPlayerPrefKey.NotifiStateKey, serTime)
    GM.BIManager:LogAction(EBIType.NotificationState, {gameSw = gameSw, systemSw = systemSw})
  end
end
```

**监控机制**：
- **定期记录**：每24小时记录一次权限状态
- **双重状态**：同时记录游戏内开关和系统权限状态
- **数据分析**：为产品优化提供权限开启率数据

## 配置系统

### 1. 服务器配置结构

通知系统的行为主要由服务器配置控制，支持动态调整。

**配置键定义**：
```lua
ServerConfigKey = {
  Notify = "notify"  -- 通知配置键
}
```

**配置结构示例**：
```json
{
  "notify": [
    {
      "scene": ["EnergyRefill", "ComeBack_24", "ItemCooldown"],
      "interval": 3600,
      "maxNum": 2,
      "pushtimetype": 1,
      "pushtime": [9, 21]
    }
  ]
}
```

**配置字段说明**：
- `scene`: 支持的通知场景列表
- `interval`: 时间间隔（秒），用于分组通知
- `maxNum`: 每个时间段内的最大通知数量
- `pushtimetype`: 推送时间类型（1表示使用时间窗口）
- `pushtime`: 推送时间窗口 [开始小时, 结束小时]

### 2. 本地配置结构

本地配置主要用于定义通知的文本内容。

```lua
-- 通知文本配置
return {
  ComeBack = {
    IsMatch = 1,  -- 支持模糊匹配
    TitleKey = "push_come_back_title",
    DescKey = "push_come_back_desc"
  },
  EnergyRefill = {
    TitleKey = "push_energy_title",
    DescKey = "push_energy_desc"
  },
  ItemCooldown = {
    TitleKey = "push_cooldown_title",
    DescKey = "push_cooldown_desc"
  }
}
```

**配置特点**：
- **本地化支持**：通过文本键支持多语言
- **模糊匹配**：`IsMatch = 1` 的配置支持场景名称的模糊匹配
- **灵活扩展**：可以轻松添加新的通知场景和文本

### 3. 文本获取机制

系统提供了灵活的文本获取机制，支持精确匹配和模糊匹配。

```lua
-- 文本获取函数
function NotificationModel:GetTextTileAndDesc(strScene)
  if self.m_TextKeyConfig == nil then
    Log.Assert(false, "没有配置Push功能的文案文件")
    return "", ""
  end

  -- 精确匹配
  if self.m_TextKeyConfig[strScene] ~= nil then
    return self.m_TextKeyConfig[strScene].TitleKey, self.m_TextKeyConfig[strScene].DescKey
  end

  -- 模糊匹配
  for strKey, oOneCfg in pairs(self.m_TextKeyConfig) do
    if oOneCfg.IsMatch == 1 then
      local bFind = string.find(strScene, strKey)
      if bFind then
        local strTileKey = oOneCfg.TitleKey == nil and "" or oOneCfg.TitleKey
        local strDescKey = oOneCfg.DescKey == nil and "" or oOneCfg.DescKey
        return strTileKey, strDescKey
      end
    end
  end

  return "", ""
end
```

**匹配策略**：
1. **精确匹配优先**：首先尝试精确匹配场景名称
2. **模糊匹配兜底**：如果精确匹配失败，使用模糊匹配
3. **默认值处理**：如果都匹配失败，返回空字符串

## UI系统

### 1. 通知开启窗口

`NotificationOpenWindow` 是主要的通知权限请求窗口，在满足触发条件时自动弹出。

```lua
-- 通知开启窗口初始化
function NotificationOpenWindow:Init(descKey)
  self.m_descKey = descKey

  -- 记录弹窗时间和次数
  PlayerPrefs.SetInt(EPlayerPrefKey.LastNotifiWindowPopTime, GM.GameModel:GetServerTime())
  local times = PlayerPrefs.GetInt(EPlayerPrefKey.NotifiWindowPopTimes, 0)
  PlayerPrefs.SetInt(EPlayerPrefKey.NotifiWindowPopTimes, times + 1)

  -- 设置描述文本
  self.m_descriptionText.text = GM.GameTextModel:GetText(descKey)

  -- 记录弹窗行为
  self:LogWindowAction(EBIType.UIActionType.Open, {EBIReferType.AutoPopup})

  -- 监听应用前台事件
  EventDispatcher.AddListener(EEventType.ApplicationWillEnterForeground, self, self.ApplicationWillEnterForeground)
end

-- 开启前检查
function NotificationOpenWindow:BeforeOpenCheck(descKey)
  if descKey ~= ENotiSceneDescKey.EnergyEmpty then
    return true
  end
  -- 体力不足场景的额外检查
  return GM.PropertyDataManager:GetPropertyNum(EPropertyType.Energy) < 10
end

-- 应用回到前台时的处理
function NotificationOpenWindow:ApplicationWillEnterForeground()
  if PlatformInterface.IsNotificationsEnabled() then
    self:Close()  -- 如果权限已开启，自动关闭窗口
  end
end
```

**窗口特点**：
- **自动统计**：自动记录弹窗时间和次数
- **动态文本**：根据场景显示不同的描述文本
- **智能关闭**：当用户在设置中开启权限后自动关闭
- **数据记录**：记录用户的操作行为用于分析

### 2. 通知教程窗口

`NotificationTutorialWindow` 用于引导用户到系统设置中开启通知权限。

```lua
-- 通知教程窗口
function NotificationTutorialWindow:Init()
  EventDispatcher.AddListener(EEventType.ApplicationWillEnterForeground, self, self.ApplicationWillEnterForeground)
end

function NotificationTutorialWindow:OnTurnOnButtonClicked()
  GM.NotificationModel:TryOpenNotification(true)  -- 直接跳转到设置
end

function NotificationTutorialWindow:ApplicationWillEnterForeground()
  if PlatformInterface.IsNotificationsEnabled() then
    self:Close()  -- 权限开启后自动关闭
  end
end
```

**教程窗口功能**：
- **引导跳转**：点击按钮直接跳转到系统设置页面
- **状态监听**：监听应用前台事件，检测权限变化
- **自动关闭**：权限开启后自动关闭窗口

### 3. 设置窗口集成

通知功能集成到游戏的设置窗口中，提供用户主动控制的入口。

```lua
-- 设置窗口中的通知开关
function SettingWindow:_InitNotificationToggle()
  local clickCallback = function(toggle)
    local isOn = not toggle:IsOn()
    GM.BIManager:LogUI(self.name, isOn and EBIType.SetNoticeAction.Open or EBIType.SetNoticeAction.Close)
    if isOn then
      GM.NotificationModel:TryOpenNotification()  -- 尝试开启通知
    else
      PlayerPrefs.SetInt(EPlayerPrefKey.OpenNotification, 0)  -- 关闭通知
      GM.UIManager:ShowPromptWithKey("notification_off_hint")
    end
  end

  local checker = function()
    -- 检查开关状态：用户设置 AND 系统权限
    return PlayerPrefs.GetInt(EPlayerPrefKey.OpenNotification, 1) ~= 0 and
           PlatformInterface.IsNotificationsEnabled()
  end

  self.m_notificationToggle:Init(clickCallback, checker)
end
```

**设置集成特点**：
- **双重状态**：开关状态同时反映用户设置和系统权限
- **行为记录**：记录用户的开关操作
- **即时反馈**：提供开启/关闭的即时提示信息

## 系统特点总结

### 1. 核心特性

1. **多层次权限管理**
   - 用户意图层：游戏内开关设置
   - 系统权限层：操作系统通知权限
   - 请求历史层：避免重复请求权限

2. **智能弹窗触发**
   - 多重条件检查：时间、次数、场景、权限状态
   - 72小时冷却机制：避免频繁打扰
   - 3次弹窗上限：尊重用户选择

3. **灵活的通知调度**
   - 优先级过滤：确保重要通知优先发送
   - 时间窗口限制：避免在不合适的时间发送
   - 数量控制：防止通知过载

4. **完善的配置系统**
   - 服务器配置：支持动态调整通知策略
   - 本地配置：支持多语言文本管理
   - 模糊匹配：提高配置的灵活性

### 2. 技术亮点

1. **助手模式设计**
   - 每种通知类型对应独立的助手类
   - 统一接口规范，便于扩展
   - 支持复杂的通知生成逻辑

2. **状态机管理**
   - 清晰的权限状态转换
   - 多平台差异处理
   - 异常情况的优雅处理

3. **数据驱动架构**
   - 服务器配置控制通知行为
   - 本地配置管理文本内容
   - 支持热更新和A/B测试

4. **用户体验优化**
   - 智能的触发时机选择
   - 非侵入式的权限请求
   - 完善的状态反馈机制

### 3. 平衡性设计

1. **用户体验与功能需求的平衡**
   - 在不打扰用户的前提下提供必要的通知功能
   - 通过时间和次数限制避免过度骚扰
   - 提供明确的关闭选项

2. **系统性能与功能完整性的平衡**
   - 高效的通知生成和过滤算法
   - 合理的内存和CPU使用
   - 最小化对游戏主流程的影响

3. **灵活性与稳定性的平衡**
   - 支持动态配置的同时保证系统稳定
   - 完善的异常处理和降级机制
   - 向后兼容的配置格式

该通知系统通过精心设计的触发规则、完善的权限管理和灵活的配置机制，为玩家提供了既实用又不打扰的通知体验，在提升用户留存的同时保持了良好的用户体验。
