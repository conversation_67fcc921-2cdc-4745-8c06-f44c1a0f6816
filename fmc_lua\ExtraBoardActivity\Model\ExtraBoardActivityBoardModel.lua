ExtraBoardActivityBoardModel = setmetatable({}, BaseUIBoardModel)
ExtraBoardActivityBoardModel.__index = ExtraBoardActivityBoardModel

function ExtraBoardActivityBoardModel.Create(itemDataTable, itemLayerDataTable, itemCacheDataTable, initCodeMap, width, height, activityType)
  local boardModel = setmetatable({}, ExtraBoardActivityBoardModel)
  boardModel:Init(itemDataTable, itemLayerDataTable, itemCacheDataTable, initCodeMap, width, height, activityType)
  return boardModel
end

function ExtraBoardActivityBoardModel:Init(itemDataTable, itemLayerDataTable, itemCacheDataTable, initCodeMap, width, height, activityType)
  self.m_activityModel = GM.ActivityManager:GetModel(activityType)
  self:_LoadCobwebItemIds()
  BaseUIBoardModel.Init(self, EGameMode.ExtraBoard, itemDataTable, itemLayerDataTable, itemCacheDataTable, initCodeMap, width, height, activityType)
  EventDispatcher.AddListener(EEventType.ExtraBoardEnterNextCobwebRound, self, self.EnterNextCobwebRound)
end

function ExtraBoardActivityBoardModel:_CreateItemLayerModel()
  return ExtraBoardItemLayerModel.Create(self, self.m_itemLayerDataTable, self.m_itemManager, self.m_initCodeMap)
end

function ExtraBoardActivityBoardModel:Destroy()
  BaseUIBoardModel.Destroy(self)
  self.m_itemLayerModel:Destroy()
end

function ExtraBoardActivityBoardModel:FindEmptyPositionForCacheItem()
  local position
  local maxX = self:GetHorizontalTiles()
  local maxY = self:GetVerticalTiles()
  local centerPos = self:CreatePosition((maxX + 1) // 2, maxY)
  if self:GetItem(centerPos, true) == nil then
    position = centerPos
  else
    position = self:FindEmptyPositionInSpreadOrder(centerPos)
  end
  return position
end

function ExtraBoardActivityBoardModel:_LoadCobwebItemIds()
  if self.m_activityModel == nil or not self.m_activityModel:IsCobwebOpen() then
    return
  end
end

function ExtraBoardActivityBoardModel:GetCobwebItemCodes()
  return self.m_activityModel and self.m_activityModel:GetCobwebItemList()
end

function ExtraBoardActivityBoardModel:GetCobwebItems()
  return self.m_itemLayerModel:GetCobwebItems()
end

function ExtraBoardActivityBoardModel:IsCobwebItem(item)
  return self.m_itemLayerModel:IsCobwebItem(item)
end

function ExtraBoardActivityBoardModel:IsCobwebOpen()
  return not Table.IsEmpty(self:GetCobwebItemCodes())
end

function ExtraBoardActivityBoardModel:HasFinishedAllMainCobweb()
  local items = self:FilterItems(function(item)
    return item:GetComponent(ItemCobweb) ~= nil or item:GetComponent(ItemPaperBox) ~= nil
  end, 1)
  return Table.IsEmpty(items)
end

function ExtraBoardActivityBoardModel:EnterNextCobwebRound()
  self:_LoadCobwebItemIds()
  self.m_itemLayerModel:LoadCobwebItems()
end

function ExtraBoardActivityBoardModel:GetCobwebItemByPosition(position)
  return self.m_itemLayerModel:GetCobwebItem(position)
end

function ExtraBoardActivityBoardModel:GetMergeAllIgnoreItems()
  local mergeAllIgnoredItems = BaseUIBoardModel.GetMergeAllIgnoreItems(self)
  local cobwebItems = self:GetCobwebItems()
  if not Table.IsEmpty(cobwebItems) then
    for _, item in ipairs(cobwebItems) do
      mergeAllIgnoredItems[item] = true
    end
  end
  return mergeAllIgnoredItems
end

function ExtraBoardActivityBoardModel:GetBoardItemsForMerge()
  if not self:IsCobwebOpen() or not self:HasFinishedAllMainCobweb() then
    return BaseUIBoardModel.GetBoardItemsForMerge(self)
  end
  return self.m_itemLayerModel:GetAllItemsWithCobweb()
end

function ExtraBoardActivityBoardModel:_MergeCobwebItem(item, targetItem)
  local newItem = self:ReplaceItem(item, item:GetMergedType(), ItemModelHelper.MergeCost(item, {}), false)
  self:_RemoveCobwebItem(targetItem)
  local mergeMessage = {
    Source = item,
    Target = targetItem,
    New = newItem
  }
  self.event:Call(BoardEventType.MergeCobwebItem, mergeMessage)
  mergeMessage.GameMode = self:GetGameMode()
  EventDispatcher.DispatchEvent(EEventType.ItemMerged, mergeMessage)
  local ext = {extraCobweb = 1}
  PlatformInterface.Vibrate(EVibrationType.Medium)
  GM.BIManager:LogMerge(item:GetType(), newItem:GetCode(), ext, self:GetGameMode())
  self.m_activityModel:TryEnterNextCobwebRound()
end

function ExtraBoardActivityBoardModel:_RemoveCobwebItem(item)
  self.m_itemLayerModel:RemoveCobwebItem(item)
  self.m_activityModel:RemoveCobwebItem(item:GetPosition():GetX())
end

function ExtraBoardActivityBoardModel:Drag2CobwebItem(item, targetItem)
  if self:CanTwoItemsMerge(item, targetItem) then
    local bItemSpreadFinished = true
    for _, v in pairs({item, targetItem}) do
      local itemSpread = v:GetComponent(ItemSpread)
      if itemSpread ~= nil and not itemSpread:IsSpreadFinish() then
        bItemSpreadFinished = false
        break
      end
    end
    if not bItemSpreadFinished then
      GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "extraboard_tip1_title", "extraboard_tip1_desc", "common_button_ok", "common_button_cancel", function(window)
        if self:CheckItemStillInPosition(item) then
          self:_MergeCobwebItem(item, targetItem)
        end
        window:Close()
      end, function(window)
        if self:CheckItemStillInPosition(item) then
          item:SetPosition(item:GetPosition())
        end
        window:Close()
      end, false)
    else
      self:_MergeCobwebItem(item, targetItem)
    end
    return
  end
  item:SetPosition(item:GetPosition())
  GM.AudioModel:PlayEffect(AudioFileConfigName.sfxDragItemEnd)
  PlatformInterface.Vibrate(EVibrationType.Medium)
end

function ExtraBoardActivityBoardModel:DragItem(item, targetPosition)
  BaseUIBoardModel.DragItem(self, item, targetPosition)
  local cbItem = self.m_itemLayerModel:GetCobwebItem(targetPosition)
  if cbItem ~= nil then
    self:Drag2CobwebItem(item, cbItem)
  end
end
