require("Model.Bundle.BundleBaseModel")
require("Model.Bundle.BundleActivityBaseModel")
require("Model.Bundle.BundleNormalModel")
require("Model.Bundle.BundleMultiTierModel")
require("Model.Bundle.BundleChainModel")
require("Model.Bundle.BundleOnePlusNModel")
BundleVirtualDBTableName = {
  Starter = "starter",
  Energy = "energy",
  CD = "cd",
  CDFill = "cdFill",
  OrderGroup = "orderGroup",
  MultiTier = "multiTier",
  Chain = "chain",
  OnePlusN = "onePlusN"
}
local BundleModelInfo = {
  [EBundleType.Starter] = {
    class = BundleNormalModel,
    dbName = BundleVirtualDBTableName.Starter
  },
  [EBundleType.Energy] = {
    class = BundleNormalModel,
    dbName = BundleVirtualDBTableName.Energy
  },
  [EBundleType.CD] = {
    class = BundleNormalModel,
    dbName = BundleVirtualDBTableName.CD
  },
  [EBundleType.CDFill] = {
    class = BundleNormalModel,
    dbName = BundleVirtualDBTableName.CDFill
  },
  [EBundleType.OrderGroup] = {
    class = BundleNormalModel,
    dbName = BundleVirtualDBTableName.OrderGroup
  },
  [EBundleType.MultiTier] = {
    class = BundleMultiTierModel,
    dbName = BundleVirtualDBTableName.MultiTier
  },
  [EBundleType.Chain] = {
    class = BundleChainModel,
    dbName = BundleVirtualDBTableName.Chain
  },
  [EBundleType.OnePlusN] = {
    class = BundleOnePlusNModel,
    dbName = BundleVirtualDBTableName.OnePlusN
  }
}
BundleManager = {}
BundleManager.__index = BundleManager

function BundleManager:Init()
  VIRTUAL_DEFINE_SYNC_DATA(self, "BundleDataTable", GM.DBTableManager:GetTable(EDBTableConfigs.Bundles))
  ModelHelper.DefineSyncData(self, "BundleMetaDataTable", GM.DBTableManager:GetTable(EDBTableConfigs.BundleMeta))
  ModelHelper.DefineDispatchEvent(self, "LateInit")
  ModelHelper.DefineDispatchEvent(self, "UpdateAfterLoginFinished")
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self.OnLoginFinished)
  EventDispatcher.AddListener(EEventType.CloseView, self, self.OnCloseView)
  EventDispatcher.AddListener(EEventType.OrderFinished, self, self._OnOrderFinished)
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self._OnMainTaskFinished)
end

function BundleManager:Destroy()
  if self.m_models ~= nil then
    for _, model in pairs(self.m_models) do
      model:Destroy()
    end
  end
  EventDispatcher.RemoveTarget(self)
end

function BundleManager:OnSyncDataFinished()
  self.m_models = {}
  local model, virtualDBTable
  for bundleType, info in pairs(BundleModelInfo) do
    model = setmetatable({}, info.class)
    virtualDBTable = VirtualDBTable.Create(self.m_bundleDataTable, info.dbName)
    model:Init(bundleType, virtualDBTable)
    self.m_models[bundleType] = model
  end
  self.m_inited = true
end

function BundleManager:OnSceneViewLoaded()
  self.m_bSceneViewLoaded = true
  if self.m_models ~= nil then
    for _, model in pairs(self.m_models) do
      model:OnSceneViewLoaded()
    end
  end
  if GameConfig.IsTestMode() and not self:CheckTriggerConfigValid() then
    Log.Error("BundleTrigger 同一时间内相同的trigger配置了不同的popOrder或popNum请检查!")
  end
  self:TryStartBundlePopupChain(EBundleTriggerType.Login)
end

function BundleManager:LoadServerConfig()
  local serverConfig = GM.ConfigModel:GetServerConfig(ServerConfigKey.BundleController)
  local md5 = GM.ConfigModel:GetServerConfigMD5(ServerConfigKey.BundleController)
  if self.m_md5 ~= md5 then
    self.m_md5 = md5
    self.m_mapType2Configs = {}
    for _, config in ipairs(serverConfig or {}) do
      if self.m_mapType2Configs[config.specialType] == nil then
        self.m_mapType2Configs[config.specialType] = {}
      end
      table.insert(self.m_mapType2Configs[config.specialType], config)
    end
  end
  if not Table.IsEmpty(self.m_models) then
    for _, model in pairs(self.m_models) do
      if model.LoadServerConfig ~= nil then
        model:LoadServerConfig()
      end
    end
  end
end

function BundleManager:GetServerConfigs(bundleType)
  return self.m_mapType2Configs and self.m_mapType2Configs[bundleType]
end

function BundleManager:UpdatePerSecond()
  if self.m_models ~= nil then
    for _, model in pairs(self.m_models) do
      model:UpdatePerSecond()
    end
  end
end

function BundleManager:GetModel(bundleType)
  return self.m_models and self.m_models[bundleType]
end

local activeGroups = {}

function BundleManager:GetActiveGroupDatas(arrSequence)
  while 0 < #activeGroups do
    table.remove(activeGroups)
  end
  if arrSequence == nil then
    for _, model in pairs(self.m_models) do
      model:GetActiveGroupDatas(activeGroups)
    end
  else
    for _, bundleType in ipairs(arrSequence) do
      if self:GetModel(bundleType) then
        self:GetModel(bundleType):GetActiveGroupDatas(activeGroups, bundleType)
      end
    end
  end
  return activeGroups
end

function BundleManager:Buy(bundleType, bundleData, successCallback, bIgnoreRecord)
  local bundleModel = self:GetModel(bundleType)
  local groupData = bundleData:GetGroupData()
  local groupId = groupData:GetGroupId()
  if bundleData:GetPurchaseId() == nil then
    GM.UIManager:ShowPromptWithKey("bundle_buy_fail_tips")
    GM.BIManager:LogErrorInfo(EBIProjectType.BundleBuyFailed, groupId .. " purchaseId missing")
    return
  end
  local purchaseId = bundleData:GetPurchaseId()
  local bundleId = bundleData:GetBundleId()
  local bundleInfo = {
    groupId = groupId,
    bundleId = bundleId,
    bundleType = bundleType,
    rewards = bundleData:GetGoods()
  }
  if not bIgnoreRecord then
    self:_RecordPurchaseInfo(purchaseId, bundleInfo)
  end
  GM.InAppPurchaseModel:StartPurchase(purchaseId, function()
    if not bIgnoreRecord then
      self:_ReleasePurchaseInfo(purchaseId)
    end
    RewardApi.AcquireRewardsLogic(bundleData:GetGoods(), EPropertySource.Buy, EBIType.IAP, EGameMode.Board, CacheItemType.Stack)
    bundleModel:OnPurchaseFinished(groupId, bundleId)
    if successCallback ~= nil then
      successCallback(bundleInfo.rewards)
    end
  end, EBIType.BundleBuy)
end

function BundleManager:GetBundleConfigData(eIAPType)
  local configData
  for _, model in pairs(self.m_models) do
    configData = model:GetBundleConfigData(eIAPType)
    if configData ~= nil then
      return configData
    end
  end
  return configData
end

function BundleManager:OnRestoreIAPRewards(eIAPType)
  for _, model in pairs(self.m_models) do
    if model and model.OnRestoreIAPRewards then
      local isOk, result = SafeCall(model.OnRestoreIAPRewards, nil, model, eIAPType)
      if isOk and result then
        return true
      end
    end
  end
  local recordBundleInfo = self:_GetRecordPurchaseInfo(eIAPType)
  if recordBundleInfo ~= nil then
    local model = self:GetModel(recordBundleInfo.bundleType)
    model:OnPurchaseFinished(recordBundleInfo.groupId, recordBundleInfo.bundleId)
    self:_ReleasePurchaseInfo(eIAPType)
    if self:CheckNeedGiveGems(recordBundleInfo.rewards) then
      return false
    end
    RewardApi.CryptRewards(recordBundleInfo.rewards, true)
    self:_AcquireRewards(recordBundleInfo.rewards)
    return true
  end
  local configData = self:GetBundleConfigData(eIAPType)
  if configData == nil or self:CheckNeedGiveGems(configData:GetGoods()) then
    return false
  end
  self:_AcquireRewards(configData:GetGoods())
  return true
end

function BundleManager:CheckNeedGiveGems(rewards)
  for _, v in ipairs(rewards or {}) do
    if not RewardApi.CheckRewardTypeValid(v[PROPERTY_TYPE]) then
      return true
    end
  end
  return false
end

function BundleManager:_RecordPurchaseInfo(purchaseId, bundleInfo)
  local k = self:_GetPurchaseMetaKey(purchaseId)
  if k ~= nil then
    self.m_bundleMetaDataTable:Set(k, BundleColumnValue, StringUtil.Replace(json.encode(bundleInfo), ",", "@"))
    return true
  end
  return false
end

function BundleManager:_GetRecordPurchaseInfo(purchaseId)
  local k = self:_GetPurchaseMetaKey(purchaseId)
  if k ~= nil then
    local v = self.m_bundleMetaDataTable:GetValue(k, BundleColumnValue)
    if not StringUtil.IsNilOrEmpty(v) then
      v = StringUtil.Replace(v, "@", ",")
      return json.decode(v)
    end
  end
  return nil
end

function BundleManager:_ReleasePurchaseInfo(purchaseId)
  local k = self:_GetPurchaseMetaKey(purchaseId)
  if k ~= nil and self.m_bundleMetaDataTable:HasRow(k) then
    self.m_bundleMetaDataTable:Remove(k)
  end
end

function BundleManager:_GetPurchaseMetaKey(purchaseId)
  return purchaseId ~= nil and purchaseId .. BundleMetaPurchaseInfoKeySuffix or nil
end

function BundleManager:GetBundleMetaDBTable()
  return self.m_bundleMetaDataTable
end

function BundleManager:_AcquireRewards(rewards)
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Buy, EBIType.Restore, EGameMode.Board, CacheItemType.Stack)
  if 0 < #rewards then
    GM.UIManager:OpenViewWhenIdle(UIPrefabConfigName.RewardWindow, rewards, "rewards_window_title_shop", true)
  end
end

function BundleManager:TryTriggerActivityBundle(eTriggerType, triggerArg, lastBundleType, bOpenImmediately)
  local bStartCheck = lastBundleType == nil
  local arrOrderList = self:GetBundleTriggerOrderList(eTriggerType)
  for _, bundleType in ipairs(arrOrderList) do
    if bStartCheck then
      local bundleModel = self:GetModel(bundleType)
      if bundleModel and bundleModel.TryTriggerBundle then
        local bTriggerd, viewName = bundleModel:TryTriggerBundle(eTriggerType, triggerArg, bOpenImmediately)
        if bTriggerd and viewName then
          return bTriggerd, viewName, bundleType
        end
      end
    elseif bundleType == lastBundleType then
      bStartCheck = true
    end
  end
  return false
end

function BundleManager:OnLackGem(lackNum, endFunc)
  local originGemNum = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
  local checkFunc = function()
    local curGemNum = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
    return curGemNum - originGemNum < lackNum
  end
  local openShopWindowFunc = function()
    if checkFunc() and endFunc ~= nil then
      endFunc()
    end
  end
  if self:TryStartBundlePopupChain(EBundleTriggerType.LackGem, lackNum, checkFunc, openShopWindowFunc, true) then
    return true
  end
  return false
end

function BundleManager:OnItemRecharge(itemModel)
  local itemDataModel = GM.ItemDataModel
  local arrItem = GM.MainBoardModel:FilterItems(function(itemModel)
    if not itemDataModel:IsPd(itemModel:GetType()) then
      return false
    end
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and itemSpread:ShowCountDown() then
      return true
    end
    return false
  end)
  GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.PdCDNumber, #arrItem)
  local targetChainId = GM.ItemDataModel:GetChainId(itemModel:GetType())
  arrItem = GM.MainBoardModel:FilterItems(function(itemModel)
    local chainId = itemDataModel:GetChainId(itemModel:GetType())
    if targetChainId ~= chainId then
      return false
    end
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and not itemSpread:ShowCountDown() then
      return true
    end
  end)
  if Table.IsEmpty(arrItem) then
    GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.ClickCDGenerator)
  end
end

function BundleManager:GetBundleTriggerConfig(eTriggerType)
  local popOrderConfig
  for _, model in pairs(self.m_models) do
    popOrderConfig = model:GetTargetPopOrderConfig(eTriggerType)
    if popOrderConfig ~= nil then
      return popOrderConfig
    end
  end
end

function BundleManager:GetBundleTriggerMaxNum(eTriggerType)
  local config = self:GetBundleTriggerConfig(eTriggerType)
  local defaultNum = eTriggerType ~= EBundleTriggerType.Login and 1 or math.maxinteger
  return config and config.popNum or defaultNum
end

function BundleManager:GetBundleTriggerOrderList(eTriggerType)
  local config = self:GetBundleTriggerConfig(eTriggerType) or {}
  local arrBundleType = config.popOrder or {}
  for _, bundleType in ipairs(BundleTypeOrderList) do
    if not Table.ListContain(arrBundleType, bundleType) then
      table.insert(arrBundleType, bundleType)
    end
  end
  return arrBundleType
end

function BundleManager:CheckTriggerConfigValid()
  local checkFunc = function(a, b)
    if a.popNum ~= b.popNum then
      return false
    end
    local orderListA = a.popOrder or {}
    local orderListB = b.popOrder or {}
    for i = 1, #orderListA do
      if orderListA[i] ~= orderListB[i] then
        return false
      end
    end
    return true
  end
  local arrOrderConfig, firstConfig
  for _, triggerType in pairs(EBundleTriggerType) do
    firstConfig = self:GetBundleTriggerConfig(triggerType)
    if firstConfig ~= nil then
      for _, model in pairs(self.m_models) do
        arrOrderConfig = model:GetAllPopOrderConfig(triggerType)
        if not Table.IsEmpty(arrOrderConfig) then
          for _, config in ipairs(arrOrderConfig) do
            if not checkFunc(firstConfig, config) then
              return false
            end
          end
        end
      end
    end
  end
  return true
end

function BundleManager:OnLoginFinished(msg)
  if msg.bSuccess and self.m_bSceneViewLoaded and GM.SceneManager:GetGameMode() == EGameMode.Main then
    self:TryStartBundlePopupChain(EBundleTriggerType.Login)
  end
end

function BundleManager:_OnOrderFinished(msg)
  local orderModel = GM.MainBoardModel:GetOrderModel()
  if orderModel:CanClaimGroupReward() then
    self:TryStartBundlePopupChain(EBundleTriggerType.FinishOrderGroup)
  end
end

function BundleManager:_OnMainTaskFinished(msg)
  self:TryStartBundlePopupChain(EBundleTriggerType.TaskFinished)
end

function BundleManager:OnCloseView(msg)
  local info = self.m_mapView2MonitorFunc and self.m_mapView2MonitorFunc[msg.name]
  if not Table.IsEmpty(info) then
    info.func(msg.name, info.bundleType)
  end
end

function BundleManager:TryStartBundlePopupChain(eTriggerType, triggerArg, checkFunc, endFunc, bOpenImmediately)
  if not self.m_bSceneViewLoaded then
    Log.Error("SceneViewLoaded 之前不应调用触发礼包！")
    return
  end
  GM.UIManager:ShowTestPrompt("礼包 Trigger:" .. tostring(eTriggerType))
  bOpenImmediately = bOpenImmediately or false
  self.m_mapView2MonitorFunc = {}
  local maxNum = self:GetBundleTriggerMaxNum(eTriggerType)
  local closeViewFunc = function(closeViewName, closeBundleType)
    if checkFunc == nil or checkFunc() then
      if maxNum == 0 then
        if endFunc ~= nil then
          endFunc()
        end
        return
      end
      local originNum = maxNum
      local bTriggerd, viewName, bundleType = self:TryTriggerActivityBundle(eTriggerType, triggerArg, closeBundleType, bOpenImmediately)
      if bTriggerd and viewName then
        self.m_mapView2MonitorFunc[viewName] = {
          func = self.m_mapView2MonitorFunc[closeViewName].func,
          bundleType = bundleType
        }
        maxNum = maxNum - 1
      end
      if viewName ~= closeViewName then
        self.m_mapView2MonitorFunc[closeViewName] = nil
      end
      if originNum == maxNum and endFunc ~= nil then
        endFunc()
      end
    end
  end
  local bTriggerd, viewName, bundleType = self:TryTriggerActivityBundle(eTriggerType, triggerArg, nil, bOpenImmediately)
  if bTriggerd and viewName then
    self.m_mapView2MonitorFunc[viewName] = {func = closeViewFunc, bundleType = bundleType}
    maxNum = maxNum - 1
    return true
  end
  return false
end

function GetBundleHudKey(bundleType, groupId)
  return bundleType .. groupId
end
