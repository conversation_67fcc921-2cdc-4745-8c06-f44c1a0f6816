BaseSceneBoardModel = setmetatable({}, BaseInteractiveBoardModel)
BaseSceneBoardModel.__index = BaseSceneBoardModel

function BaseSceneBoardModel:Init(gameMode, itemManager, itemCacheModel, width, height, orderModel)
  BaseInteractiveBoardModel.Init(self, gameMode, itemManager, itemCacheModel, width, height, orderModel)
  self.m_orderModel = orderModel
end

function BaseSceneBoardModel:Destroy()
  BaseInteractiveBoardModel.Destroy(self)
  self.m_orderModel:Destroy()
end

function BaseSceneBoardModel:_SetItem(position, item, autoUpdateOrderState)
  BaseInteractiveBoardModel._SetItem(self, position, item, autoUpdateOrderState)
  if autoUpdateOrderState ~= false then
    self:UpdateOrderState()
  end
end

function BaseSceneBoardModel:LoadFileConfig()
  self.m_promptConfig = {}
  local promptConfigs = GM.ConfigModel:GetLocalConfig(LocalConfigKey.BoardPrompt)
  for _, promptConfig in ipairs(promptConfigs) do
    self.m_promptConfig[promptConfig.Type] = promptConfig
  end
  self.m_itemLayerModel:LoadFileConfig()
  self.m_orderModel:LoadFileConfig()
end

function BaseSceneBoardModel:OnSyncDataFinished()
  self.m_itemManager:OnSyncDataFinished()
  local noItem = self.m_itemManager:IsEmpty()
  self.m_itemLayerModel:OnSyncDataFinished(noItem)
  self.m_itemCacheModel:OnSyncDataFinished()
  self.m_orderModel:OnSyncDataFinished()
  self:UpdateOrderState()
  self:UpdateOpeningItem()
end

function BaseSceneBoardModel:Update()
  self.m_frameCache = nil
  self:_RealUpdateOrderState()
  if self.m_gameMode ~= GM.SceneManager:GetGameMode() then
    return
  end
  BaseInteractiveBoardModel.Update(self)
end

function BaseSceneBoardModel:UpdatePerSecond()
  if self.m_gameMode ~= GM.SceneManager:GetGameMode() then
    return
  end
  BaseInteractiveBoardModel.UpdatePerSecond(self)
end

function BaseSceneBoardModel:GetPromptConfig()
  return self.m_promptConfig
end

function BaseSceneBoardModel:GetOrderModel()
  return self.m_orderModel
end

function BaseSceneBoardModel:UpdateOrderState()
  self.m_bNeedUpdateOrderState = true
end

function BaseSceneBoardModel:_RealUpdateOrderState()
  if not self.m_bNeedUpdateOrderState then
    return
  end
  self.m_bNeedUpdateOrderState = nil
  local codeCountMap = self:GetCodeCountMap(true, false, true)
  local itemCookCmp = self:GetAllItemCookCmp()
  self.m_orderModel:UpdateState(codeCountMap, itemCookCmp)
  EventDispatcher.DispatchEvent(EEventType.OrderStateChanged)
end

function BaseSceneBoardModel:FinishOrder(order, checkError)
  order:SetFinished()
  local removeItemInfo = self:RemoveOrderRequirementItems(order, checkError)
  local rewards = order:GetRewards()
  self.m_orderModel:FinishOrder(order, removeItemInfo)
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.FinishOrder, self.m_gameMode, CacheItemType.Stack)
  local goldCount
  for _, v in pairs(rewards) do
    if v[PROPERTY_TYPE] == EPropertyType.Gold then
      goldCount = v[PROPERTY_COUNT]
      EventDispatcher.DispatchEvent(EEventType.CollectGold, goldCount)
    end
  end
  self:UpdateOrderState()
  self.event:Call(BoardEventType.FinishOrder, {
    Order = order,
    RemoveItemInfo = removeItemInfo,
    Rewards = rewards
  })
end

function BaseSceneBoardModel:RemoveOrderRequirementItems(order, checkError)
  local removeInfo = {}
  local removedItemsFromBoard = {}
  local removedCellIndexFromBoard = {}
  local itemDataModel = GM.ItemDataModel
  local countMap, arrOrder = order:GetUniqueRequirements()
  for i, requirement in ipairs(arrOrder) do
    local left = countMap[requirement]
    local code
    for item, _ in pairs(self:GetAllBoardItems(true)) do
      code = item:GetCode()
      if code == requirement and (not itemDataModel:IsDisposableInstrument(code) or item:IsEmptyInstrument()) then
        self:RemoveItem(item)
        removedItemsFromBoard[#removedItemsFromBoard + 1] = item
        removedCellIndexFromBoard[#removedCellIndexFromBoard + 1] = i
        left = left - 1
        if left <= 0 then
          break
        end
      end
    end
    if 0 < left and checkError ~= false then
      GM.BIManager:LogErrorInfo(EBIType.FinishOrderRemoveItemError, requirement)
    end
  end
  removeInfo.RemovedFromBoard = removedItemsFromBoard
  removeInfo.RemovedCellIndexFromBoard = removedCellIndexFromBoard
  return removeInfo
end

function BaseSceneBoardModel:GetOrders()
  return self.m_orderModel:GetOrders()
end

function BaseSceneBoardModel:GetOrderCodeStateMap()
  local codeStateMap = {}
  for _, order in pairs(self:GetOrders()) do
    local orderState = order:GetState()
    local fillStates = order:GetRequirementFillStates()
    for index, code in ipairs(order:GetRequirements()) do
      if fillStates[index] == ERequirementFillState.Filled and orderState ~= OrderState.Finished then
        if codeStateMap[code] == nil then
          codeStateMap[code] = OrderState.Init
        end
        if orderState > codeStateMap[code] then
          codeStateMap[code] = orderState
        end
      end
    end
  end
  return codeStateMap
end

function BaseSceneBoardModel:GetOrderCodeRequireCount()
  local codeRequireCount = {}
  for _, order in pairs(self:GetOrders()) do
    local fillStates = order:GetRequirementFillStates()
    for index, code in ipairs(order:GetRequirements()) do
      if fillStates[index] ~= ERequirementFillState.Filled then
        codeRequireCount[code] = (codeRequireCount[code] or 0) + 1
      end
    end
  end
  return codeRequireCount
end

function BaseSceneBoardModel:GetOrderRequirements(includeIndirect, selectOrder)
  local directDishes = {}
  local directNonDishes = {}
  local orders = selectOrder and {selectOrder} or self:GetOrders()
  for _, order in pairs(orders) do
    local requirements = order:GetRequirements()
    for _, require in ipairs(requirements) do
      if GM.ItemDataModel:IsDishes(require) then
        directDishes[require] = (directDishes[require] or 0) + 1
      else
        directNonDishes[require] = (directNonDishes[require] or 0) + 1
      end
    end
  end
  local indirectDishes, indirectNonDishes
  if includeIndirect then
    indirectDishes = {}
    indirectNonDishes = {}
    local itemDataModel = GM.ItemDataModel
    local arrNonDishMaterials, arrDishMaterials
    for directDish, count in pairs(directDishes) do
      arrNonDishMaterials, arrDishMaterials = itemDataModel:GetAllMaterials(directDish)
      for _, dishMaterial in ipairs(arrDishMaterials) do
        indirectDishes[dishMaterial] = (indirectDishes[dishMaterial] or 0) + 1
      end
      for _, dishMaterial in ipairs(arrNonDishMaterials) do
        indirectNonDishes[dishMaterial] = (indirectNonDishes[dishMaterial] or 0) + 1
      end
    end
  end
  return directDishes, directNonDishes, indirectDishes, indirectNonDishes
end

function BaseSceneBoardModel:GetUnfilledDirectOrderRequirements(excludeStore, selectOrder)
  local unfilledDirectResult = {}
  local filledResult = {}
  for _, order in pairs(selectOrder and {selectOrder} or self:GetOrders()) do
    local requirements = order:GetRequirements()
    for _, require in ipairs(requirements) do
      unfilledDirectResult[require] = (unfilledDirectResult[require] or 0) + 1
    end
  end
  local codeCountMap = self:GetCodeCountMap(true, false, not excludeStore)
  self:_AddCookCmpItems(codeCountMap, not excludeStore)
  self:_FillWithCodeCountMap(unfilledDirectResult, codeCountMap, filledResult)
  return unfilledDirectResult, codeCountMap, filledResult
end

function BaseSceneBoardModel:GetUnfilledOrderRequirementsSeparately(excludeStore, selectOrder)
  local cacheKey = "GetUnfilledOrderRequirementsSeparately"
  if excludeStore then
    cacheKey = cacheKey .. "_e"
  end
  local cache = self.m_frameCache ~= nil and self.m_frameCache[cacheKey] or nil
  if not selectOrder and cache ~= nil and #cache == 4 then
    return cache[1], cache[2], cache[3], cache[4]
  end
  local directResult, codeCountMap, filledResult = self:GetUnfilledDirectOrderRequirements(excludeStore, selectOrder)
  local itemDataModel = GM.ItemDataModel
  local indirectResult = {}
  local mapDishResult = {}
  for directCode, count in pairs(directResult) do
    if itemDataModel:IsDishes(directCode) then
      mapDishResult[directCode] = (mapDishResult[directCode] or 0) + count
    end
  end
  repeat
    local mapMaterials = {}
    for code, count in pairs(mapDishResult) do
      for _, materialCode in ipairs(itemDataModel:GetMaterials(code, false, true)) do
        mapMaterials[materialCode] = (mapMaterials[materialCode] or 0) + count
      end
    end
    local tempDishResult = {}
    for materialCode, count in pairs(mapMaterials) do
      if itemDataModel:IsDishes(materialCode) then
        local needCount = self:_FilterOut(materialCode, count, codeCountMap)
        if 0 < needCount then
          tempDishResult[materialCode] = (tempDishResult[materialCode] or 0) + needCount
        end
        filledResult[materialCode] = (filledResult[materialCode] or 0) + count - needCount
      else
        local needCount = self:_FilterOut(materialCode, count, codeCountMap)
        if 0 < needCount then
          indirectResult[materialCode] = (indirectResult[materialCode] or 0) + needCount
        end
        filledResult[materialCode] = (filledResult[materialCode] or 0) + count - needCount
      end
    end
    mapDishResult = tempDishResult
  until next(mapDishResult) == nil
  if not selectOrder then
    if self.m_frameCache == nil then
      self.m_frameCache = {}
    end
    self.m_frameCache[cacheKey] = {
      directResult,
      indirectResult,
      filledResult,
      codeCountMap
    }
  end
  return directResult, indirectResult, filledResult, codeCountMap
end

function BaseSceneBoardModel:GetUnfilledOrderRequirements(excludeStore, selectOrder)
  local directResult, indirectResult, filledResult, codeCountMap = self:GetUnfilledOrderRequirementsSeparately(excludeStore, selectOrder)
  directResult = Table.ShallowCopy(directResult)
  for code, count in pairs(indirectResult) do
    directResult[code] = (directResult[code] or 0) + count
  end
  return directResult, codeCountMap
end

function BaseSceneBoardModel:GetOrderFillEnergyDiff(order)
  local log = "ID: " .. order:GetId() .. "\n"
  local unfilledRequirements, codeCountMap = self:GetUnfilledOrderRequirements(false, order)
  local energyDiff = 0
  local oneEnergyDiff, oneLog
  for requireItem, requireCount in pairs(unfilledRequirements) do
    if not GM.ItemDataModel:IsDishes(requireItem) then
      log = log .. "\n\t棋子 " .. requireItem .. " 缺少 " .. requireCount .. "个\n"
      oneEnergyDiff, oneLog = self:_GetEnergyDiff(requireItem, requireCount, codeCountMap, "")
      energyDiff = energyDiff + oneEnergyDiff
      log = log .. oneLog
    end
  end
  log = log .. "\n总体力差额为  " .. energyDiff .. "\n"
  return energyDiff, log
end

function BaseSceneBoardModel:_GetEnergyDiff(requireItem, requireCount, codeCountMap, log)
  local remainRequireToLevel1Count = ItemUtility.GetRemainRequireToLevel1CountMinusBelowLevelItems(requireItem, requireCount, codeCountMap)
  log = log .. "\t刨除已有低等级棋子后，折合缺少一级棋子 " .. remainRequireToLevel1Count .. "个\n"
  if remainRequireToLevel1Count <= 0 then
    return 0, log
  end
  local requireChain = GM.ItemDataModel:GetChainId(requireItem)
  local level1Item = ItemUtility.GetItemType(requireChain, 1)
  local transfromFrom = GM.ItemDataModel:GetTransformFrom(level1Item)
  if transfromFrom == nil then
    local oneEnergy = GM.ItemDataModel:GetItemCurProduceEnergy(level1Item)
    local result = oneEnergy * remainRequireToLevel1Count
    log = log .. "\t一个一级棋子体力消耗为 " .. oneEnergy .. "，" .. remainRequireToLevel1Count .. "个为" .. result .. "\n"
    return result, log
  else
    log = log .. "\t向上追溯 " .. transfromFrom .. " " .. remainRequireToLevel1Count .. "个\n"
    return self:_GetEnergyDiff(transfromFrom, remainRequireToLevel1Count, codeCountMap, log)
  end
end

function BaseSceneBoardModel:GetUnfilledOrderRequirementsConsiderView()
  local cacheKey = "GetUnfilledOrderRequirementsConsiderView"
  local cache = self.m_frameCache ~= nil and self.m_frameCache[cacheKey] or nil
  if cache ~= nil then
    return cache
  end
  local storeCodeCountMap, _ = self:_GetCodeCountMapInclueInCook(false, true)
  local boardCodeCountMap, itemCookCmpMaterials = self:_GetCodeCountMapInclueInCook(true, false)
  table.sort(itemCookCmpMaterials, function(a, b)
    return a.count > b.count
  end)
  local totalRequire = {}
  local orders = self:GetOrders()
  for _, order in pairs(orders) do
    local requirements = order:GetRequirements()
    for _, require in ipairs(requirements) do
      totalRequire[require] = (totalRequire[require] or 0) + 1
    end
  end
  local directLack = {}
  for requireItem, requireCount in pairs(totalRequire) do
    directLack[requireItem] = requireCount - (storeCodeCountMap[requireItem] or 0)
    storeCodeCountMap[requireItem] = (storeCodeCountMap[requireItem] or 0) - requireCount
    if storeCodeCountMap[requireItem] <= 0 then
      storeCodeCountMap[requireItem] = nil
    end
    if 0 < directLack[requireItem] then
      requireCount = directLack[requireItem]
      directLack[requireItem] = requireCount - (boardCodeCountMap[requireItem] or 0)
      if directLack[requireItem] <= 0 then
        directLack[requireItem] = nil
      end
      boardCodeCountMap[requireItem] = (boardCodeCountMap[requireItem] or 0) - requireCount
      if boardCodeCountMap[requireItem] <= 0 then
        boardCodeCountMap[requireItem] = nil
      end
    else
      directLack[requireItem] = nil
    end
  end
  local directNonDishLack = {}
  local directDishLack = {}
  for item, count in pairs(directLack) do
    if GM.ItemDataModel:IsDishes(item) then
      directDishLack[item] = count
    else
      directNonDishLack[item] = count
    end
  end
  local lackDishs = directDishLack
  local lackDishMaterials = {}
  local lackDishMat2Dish = {}
  local indirectNonDishLack = {}
  local indirectDishLack = {}
  repeat
    local tmpLackMat = {}
    local originalMat, tempMat, instru, instruChain
    local lackDishArray = Table.GetKeys(lackDishs)
    table.sort(lackDishArray, function(a, b)
      local matCountA = #GM.ItemDataModel:GetMaterials(a)
      local matCountB = #GM.ItemDataModel:GetMaterials(b)
      return matCountA < matCountB
    end)
    local count
    for _, dish in ipairs(lackDishArray) do
      count = lackDishs[dish]
      originalMat = GM.ItemDataModel:GetMaterials(dish, false, false)
      tempMat = {}
      for _, t in ipairs(originalMat) do
        tempMat[t] = count
      end
      instru = GM.ItemDataModel:GetModelConfig(dish).Instrument[1].Instru
      instruChain = GM.ItemDataModel:GetChainId(instru)
      for _, cook in ipairs(itemCookCmpMaterials) do
        if 0 < #cook.remain and instruChain == cook.chain and Table.IsSubArray(originalMat, cook.origin) then
          local deleteMat = {}
          for _, mat in ipairs(cook.remain) do
            if tempMat[mat] and 0 < tempMat[mat] then
              tempMat[mat] = tempMat[mat] - 1
              if tempMat[mat] <= 0 then
                tempMat[mat] = nil
              end
              deleteMat[#deleteMat + 1] = mat
            end
          end
          for _, mat in ipairs(deleteMat) do
            Table.ListRemove(cook.remain, mat)
          end
        end
      end
      for material, count in pairs(tempMat) do
        lackDishMaterials[material] = (lackDishMaterials[material] or 0) + count
        if lackDishMat2Dish[material] == nil then
          lackDishMat2Dish[material] = {}
        end
        lackDishMat2Dish[material][dish] = true
        tmpLackMat[material] = (tmpLackMat[material] or 0) + count
      end
    end
    local tmpLackDishs = {}
    for key, value in pairs(tmpLackMat) do
      tmpLackMat[key] = value - (boardCodeCountMap[key] or 0)
      boardCodeCountMap[key] = (boardCodeCountMap[key] or 0) - value
      if boardCodeCountMap[key] <= 0 then
        boardCodeCountMap[key] = nil
      end
      if 0 < tmpLackMat[key] then
        if GM.ItemDataModel:IsDishes(key) then
          tmpLackDishs[key] = tmpLackMat[key]
          indirectDishLack[key] = (indirectDishLack[key] or 0) + tmpLackMat[key]
        else
          indirectNonDishLack[key] = (indirectNonDishLack[key] or 0) + tmpLackMat[key]
        end
      end
    end
    lackDishs = tmpLackDishs
  until next(lackDishs) == nil
  if self.m_frameCache == nil then
    self.m_frameCache = {}
  end
  local status = {
    directNonDishLack = directNonDishLack,
    directDishLack = directDishLack,
    indirectNonDishLack = indirectNonDishLack,
    indirectDishLack = indirectDishLack,
    lackDishMaterials = lackDishMaterials,
    lackDishMat2Dish = lackDishMat2Dish
  }
  self.m_frameCache[cacheKey] = status
  return status
end

function BaseSceneBoardModel:_GetCodeCountMapInclueInCook(includeBoard, includeStore)
  local codeCountMap = self:GetCodeCountMap(includeBoard, false, includeStore)
  local itemCookCmps = self:GetAllItemCookCmp(includeBoard, includeStore)
  local itemCookCmpMaterials = {}
  for _, cookCmp in ipairs(itemCookCmps) do
    if cookCmp:GetState() == EItemCookState.Cooked or cookCmp:GetState() == EItemCookState.Cooking then
      local recipe = cookCmp:GetRecipe()
      codeCountMap[recipe] = (codeCountMap[recipe] or 0) + 1
    elseif cookCmp:GetState() == EItemCookState.Prepare or cookCmp:GetState() == EItemCookState.CanCook then
      itemCookCmpMaterials[#itemCookCmpMaterials + 1] = {
        chain = GM.ItemDataModel:GetChainId(cookCmp:GetItemModel():GetCode()),
        count = #cookCmp:GetCurMaterialsArray(),
        origin = cookCmp:GetCurMaterialsArray(),
        remain = cookCmp:GetCurMaterialsArray()
      }
    end
  end
  return codeCountMap, itemCookCmpMaterials
end

function BaseSceneBoardModel:_FilterOut(itemCode, needCount, codeCountMap)
  local existCount = codeCountMap[itemCode]
  if existCount and 0 < existCount then
    if needCount <= existCount then
      codeCountMap[itemCode] = existCount - needCount
      if codeCountMap[itemCode] <= 0 then
        codeCountMap[itemCode] = nil
      end
      return 0
    else
      codeCountMap[itemCode] = nil
      return needCount - existCount
    end
  else
    return needCount
  end
end

function BaseSceneBoardModel:_AddCookCmpItems(codeCountMap, includeStore)
  local cookState, cookCode
  local cookCmps = self:GetAllItemCookCmp(true, includeStore)
  for _, cookCmp in ipairs(cookCmps) do
    cookState = cookCmp:GetState()
    if cookState == EItemCookState.Cooked or cookState == EItemCookState.Cooking then
      local recipe = cookCmp:GetRecipe()
      codeCountMap[recipe] = (codeCountMap[recipe] or 0) + 1
      if cookCmp:IsDisposable() then
        cookCode = cookCmp:GetItemModel():GetCode()
        codeCountMap[cookCode] = (codeCountMap[cookCode] or 0) - 1
        if codeCountMap[cookCode] <= 0 then
          codeCountMap[cookCode] = nil
        end
      end
    elseif cookState == EItemCookState.Prepare or cookState == EItemCookState.CanCook then
      local arrItemCodes = cookCmp:GetCurMaterialsArray()
      for _, code in ipairs(arrItemCodes) do
        codeCountMap[code] = (codeCountMap[code] or 0) + 1
      end
    end
  end
end

function BaseSceneBoardModel:_FillWithCodeCountMap(mapUnfilled, codeCountMap, filledResult)
  local deleteKeys = {}
  for code, count in pairs(mapUnfilled) do
    local existCount = codeCountMap[code]
    if existCount and 0 < existCount then
      if count <= existCount then
        deleteKeys[#deleteKeys + 1] = code
        codeCountMap[code] = existCount - count
        filledResult[code] = (filledResult[code] or 0) + count
        if codeCountMap[code] <= 0 then
          codeCountMap[code] = nil
        end
      else
        mapUnfilled[code] = count - existCount
        filledResult[code] = (filledResult[code] or 0) + existCount
        codeCountMap[code] = nil
      end
    end
  end
  for _, key in ipairs(deleteKeys) do
    mapUnfilled[key] = nil
  end
end

function BaseSceneBoardModel:GetUnfilledDishMaterials(dishType, excludeStore)
  local codeCountMap = self:GetCodeCountMap(true, false, not excludeStore)
  self:_AddCookCmpItems(codeCountMap, not excludeStore)
  local _, arrDishMaterials = GM.ItemDataModel:GetAllMaterials(dishType)
  local mapUnfilled = {}
  for _, mat in ipairs(arrDishMaterials) do
    mapUnfilled[mat] = (mapUnfilled[mat] or 0) + 1
  end
  self:_FillWithCodeCountMap(mapUnfilled, codeCountMap, {})
  return mapUnfilled
end

function BaseSceneBoardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(itemCode, direct, excludeStore, selectOrder)
  if not itemCode then
    return false
  end
  if ESpreadReplaceType[itemCode] ~= nil or StringUtil.StartWith(itemCode, ESpreadReplaceType.score) then
    return false
  end
  local cacheKey = "IsUnfilledOrderRequirementsChainOrPdChainItem" .. (direct and "_direct" or "_indirect")
  if excludeStore then
    cacheKey = cacheKey .. "_e"
  end
  local cache = self.m_frameCache ~= nil and self.m_frameCache[cacheKey] or nil
  if not selectOrder and cache ~= nil and cache[itemCode] ~= nil then
    return cache[itemCode]
  end
  if self.m_frameCache == nil then
    self.m_frameCache = {}
  end
  if self.m_frameCache[cacheKey] == nil then
    self.m_frameCache[cacheKey] = {}
  end
  local chainId = GM.ItemDataModel:GetChainId(itemCode)
  local chainLevel = GM.ItemDataModel:GetChainLevel(itemCode)
  local requirements = self:GetUnfilledOrderRequirements(excludeStore, selectOrder)
  local curChain, curLevel
  for item, _ in pairs(requirements) do
    curChain = GM.ItemDataModel:GetChainId(item)
    curLevel = GM.ItemDataModel:GetChainLevel(item)
    if curChain == chainId and chainLevel <= curLevel then
      self.m_frameCache[cacheKey][itemCode] = true
      return true
    end
    if not direct and GM.ItemDataModel:GetItemGenerators(item) ~= nil then
      for _, pdItem in ipairs(GM.ItemDataModel:GetItemGenerators(item)) do
        curChain = GM.ItemDataModel:GetChainId(pdItem)
        curLevel = GM.ItemDataModel:GetChainLevel(pdItem)
        if curChain == chainId and chainLevel <= curLevel then
          self.m_frameCache[cacheKey][itemCode] = true
          return true
        end
        if GM.ItemDataModel:GetItemGenerators(pdItem) and Table.ListContain(GM.ItemDataModel:GetItemGenerators(pdItem), itemCode) then
          self.m_frameCache[cacheKey][itemCode] = true
          return true
        end
      end
    end
  end
  if not selectOrder then
    self.m_frameCache[cacheKey][itemCode] = false
  end
  return false
end

function BaseSceneBoardModel:IsOrderRelaventItem(order, itemCode)
  local itemDataModel = GM.ItemDataModel
  if not itemDataModel:IsItemExist(itemCode) then
    return false
  end
  local mapCodes = {}
  local _, arrOrder = order:GetUniqueRequirements()
  local arrNonDishMaterials, arrDishMaterials
  for _, require in ipairs(arrOrder) do
    mapCodes[require] = true
    if itemDataModel:IsDishes(require) then
      arrNonDishMaterials, arrDishMaterials = itemDataModel:GetAllMaterials(require)
      for _, material in ipairs(arrNonDishMaterials) do
        mapCodes[material] = true
      end
      for _, material in ipairs(arrDishMaterials) do
        mapCodes[material] = true
      end
    end
  end
  if mapCodes[itemCode] then
    return true
  end
  return false
end
