ItemStoreModel = {}
ItemStoreModel.__index = ItemStoreModel

function ItemStoreModel.Create(dbTable, itemManager)
  local itemStoreModel = setmetatable({}, ItemStoreModel)
  itemStoreModel:Init(dbTable, itemManager)
  return itemStoreModel
end

function ItemStoreModel:Init(dbTable, itemManager)
  self.m_dbTable = dbTable
  self.m_itemManager = itemManager
  self.m_mapNewAddedItems = {}
end

function ItemStoreModel:LoadFileConfig()
  self.m_arrProducerConfigInfos = GM.ConfigModel:GetLocalConfig(LocalConfigKey.InventorySlot).ProducerInventory or {}
  self.m_mapType2Day = {}
  self.m_mapDay2Type = {}
end

function ItemStoreModel:OnSyncDataFinished()
  self.m_itemDataList = {}
  self.m_itemCodeMap = {}
  self.m_itemCountMap = {}
  local item, itemCode
  for itemId, data in pairs(self.m_dbTable:GetValues()) do
    item = self.m_itemManager:GetItem(itemId)
    if item ~= nil then
      itemCode = item:GetCode()
      data.itemId = itemId
      table.insert(self.m_itemDataList, data)
      if not self.m_itemCodeMap[itemCode] then
        self.m_itemCodeMap[itemCode] = {}
        self.m_itemCountMap[itemCode] = 0
      end
      self.m_itemCodeMap[itemCode][item] = true
      self.m_itemCountMap[itemCode] = self.m_itemCountMap[itemCode] + 1
      if StringUtil.IsNilOrEmpty(data.codeStr) then
        self.m_dbTable:Set(data.itemId, "codeStr", item:GetCode())
      end
    else
      GM.BIManager:LogProject(EBIType.ItemNotFound, "ISM itemId:" .. tostring(itemId) .. " itemCode:" .. tostring(data.codeStr))
      self.m_dbTable:Remove(itemId)
    end
  end
  table.sort(self.m_itemDataList, function(a, b)
    return a.storeTime < b.storeTime
  end)
  self:_RefreshIndexMap()
  for i = #self.m_arrProducerConfigInfos, 1, -1 do
    local type = self.m_arrProducerConfigInfos[i].type
    if not GM.ItemDataModel:IsItemExist(type) then
      table.remove(self.m_arrProducerConfigInfos, i)
      if GameConfig.IsTestMode() then
        Log.Error("[InventorySlotConfig] 不存在的棋子类型, 请检查: " .. (type or "nil"))
      end
    elseif self.m_mapType2Day[type] ~= nil then
      table.remove(self.m_arrProducerConfigInfos, i)
      if GameConfig.IsTestMode() then
        Log.Error("[InventorySlotConfig] 重复的棋子类型, 请检查: " .. (type or "nil"))
      end
    else
      local day = self.m_arrProducerConfigInfos[i].day
      self.m_mapType2Day[type] = day
      Log.Assert(self.m_mapDay2Type[day] == nil, "[InventorySlotConfig] 重复的天数：" .. tostring(day))
      self.m_mapDay2Type[day] = type
    end
  end
end

function ItemStoreModel:_RefreshIndexMap()
  self.m_itemIndexMap = {}
  for index, storeData in ipairs(self.m_itemDataList) do
    self.m_itemIndexMap[storeData.itemId] = index
  end
end

function ItemStoreModel:Sort()
  local arrChainId = {}
  local mapChainItems = {}
  local itemDataModel = GM.ItemDataModel
  local item, type, chain, arrChainItems
  local count = self:GetItemCount()
  for i = 1, count do
    item = self:GetItem(i)
    if item ~= nil then
      type = item:GetType()
      chain = itemDataModel:GetChainId(type)
      arrChainItems = mapChainItems[chain]
      if not mapChainItems[chain] then
        arrChainId[#arrChainId + 1] = chain
        arrChainItems = {}
        mapChainItems[chain] = arrChainItems
      end
      arrChainItems[#arrChainItems + 1] = item
    end
  end
  for _, chainId in ipairs(arrChainId) do
    arrChainItems = mapChainItems[chainId]
    local chainLevel
    local curLevel = 1
    local needSort = false
    for _, item in ipairs(arrChainItems) do
      chainLevel = itemDataModel:GetChainLevel(item:GetType())
      if curLevel <= chainLevel then
        curLevel = chainLevel
      else
        needSort = true
        break
      end
    end
    if needSort then
      table.sort(arrChainItems, function(item1, item2)
        return itemDataModel:GetChainLevel(item1:GetType()) < itemDataModel:GetChainLevel(item2:GetType())
      end)
    end
  end
  self.m_itemDataList = {}
  local arrBatchData = {}
  local index = 1
  local fakeStoreTime
  for _, chainId in ipairs(arrChainId) do
    arrChainItems = mapChainItems[chainId]
    for _, itemModel in ipairs(arrChainItems) do
      fakeStoreTime = index * 1000
      arrBatchData[itemModel:GetId()] = {
        storeTime = fakeStoreTime,
        codeStr = itemModel:GetCode()
      }
      self.m_itemDataList[index] = {
        itemId = itemModel:GetId(),
        storeTime = fakeStoreTime
      }
      index = index + 1
    end
  end
  self.m_dbTable:BatchSet(arrBatchData)
  self:_RefreshIndexMap()
end

function ItemStoreModel:AddItem(item, index)
  local id = item:GetId()
  local data = {
    itemId = id,
    storeTime = GM.GameModel:GetServerTime()
  }
  if self.m_itemDataList[index] then
    data.storeTime = self.m_itemDataList[index].storeTime - 1
  else
    index = nil
  end
  self.m_dbTable:Set(id, "storeTime", data.storeTime)
  self.m_dbTable:Set(data.itemId, "codeStr", item:GetCode())
  if index then
    table.insert(self.m_itemDataList, index, data)
    self:_RefreshIndexMap()
  else
    table.insert(self.m_itemDataList, data)
    self.m_itemIndexMap[id] = #self.m_itemDataList
  end
  local itemCode = item:GetCode()
  if not self.m_itemCodeMap[itemCode] then
    self.m_itemCodeMap[itemCode] = {}
    self.m_itemCountMap[itemCode] = 0
  end
  self.m_itemCodeMap[itemCode][item] = true
  self.m_itemCountMap[itemCode] = self.m_itemCountMap[itemCode] + 1
  self.m_mapNewAddedItems[item] = true
end

function ItemStoreModel:RemoveItem(index)
  local data = self.m_itemDataList[index]
  table.remove(self.m_itemDataList, index)
  self:_RefreshIndexMap()
  self.m_dbTable:Remove(data.itemId)
  local item = self.m_itemManager:GetItem(data.itemId)
  if item == nil then
    Log.Error("应先从仓库移除再从 ItemManager 中移除！")
    return
  end
  local itemCode = item:GetCode()
  self.m_itemCodeMap[itemCode][item] = nil
  self.m_itemCountMap[itemCode] = self.m_itemCountMap[itemCode] - 1
  self.m_mapNewAddedItems[item] = nil
end

function ItemStoreModel:RemoveItemByItemModel(itemModel)
  local index = self:GetIndexByItemModel(itemModel)
  if 0 < index then
    self:RemoveItem(index)
  end
end

function ItemStoreModel:GetNewAddedItems()
  return self.m_mapNewAddedItems
end

function ItemStoreModel:ClearNewAddedItems()
  self.m_mapNewAddedItems = {}
end

function ItemStoreModel:SwapItem(itemModel1, itemModel2)
  local id1 = itemModel1 and itemModel1:GetId() or 0
  local id2 = itemModel2 and itemModel2:GetId() or 0
  local index1 = self.m_itemIndexMap[id1]
  local index2 = self.m_itemIndexMap[id2]
  local storeData1 = self.m_itemDataList[index1]
  local storeData2 = self.m_itemDataList[index2]
  Log.Assert(storeData1 and storeData2, "只能交换仓库中存在的棋子")
  self.m_itemIndexMap[id1] = index2
  self.m_itemIndexMap[id2] = index1
  self.m_itemDataList[index1] = storeData2
  self.m_itemDataList[index2] = storeData1
  local temp = storeData1.storeTime
  storeData1.storeTime = storeData2.storeTime
  storeData2.storeTime = temp
end

function ItemStoreModel:RepositionStoredItem(itemModel, index)
  local toItem = self:GetItem(index)
  if toItem then
    self:RemoveItemByItemModel(itemModel)
    self:AddItem(itemModel, index)
  else
    self:RemoveItemByItemModel(itemModel)
    self:AddItem(itemModel)
  end
end

function ItemStoreModel:GetItem(index)
  local data = self.m_itemDataList[index]
  return data and self.m_itemManager:GetItem(data.itemId)
end

function ItemStoreModel:GetItemsByType(itemType, copy)
  local map = self.m_itemCodeMap[itemType]
  if map and copy then
    map = Table.ShallowCopy(map)
  end
  return map or {}
end

function ItemStoreModel:GetItemCountByCode(itemCode)
  return self.m_itemCountMap[itemCode] or 0
end

function ItemStoreModel:GetDuplicateItems(copy)
  local mapDuplicates = {}
  for itemCode, count in pairs(self.m_itemCountMap) do
    if 2 <= count then
      mapDuplicates[itemCode] = self.m_itemCodeMap[itemCode]
      if copy then
        mapDuplicates[itemCode] = Table.ShallowCopy(mapDuplicates[itemCode])
      end
    end
  end
  return mapDuplicates
end

function ItemStoreModel:GetItemCount()
  return #self.m_itemDataList
end

function ItemStoreModel:GetIndexOfItemCode(targetCode)
  local item
  local count = self:GetItemCount()
  for i = 1, count do
    item = self:GetItem(i)
    if item ~= nil then
      local code = item:GetCode()
      if code == targetCode then
        return i, item
      end
    end
  end
end

function ItemStoreModel:GetIndexByItemModel(itemModel)
  local index = self.m_itemIndexMap[itemModel:GetId()]
  return index or -1
end

function ItemStoreModel:GetProducerInventoryConfig()
  return self.m_arrProducerConfigInfos
end

function ItemStoreModel:GetProducerOpenDayByType(type)
  local day = self.m_mapType2Day[type]
  return day
end

function ItemStoreModel:GetProducerTypeByOpenDay(day)
  local type = self.m_mapDay2Type[day]
  return type
end
