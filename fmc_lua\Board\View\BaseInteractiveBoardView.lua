BaseInteractiveBoardView = setmetatable({}, BaseBoardView)
BaseInteractiveBoardView.__index = BaseInteractiveBoardView

function BaseInteractiveBoardView:Init(boardModel)
  BaseBoardView.Init(self, boardModel)
  EventDispatcher.AddActiveListener(EEventType.TutorialFinished, self, self.TryStartBoardPrompt)
  EventDispatcher.AddActiveListener(EEventType.UpdateStorage, self, self.TryStartBoardPrompt)
  EventDispatcher.AddActiveListener(EEventType.StrongTutorialStart, self, self._CancelBoardPrompt)
  EventDispatcher.AddActiveListener(EEventType.RefreshPrompt, self, self.TryStartBoardPrompt)
  EventDispatcher.AddActiveListener(EEventType.CloseView, self, self._OnCloseView)
  EventDispatcher.AddActiveListener(EEventType.OpenView, self, self._OnOpenView)
  EventDispatcher.AddActiveListener(EEventType.ApplicationWillEnterForeground, self, self._ClearPointerDataOnUnexpectedPointerExit)
  REGISTER_BOARD_EVENT_HANDLER(self, "MergeItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "CollectItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "CollapseItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "CostItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "SellItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "UndoSellItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "PopCachedItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "ChooseItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "SplitItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "DuplicateItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "BoostItemSpread")
  REGISTER_BOARD_EVENT_HANDLER(self, "TimeSkip")
  REGISTER_BOARD_EVENT_HANDLER(self, "BubbleDisposed")
  REGISTER_BOARD_EVENT_HANDLER(self, "RewardBubbleDisposed")
  REGISTER_BOARD_EVENT_HANDLER(self, "LackGem")
  REGISTER_BOARD_EVENT_HANDLER(self, "LackSpreadEnergy")
  REGISTER_BOARD_EVENT_HANDLER(self, "SpreadFailed")
  REGISTER_BOARD_EVENT_HANDLER(self, "PutBackMaterial")
  REGISTER_BOARD_EVENT_HANDLER(self, "UpdateOpeningItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "ShowPrompt")
end

function BaseInteractiveBoardView:OnDestroy()
  BaseBoardView.OnDestroy(self)
  EventDispatcher.RemoveTarget(self)
end

function BaseInteractiveBoardView:OnDisable()
  self.m_effectRoot.gameObject:RemoveChildren()
end

function BaseInteractiveBoardView:Update()
  if self.m_model == nil then
    return
  end
  if self.m_collapsedPaperBoxNumber then
    GM.AudioModel:PlayEffect(self.m_collapsedPaperBoxNumber > 1 and AudioFileConfigName.SfxMergeBoxMulti01 or AudioFileConfigName.sfxMergeBox)
  end
  self.m_collapsedPaperBoxNumber = nil
end

function BaseInteractiveBoardView:LateUpdate()
  if self.m_model == nil then
    return
  end
  self:_RealTryStartBoardPrompt()
end

function BaseInteractiveBoardView:_RemoveItemView(itemView)
  if itemView and self.m_lastTouchedItem == itemView then
    self.m_lastTouchedItem = nil
  end
  BaseBoardView._RemoveItemView(self, itemView)
end

function BaseInteractiveBoardView:_OnPointerDown(eventData)
  self:_ClearPointerDataOnUnexpectedPointerExit()
  local screenPosition = eventData.position
  local worldPosition = self:ConvertScreenPositionToWorldPosition(Vector3(screenPosition.x, screenPosition.y, 0))
  local boardPosition = self:_GetBoardPosition(worldPosition)
  if self:IsBoardPositionValid(boardPosition) then
    local forceSourceBoardPosition = GM.TutorialModel:GetForceSourceBoardPosition()
    if forceSourceBoardPosition ~= nil and forceSourceBoardPosition ~= boardPosition then
      return
    end
    local itemModel = self.m_model:GetItem(boardPosition)
    if itemModel ~= nil then
      self.m_lastTouchedItem = self:GetItemView(itemModel)
      if itemModel:GetComponent(ItemPaperBox) ~= nil or itemModel:GetComponent(ItemLocker) ~= nil then
        return
      end
      if self.m_selectedBoardPosition == boardPosition then
        self.m_moreThanOnceTap = true
      end
      self:_UpdateIndicator(itemModel, false)
      self:UpdateBoardInfoBar(itemModel)
      if self:_StartBoardPromptStep2() then
        self.m_startedBoardPromptStep2 = true
      else
        self:_CancelBoardPrompt()
      end
    end
  end
end

function BaseInteractiveBoardView:IsBoardPositionValid(boardPosition)
  return boardPosition:IsValid()
end

function BaseInteractiveBoardView:_OnDrag(eventData)
  if self.m_lastTouchedItem == nil then
    return
  end
  local screenPosition = eventData.position
  local canItemMove = self.m_model:CanItemMove(self.m_lastTouchedItem:GetModel())
  if not self.m_dragging and canItemMove then
    self:_OnDragBegin()
  end
  self.m_dragging = true
  if not canItemMove then
    return
  end
  local worldPosition = self:ConvertScreenPositionToWorldPosition(Vector3(screenPosition.x, screenPosition.y, 0))
  self.m_lastTouchedItem.transform.position = Vector3(worldPosition.x, worldPosition.y, 0)
  self:_TryShowMergeLight(worldPosition)
end

function BaseInteractiveBoardView:_OnDragBegin()
  self.m_lastTouchedItem:OnDragBegin()
  self:_UpdateIndicator()
  self:_UpdateItemAffectedEffect()
  local isSpecial = self.m_lastTouchedItem:GetModel():GetComponent(ItemBooster) ~= nil
  if isSpecial then
    GM.AudioModel:PlayEffectLoop(AudioFileConfigName.SfxDragSpecialItem, self)
  else
    GM.AudioModel:PlayEffect(AudioFileConfigName.sfxDragItem)
  end
end

function BaseInteractiveBoardView:_UpdateItemAffectedEffect()
  for _, view in pairs(self.m_modelViewMap) do
    if view ~= self.m_lastTouchedItem then
      view:UpdateItemAffectedEffect(self.m_lastTouchedItem and self.m_lastTouchedItem:GetModel() or nil)
    end
  end
end

function BaseInteractiveBoardView:_OnPointerUp(eventData)
  if self.m_lastTouchedItem == nil then
    return
  end
  self.m_lastTouchedItem:OnDragEnd()
  self:_ClearMergeLight()
  local screenPosition = eventData.position
  local worldPosition = self:ConvertScreenPositionToWorldPosition(Vector3(screenPosition.x, screenPosition.y, 0))
  local targetBoardPosition = self:_GetBoardPosition(worldPosition)
  if self.m_lastMergableItem ~= nil and CS.UnityEngine.Time.timeSinceLevelLoad - self.m_lastMergableItemTime < 0.15 then
    local lastMergeablePosition = self.m_lastMergableItem:GetPosition()
    if (lastMergeablePosition:GetX() - targetBoardPosition:GetX()) ^ 2 <= 1 and 1 >= (lastMergeablePosition:GetY() - targetBoardPosition:GetY()) ^ 2 then
      targetBoardPosition = lastMergeablePosition
    end
  end
  local forceTargetBoardPosition = GM.TutorialModel:GetForceTargetBoardPosition()
  if forceTargetBoardPosition ~= nil and forceTargetBoardPosition ~= targetBoardPosition then
    targetBoardPosition = self.m_model:CreatePosition(0, 0)
  end
  local itemModel = self.m_lastTouchedItem:GetModel()
  self.m_lastMergableItem = nil
  self.m_lastMergableItemTime = nil
  if self.m_dragging then
    if self.m_model:CanItemMove(itemModel) then
      self:_OnDragEnd(itemModel, targetBoardPosition, eventData)
      local isSpecial = itemModel:GetComponent(ItemBooster) ~= nil
      if isSpecial then
        GM.AudioModel:StopEffectLoop(self)
      end
    end
  else
    if itemModel:GetComponent(ItemLocker) ~= nil then
      self.m_model:TapItem(itemModel)
    elseif self.m_moreThanOnceTap and (forceTargetBoardPosition == nil or forceTargetBoardPosition == itemModel:GetPosition()) then
      self:_OnMultiPointerUpEnd(itemModel)
    end
    if self.m_model:GetItem(itemModel:GetPosition()) == itemModel and itemModel:GetComponent(ItemPaperBox) == nil and itemModel:GetComponent(ItemLocker) == nil then
      local itemView = self:GetItemView(itemModel)
      itemView:PlayTapAnimation(0.2)
      if not self.m_moreThanOnceTap then
        itemView:PlayChooseAudio()
      end
    end
  end
  EventDispatcher.DispatchEvent(EEventType.BoardPointerUp, {
    dragging = self.m_dragging
  })
  if itemModel:GetComponent(ItemPaperBox) ~= nil or itemModel:GetComponent(ItemLocker) ~= nil then
    self:_ResetTouchRecords()
    return
  end
  local selectedItem = self:GetSelectedItemModel()
  self:_UpdateIndicator(selectedItem, true)
  self:UpdateBoardInfoBar(selectedItem)
  self:_ResetTouchRecords()
  if self.m_startedBoardPromptStep2 then
    self.m_startedBoardPromptStep2 = false
  else
    self:TryStartBoardPrompt()
  end
end

function BaseInteractiveBoardView:_OnDragEnd(itemModel, targetBoardPosition, eventData)
  self.m_model:DragItem(itemModel, targetBoardPosition)
  if self.m_model:GetItem(itemModel:GetPosition()) == itemModel then
    self.m_selectedBoardPosition = itemModel:GetPosition()
  else
    self.m_selectedBoardPosition = targetBoardPosition
  end
end

function BaseInteractiveBoardView:_OnMultiPointerUpEnd(itemModel)
  if itemModel:GetComponent(ItemChoose) ~= nil then
    if not GM.UIManager:IsEventLock() and not GM.UIManager:IsViewExisting(UIPrefabConfigName.ItemChooseWindow) then
      GM.UIManager:OpenView(UIPrefabConfigName.ItemChooseWindow, itemModel)
    end
  elseif itemModel:GetComponent(ItemRewardBubble) ~= nil then
    self.m_model:BreakItem(itemModel)
  else
    self.m_model:TapItem(itemModel)
  end
end

function BaseInteractiveBoardView:_ResetTouchRecords()
  self.m_moreThanOnceTap = false
  self.m_dragging = false
  self.m_lastTouchedItem = nil
  self:_UpdateItemAffectedEffect()
end

function BaseInteractiveBoardView:_ClearPointerDataOnUnexpectedPointerExit()
  self:_ClearMergeLight()
  if self.m_lastTouchedItem == nil then
    return
  end
  local itemModel = self.m_lastTouchedItem:GetModel()
  if self.m_model:CanItemMove(itemModel) then
    self.m_model:DragItem(itemModel, self.m_model:CreatePosition(0, 0))
  end
  local selectedItem = self:GetSelectedItemModel()
  self:_UpdateIndicator(selectedItem, true)
  self:UpdateBoardInfoBar(selectedItem)
  self:_ResetTouchRecords()
end

function BaseInteractiveBoardView:_OnCloseView()
  self:TryStartBoardPrompt()
end

function BaseInteractiveBoardView:_OnOpenView()
end

function BaseInteractiveBoardView:_OnMergeItem(message)
  self:_UpdateIndicator(message.New, true)
  self:UpdateBoardInfoBar(message.New)
  local newItemType = message.New:GetType()
  local level = GM.ItemDataModel:GetChainLevel(message.New:GetType())
  local sfx
  if level <= 2 then
    sfx = AudioFileConfigName.SfxMergelv2
  elseif level < 9 then
    sfx = AudioFileConfigName["SfxMergelv" .. level]
  else
    sfx = AudioFileConfigName.SfxMergelv9
  end
  GM.AudioModel:PlayEffect(sfx)
  local sourceItemView = self:GetItemView(message.Source)
  sourceItemView.toBeRemoved = true
  local targetItemView = self:GetItemView(message.Target)
  targetItemView.toBeRemoved = true
  local newItemView = self:_AddItemView(message.New)
  if sourceItemView == nil or targetItemView == nil then
    self:_RemoveItemView(sourceItemView)
    self:_RemoveItemView(targetItemView)
    return
  end
  local targetPosition = targetItemView.transform.localPosition
  local sequence = DOTween.Sequence()
  sequence:Insert(0, sourceItemView.transform:DOLocalMove(targetPosition, 0.1))
  sequence:Insert(0, sourceItemView.transform:DOScale(0.3, 0.1))
  sequence:InsertCallback(0.1, function()
    self:_RemoveItemView(sourceItemView)
  end)
  targetItemView:MergeLightDisappear()
  sequence:Insert(0, targetItemView.transform:DOScale(0.3, 0.1))
  sequence:InsertCallback(0.1, function()
    self:_RemoveItemView(targetItemView)
  end)
  if newItemView ~= nil then
    newItemView.transform.localScale = Vector3.zero
    sequence:InsertCallback(0.1, function()
      newItemView.transform.localScale = 0.3 * V3One
    end)
    sequence:Insert(0.1, newItemView.transform:DOScale(1.3, 0.2))
    sequence:Insert(0.3, newItemView.transform:DOScale(1, 0.1))
  end
  local effectPos = targetItemView.transform.position
  sequence:InsertCallback(0.1, function()
    local mergeEffectPrefab = self.m_mergeEffectManager:GetPrefab(level)
    if mergeEffectPrefab ~= nil then
      local gameObject = Object.Instantiate(mergeEffectPrefab, effectPos, Quaternion.identity, self.m_effectRoot)
      DOVirtual.DelayedCall(1, function()
        if not gameObject:IsNull() then
          gameObject:RemoveSelf()
        end
      end)
    end
  end)
end

function BaseInteractiveBoardView:_OnSpreadItem(message)
  local sfx = self:_GetSpreadAudio(message)
  GM.AudioModel:PlayEffect(sfx)
  local sourceItemView = self:GetItemView(message.Source)
  local newItemView = self:_AddItemView(message.New)
  if newItemView == nil then
    GM.BIManager:LogErrorInfo(EBIProjectType.ElementNoViewWhenRemove, "spreadItem new ItemView Fail " .. message.New:GetCode())
    return
  end
  self:_PlayJumpAnimation(newItemView, sourceItemView.transform.localPosition, newItemView.transform.localPosition, nil, message.BoostLevelSpan, message.BoostEnergySpared)
  local itemSpread = message.Source:GetComponent(ItemSpread)
  if self.m_selectedBoardPosition == message.Source:GetPosition() and itemSpread ~= nil and itemSpread:IsAutoSpread() then
    self:UpdateBoardInfoBar(message.Source)
  end
end

function BaseInteractiveBoardView:_OnCollectItem(message)
  local rewards = message.Source:GetComponent(ItemCollectable):GetRewards()
  if rewards[1][PROPERTY_TYPE] == "skip" then
    local itemView = self:GetItemView(message.Source)
    self:_RemoveItemView(itemView)
    return
  end
  BaseInteractiveBoardView.PlayCollectItemSfx(rewards[1][PROPERTY_TYPE])
  local itemView = self:GetItemView(message.Source)
  local worldPosition = itemView.transform.position
  local screenPosition = self:ConvertWorldPositionToScreenPosition(worldPosition)
  local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
  GM.PropertyDataManager:PlayCollectAnimation(rewards, uiWorldPosition)
  itemView.toBeRemoved = true
  itemView.transform:DOScale(Vector3.zero, 0.2):OnComplete(function()
    self:_RemoveItemView(itemView)
  end)
end

function BaseInteractiveBoardView.PlayCollectItemSfx(type)
  local sfxs = {
    [EPropertyType.Gold] = AudioFileConfigName.SfxMergeCollectCoins,
    [EPropertyType.Gem] = AudioFileConfigName.SfxMergeCollectDiamond,
    [EPropertyType.Experience] = AudioFileConfigName.SfxMergeCollectExperience,
    [EPropertyType.Energy] = AudioFileConfigName.SfxMergeCollectEnergy
  }
  local sfx = sfxs[type]
  if sfx ~= nil then
    GM.AudioModel:PlayEffect(sfx)
  end
end

function BaseInteractiveBoardView:_OnCollapseItem(message)
  if message.Source:GetComponent(ItemPaperBox) ~= nil then
    self.m_collapsedPaperBoxNumber = (self.m_collapsedPaperBoxNumber or 0) + 1
  end
  if self.m_selectedBoardPosition == message.Source:GetPosition() then
    self:_UpdateIndicator(message.New, true)
    self:UpdateBoardInfoBar(message.New)
  end
  local position = message.Source:GetPosition():ToLocalPosition()
  position = Vector3(position.x + BaseBoardModel.TileSize / 2, position.y + BaseBoardModel.TileSize / 2, 0)
  local itemView = self:GetItemView(message.Source)
  self:_RemoveItemView(itemView)
  local hasPaperBox = message.Source:GetComponent(ItemPaperBox) ~= nil
  local newItemView
  if message.New ~= nil then
    newItemView = self:_AddItemView(message.New)
    if newItemView ~= nil then
      newItemView.transform.localScale = V3Zero
      if not hasPaperBox then
        newItemView.transform:DOScale(1, 0.2)
      end
    end
  end
  if hasPaperBox then
    self:PlayPaperBoxDisappearAnim(position, newItemView)
  end
end

function BaseInteractiveBoardView:PlayPaperBoxDisappearAnim(position, newItemView, parentTrans, callback)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.ItemPaperBoxDisappear), parentTrans or self.m_effectRoot, position, function(go)
    local tween = DOTween.Sequence()
    tween:AppendInterval(0.5)
    if newItemView then
      tween:Append(newItemView.transform:DOScale(1.2, 0.2))
      tween:Append(newItemView.transform:DOScale(1, 0.1))
    end
    tween:AppendInterval(1)
    tween:AppendCallback(function()
      go:RemoveSelf()
    end)
    if callback ~= nil then
      callback()
    end
  end)
end

function BaseInteractiveBoardView:_OnCostItem(message)
  local itemView = self:GetItemView(message.Source)
  self:_RemoveItemView(itemView)
  if self.m_selectedBoardPosition == message.Source:GetPosition() then
    self:_UpdateIndicator()
    self:UpdateBoardInfoBar()
  end
end

function BaseInteractiveBoardView:_OnTransformItem(message)
  if self.m_selectedBoardPosition == message.Source:GetPosition() then
    self:_UpdateIndicator(message.New, true)
    self:UpdateBoardInfoBar(message.New)
  end
  if not message.JustSpread then
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxMergeSpawnManual)
  end
  BaseBoardView._OnTransformItem(self, message)
end

function BaseInteractiveBoardView:_OnSellItem(message)
  local itemView = self:GetItemView(message.Source)
  itemView.toBeRemoved = true
  itemView.transform:DOScale(0, 0.4):OnComplete(function()
    if self:GetItemView(message.Source) == itemView then
      self:_RemoveItemView(itemView)
    else
      itemView.transform:DOKill(false)
      itemView.gameObject:RemoveSelf()
    end
  end)
  local worldPosition = itemView.transform.position
  local screenPosition = self:ConvertWorldPositionToScreenPosition(worldPosition)
  local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
  BoardViewHelper.PlaySellItemAnimation(self.m_effectRoot, itemView.transform.localPosition, uiWorldPosition, message.Source)
  self:_UpdateIndicator()
end

function BaseInteractiveBoardView:_OnUndoSellItem(message)
  if message.Removed ~= nil then
    local removedItemView = self:GetItemView(message.Removed)
    self:_RemoveItemView(removedItemView)
  end
  local itemView = self:_AddItemView(message.Source)
  if itemView ~= nil then
    itemView:PlayDropAnimation()
  end
  self:UpdateBoardInfoBar()
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxMergeSpawnManual)
end

function BaseInteractiveBoardView:_OnPopCachedItem(message)
  Log.Error("_OnPopCachedItem() 是抽象接口")
end

function BaseInteractiveBoardView:_OnChooseItem(message)
  local sourceItemView = self:GetItemView(message.Source)
  sourceItemView.toBeRemoved = true
  local newItemView = self:_AddItemView(message.New)
  newItemView:SetFlying(true)
  newItemView.gameObject.transform.localScale = V3Zero
  local sequence = DOTween.Sequence()
  sequence:Insert(0.4, sourceItemView.transform:DOScale(0, 0.2))
  sequence:InsertCallback(0.6, function()
    self:_RemoveItemView(sourceItemView)
    self:_UpdateIndicator(message.New, true)
    self:UpdateBoardInfoBar(message.New)
  end)
  sequence:InsertCallback(0.4, function()
    self:_PlayJumpAnimation(newItemView, sourceItemView.transform.localPosition, newItemView.transform.localPosition)
  end)
end

function BaseInteractiveBoardView:_OnSplitItem(message)
  self:_UpdateIndicator()
  self:UpdateBoardInfoBar()
  local itemSplit = message.Split:GetComponent(ItemSplit)
  if not itemSplit:HasLeftSplitUseCount() then
    local splitItemView = self:GetItemView(message.Split)
    self:_RemoveItemView(splitItemView)
  end
  local targetItemView = self:GetItemView(message.Target)
  targetItemView.toBeRemoved = true
  local newItemView1 = self:_AddItemView(message.New1)
  local newItemView2 = self:_AddItemView(message.New2)
  local sequence = DOTween.Sequence()
  targetItemView:MergeLightDisappear()
  sequence:Insert(0, targetItemView.transform:DOScale(0.3, 0.1))
  sequence:InsertCallback(0.1, function()
    self:_RemoveItemView(targetItemView)
  end)
  if newItemView1 ~= nil then
    newItemView1:SetFlying(true)
    newItemView1.transform.localScale = Vector3.zero
    sequence:InsertCallback(0.1, function()
      newItemView1:PlayDropAnimation()
    end)
  end
  if newItemView2 ~= nil then
    self:_PlayJumpAnimation(newItemView2, targetItemView.transform.localPosition, newItemView2.transform.localPosition)
  end
end

function BaseInteractiveBoardView:_OnDuplicateItem(message)
  local duplicateItemView = self:GetItemView(message.Duplicate)
  self:_RemoveItemView(duplicateItemView)
  local targetItemView = self:GetItemView(message.Target)
  local newItemView = self:_AddItemView(message.New)
  if newItemView ~= nil then
    self:_PlayJumpAnimation(newItemView, targetItemView.transform.localPosition, newItemView.transform.localPosition)
  end
  local position = targetItemView.transform.localPosition
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.effect_hecheng_boom_01), self.m_effectRoot, position, function(go)
  end)
end

function BaseInteractiveBoardView:_OnBoostItemSpread(message)
  local boostItemView = self:GetItemView(message.Boost)
  self:_RemoveItemView(boostItemView)
  local targetItemView = self:GetItemView(message.Target)
  local targetWorldPosition = targetItemView.transform.position
  local targetScreenPosition = self:ConvertWorldPositionToScreenPosition(targetWorldPosition)
  local targetUIWorldPosition = PositionUtil.UICameraScreen2World(targetScreenPosition)
  targetUIWorldPosition.z = 0
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.BoardBoostPrompt), GM.UIManager:GetCanvasRoot(), targetUIWorldPosition, function(go)
    local luaCmp = go:GetLuaTable()
    luaCmp:Init(message.Effect, Vector2(targetUIWorldPosition.x, targetUIWorldPosition.y))
  end)
  local position = targetItemView.transform.localPosition
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.effect_hecheng_boom_01), self.m_effectRoot, position, function(go)
  end)
end

function BaseInteractiveBoardView:_OnTimeSkip(message)
  local startItemView, cPos
  if message.Item then
    startItemView = self:GetItemView(message.Item)
    cPos = message.Item:GetPosition()
  else
    local newItem = ItemModelFactory.CreateWithCode(self.m_model, nil, message.ItemCode, true)
    startItemView = self:CreateItemView(self.m_itemsTransform, newItem, false)
    cPos = self.m_model:CreatePosition(self.m_model:GetHorizontalTiles() // 2 + 1, self.m_model:GetVerticalTiles() // 2 + 1)
    local screenPos = PositionUtil.UICameraWorld2Screen(V3Zero)
    local worldPos = self:ConvertScreenPositionToWorldPosition(screenPos)
    local localPosition = startItemView.transform.parent:InverseTransformPoint(worldPos)
    localPosition.z = 0
    startItemView.transform.localPosition = localPosition
    newItem:Destroy()
  end
  startItemView:SetFlying(true)
  local startItemTrans = startItemView.transform
  local position = startItemTrans.localPosition
  startItemTrans.localPosition = Vector3(position.x, position.y, 0)
  GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.effect_shizhong_guang_1), self.m_effectRoot, position, function(go)
    go.transform.localPosition = Vector3(position.x, position.y, 690)
  end)
  local sequence = DOTween.Sequence()
  sequence:Insert(0, startItemTrans:DOScale(2, 0.3))
  sequence:Insert(0, startItemTrans:DOShakePosition(1.5, 50, 10, 90, false))
  sequence:Insert(1, startItemTrans:DOScale(5, 0.2))
  sequence:InsertCallback(1, function()
    GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.effect_shizhong_guang_1_2), self.m_effectRoot, position, function(go)
    end)
    local screenPos = self:ConvertWorldPositionToScreenPosition(startItemTrans.position)
    local normalizedScreenPos = Vector2(screenPos.x / Screen.width, screenPos.y / Screen.height)
    GM.ModeViewController:ShowBoomEffect(normalizedScreenPos, 1)
  end)
  sequence:InsertCallback(1.2, function()
    startItemView:SetFlying(false)
    self:_RemoveItemView(startItemView)
  end)
  local item, itemView
  local maxDelay = 0
  for item, _ in pairs(self.m_model:GetAllBoardItems()) do
    if message.Item ~= item and self.m_model:CanItemMove(item) then
      itemView = self:GetItemView(item)
      if itemView ~= nil then
        local delay = self:_GetTimeSkipDelay(cPos, item:GetPosition())
        maxDelay = maxDelay < delay and delay or maxDelay
        itemView:PlayTimeSkipAnimation(delay)
      end
    end
  end
  DelayExecuteFuncInView(function()
    local items = message.EffectedItems
    for _, item in ipairs(items) do
      local itemSpread = item:GetComponent(ItemSpread)
      itemSpread:PlaySkipTimeAnim()
    end
  end, maxDelay + 0.4, self, true)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxCdSkip)
  PlatformInterface.Vibrate(EVibrationType.Heavy)
end

function BaseInteractiveBoardView:_GetTimeSkipDelay(cPos, tPos)
  return math.max(math.abs(cPos:GetX() - tPos:GetX()), math.abs(cPos:GetY() - tPos:GetY())) * 0.1 + 1
end

function BaseInteractiveBoardView:PlayBubbleDisposedAnim(sourceItemView, effectTrans, callback)
  if sourceItemView == nil then
    return
  end
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxMergeBubbleBreak)
  sourceItemView.toBeRemoved = true
  local spriteRenderer = sourceItemView:GetSpriteRenderer()
  local innerSpriteRenderer = sourceItemView:GetInnerSpriteRenderer()
  local sequence = DOTween.Sequence()
  sequence:Append(innerSpriteRenderer.transform:DOScale(0, 0.2))
  sequence:Append(spriteRenderer.transform:DOScale(2, 0.8))
  sequence:Insert(0.2, spriteRenderer:DOFade(0, 0.8))
  sequence:InsertCallback(0, function()
    local pos = sourceItemView.transform.localPosition
    pos.z = 0
    GM.ResourceLoader:LoadPrefab(GM.DataResource.ScenePrefabConfig:GetConfig(ScenePrefabConfigName.paopao_boom_1), effectTrans, pos, function()
    end)
  end)
  sequence:AppendCallback(callback)
  return sequence
end

function BaseInteractiveBoardView:_OnBubbleDisposed(message)
  if self.m_selectedBoardPosition == message.Source:GetPosition() then
    self:_UpdateIndicator(message.New, true)
    self:UpdateBoardInfoBar(message.New)
  end
  local sourceItemView = self:GetItemView(message.Source)
  local sequence = self:PlayBubbleDisposedAnim(sourceItemView, self.m_effectRoot, function()
    self:_RemoveItemView(sourceItemView)
  end)
  if message.New then
    local newItemView = self:_AddItemView(message.New)
    if newItemView ~= nil then
      newItemView.transform.localScale = Vector3.zero
      newItemView:UpdateItemAffectedEffect(self.m_lastTouchedItem and self.m_lastTouchedItem:GetModel() or nil)
    end
    sequence:Insert(0.2, newItemView.transform:DOScale(1, 0.2))
  end
  if message.Rewards then
    local worldPosition = sourceItemView and sourceItemView.transform.position or V3Zero
    local screenPosition = self:ConvertWorldPositionToScreenPosition(worldPosition)
    local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
    RewardApi.AcquireRewardsInView(message.Rewards, {
      arrWorldPos = {uiWorldPosition},
      eventLock = false
    })
  end
end

function BaseInteractiveBoardView:_OnRewardBubbleDisposed(message)
  if self.m_selectedBoardPosition == message.Source:GetPosition() then
    self:_UpdateIndicator()
    self:UpdateBoardInfoBar()
  end
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxMergeBubbleBreak)
  local sourceItemView = self:GetItemView(message.Source)
  self:PlayBubbleDisposedAnim(sourceItemView, self.m_effectRoot, function()
    self:_RemoveItemView(sourceItemView)
  end)
  if message.New then
    local rewards = {
      {
        [PROPERTY_TYPE] = message.New,
        [PROPERTY_COUNT] = 1
      }
    }
    local worldPosition = sourceItemView.transform.position
    local screenPosition = self:ConvertWorldPositionToScreenPosition(worldPosition)
    local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
    RewardApi.AcquireRewardsInView(rewards, {
      arrWorldPos = {uiWorldPosition},
      eventLock = false
    })
  end
end

function BaseInteractiveBoardView:_OnLackGem(message)
  GM.ShopModel:OnLackOfGem(message.LackNumber)
end

function BaseInteractiveBoardView:_OnLackSpreadEnergy(msg)
  GM.EnergyModel:OnLackEnergy(EnergyModel.PropertyType2EnergyType(msg.type))
end

function BaseInteractiveBoardView:_OnSpreadFailed(message)
  local key
  if message.Reason == SpreadFailedReason.ItemClosed then
    key = "hint_item_locked"
  elseif message.Reason == SpreadFailedReason.ItemOpening then
    key = "hint_item_opening"
  elseif message.Reason == SpreadFailedReason.ItemRecharging then
    key = "hint_item_recharging"
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxColdingClick)
  else
    key = "hint_board_full"
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxBoardFull)
  end
  message.Key = key
  self:_OnShowPrompt(message)
  if message.Reason == SpreadFailedReason.ItemRecharging or message.Reason == SpreadFailedReason.ItemOpening then
    self.m_infoBar:GetInfoContent():PlaySkipButtonEffect()
  end
end

function BaseInteractiveBoardView:_OnShowPrompt(message)
  local targetPosition = message.Pos
  if not targetPosition then
    local itemView = self:GetItemView(message.Item)
    targetPosition = itemView and itemView.transform.position or V3Zero
  end
  targetPosition = targetPosition + Vector3(0, 100, 0)
  local screenPosition = self:ConvertWorldPositionToScreenPosition(targetPosition)
  GM.UIManager:ShowPromptWithKey(message.Key, screenPosition, nil, false, false, 0)
end

function BaseInteractiveBoardView:_OnPutBackMaterial(message)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPlaceItem)
  local itemView = self:_AddItemView(message.Item)
  if itemView == nil then
    return
  end
  local worldPosition = message.Pos
  local sourcePosition = self.transform:InverseTransformPoint(worldPosition)
  local targetPosition = itemView.transform.localPosition
  local sourcePositionZero = Vector3(sourcePosition.x, sourcePosition.y, 0)
  local targetPositionZero = Vector3(targetPosition.x, targetPosition.y, 0)
  local transform = itemView.gameObject.transform
  transform.localPosition = sourcePositionZero
  itemView:SetFlying(true)
  local dur = 0.6
  local sequence = DOTween.Sequence()
  sequence:Append(transform:DOScale(0.6, 0.1 * dur))
  sequence:Append(transform:DOScale(1, 0.15 * dur))
  sequence:Append(transform:DOScale(2.2, 0.3 * dur))
  sequence:Append(transform:DOScale(1, 0.3 * dur))
  sequence:Append(transform:DOScale(0.7, 0.1 * dur))
  sequence:Append(transform:DOScale(1, 0.15 * dur))
  sequence:Insert(0.25 * dur, transform:DOLocalMove(targetPositionZero, 0.6 * dur))
  sequence:InsertCallback(0.9 * dur, function()
    transform.localPosition = targetPosition
    itemView:ShowSpreadLight()
  end)
  itemView:SetJumpTween(sequence, function()
    itemView:SetFlying(false)
  end)
  local item = self:GetSelectedItemModel()
  self:UpdateBoardInfoBar(item)
end

function BaseInteractiveBoardView:_OnUpdateOpeningItem()
  local hasOpeningItem = self.m_model:HasOpeningItem()
  for itemModel, _ in pairs(self.m_model:GetAllBoardItems()) do
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil then
      local itemView = self:GetItemView(itemModel)
      if itemView ~= nil then
        if itemSpread:GetState() == ItemSpreadState.Closed then
          itemView:UpdateExclamation(not hasOpeningItem)
        else
          itemView:UpdateExclamation(false)
        end
      end
    end
  end
end

function BaseInteractiveBoardView:TryStartBoardPrompt()
  self.m_bNeedTryStartBoardPrompt = true
end

function BaseInteractiveBoardView:_RealTryStartBoardPrompt()
  if not self.m_bNeedTryStartBoardPrompt then
    return
  end
  self.m_bNeedTryStartBoardPrompt = nil
  if self:_CanStartPrompt() then
    self:_StartBoardPrompt()
  else
    self:_CancelBoardPrompt()
  end
end

function BaseInteractiveBoardView:ToggleBoardPrompt(on)
  self.m_bDisableBoardPrompt = not on
end

function BaseInteractiveBoardView:_CanStartPrompt()
  if GM.UIManager:IsEventLockUntilNextPopup() or GM.TutorialModel:HasAnyStrongTutorialOngoing() or self.m_bDisableBoardPrompt then
    return false
  end
  local itemModel, itemView
  for itemModel, _ in pairs(self.m_model:GetAllBoardItems()) do
    itemView = self:GetItemView(itemModel)
    if itemView == nil or itemView:IsFlying() or itemView:IsDragging() then
      return false
    end
  end
  return true
end

function BaseInteractiveBoardView:_StartBoardPrompt()
  self:_CancelBoardPrompt()
  if self.m_boardPromptSchedulerOrigin == nil then
    function self.m_boardPromptSchedulerOrigin()
      self:_ExecuteBoardPrompt()
    end
  end
  self.m_boardPromptScheduler = self.m_boardPromptSchedulerOrigin
  local interval = self:_GetPromptInterval()
  Scheduler.Schedule(self.m_boardPromptScheduler, self, 0, 1, interval)
end

function BaseInteractiveBoardView:_ExecuteBoardPrompt()
  if self.m_boardPromptScheduler then
    Scheduler.Unschedule(self.m_boardPromptScheduler, self)
    self.m_boardPromptScheduler = nil
  end
  self.m_boardPrompt = self:_SelectPrompt(self:_GetBoardPrompts())
  local prompt = self.m_boardPrompt
  if self.m_boardPrompt ~= nil then
    self.m_boardPrompt:Start(self)
  end
end

function BaseInteractiveBoardView:_GetPromptInterval()
  local interval = GM.LevelModel:GetCurrentLevel() <= 4 and 1 or 3
  return interval
end

function BaseInteractiveBoardView:_StartBoardPromptStep2()
  return self.m_boardPrompt ~= nil and self.m_boardPrompt:StartStep2(self)
end

function BaseInteractiveBoardView:_GetBoardPrompts()
  return {}
end

function BaseInteractiveBoardView:_SelectPrompt(prompts)
  for _, prompt in ipairs(prompts) do
    if prompt:CanStart(self) then
      return prompt
    end
  end
  return nil
end

function BaseInteractiveBoardView:_CancelBoardPrompt()
  if self.m_boardPromptScheduler ~= nil then
    Scheduler.Unschedule(self.m_boardPromptScheduler, self)
    self.m_boardPromptScheduler = nil
  end
  if self.m_boardPrompt ~= nil then
    self.m_boardPrompt:Stop(self)
    self.m_boardPrompt = nil
  end
end

function BaseInteractiveBoardView:ShowHandTapEffect(targetPosition)
  self.m_handTapEffectGo:SetActive(true)
  targetPosition = Vector3(targetPosition.x, targetPosition.y, 0)
  self.m_handTapEffectGo.transform.position = targetPosition
end

function BaseInteractiveBoardView:HideHandTapEffect()
  self.m_handTapEffectGo:SetActive(false)
end

function BaseInteractiveBoardView:ShowHandDragEffect(sourcePosition, targetPosition)
  if self.m_dragTween ~= nil then
    self.m_dragTween:Kill()
    self.m_dragTween = nil
  end
  sourcePosition.z = 0
  targetPosition.z = 0
  self.m_handDragEffectGo.transform.position = sourcePosition
  self.m_handDragEffectGo:SetActive(true)
  if self.m_dragInCallback == nil then
    function self.m_dragInCallback()
      if not self.m_handDragEffectAnimator:IsNull() then
        SafeCall(function()
          self.m_handDragEffectAnimator:Play("am_shouzhi_huadong_in")
        end)
      end
    end
  end
  if self.m_dragOutCallback == nil then
    function self.m_dragOutCallback()
      if not self.m_handDragEffectAnimator:IsNull() then
        SafeCall(function()
          self.m_handDragEffectAnimator:Play("am_shouzhi_huadong_out")
        end)
      end
    end
  end
  local sequence = DOTween.Sequence():SetLoops(-1)
  sequence:AppendCallback(self.m_dragInCallback)
  sequence:AppendInterval(0.6)
  sequence:Append(self.m_handDragEffectGo.transform:DOMove(targetPosition, Vector3.Distance(sourcePosition, targetPosition) / 430))
  sequence:AppendInterval(0.2)
  sequence:AppendCallback(self.m_dragOutCallback)
  sequence:AppendInterval(1.4)
  self.m_dragTween = sequence
end

function BaseInteractiveBoardView:HideHandDragEffect()
  self.m_handDragEffectGo:SetActive(false)
  if self.m_dragTween ~= nil then
    self.m_dragTween:Kill()
    self.m_dragTween = nil
  end
end

function BaseInteractiveBoardView:UpdateBoardInfoBar(...)
  BaseBoardView.UpdateBoardInfoBar(self, ...)
  self:ToggleBoardPrompt(true)
end

function BaseInteractiveBoardView:UpdateSelectedItem(item)
  self:_UpdateIndicator(item, true)
  self:UpdateBoardInfoBar(item)
end

function BaseInteractiveBoardView:_CanShowMergeLight(itemModel, lastItemModel)
  if self.m_model:CanTwoItemsMerge(itemModel, lastItemModel) or self.m_model:CanItemAffect(lastItemModel, itemModel) then
    return true
  end
  return BaseBoardView._CanShowMergeLight(self, itemModel, lastItemModel)
end

function BaseInteractiveBoardView:_ShowMergeLight(itemView)
  BaseBoardView._ShowMergeLight(self, itemView)
  self.m_lastMergableItem = itemView:GetModel()
  self.m_lastMergableItemTime = CS.UnityEngine.Time.timeSinceLevelLoad
end

function BaseInteractiveBoardView:_UpdateIndicator(item, playAnimation)
  self.m_selectedBoardPosition = item and item:GetPosition()
  self.m_indicator:UpdateIndicator(item, playAnimation)
end

function BaseInteractiveBoardView:_SetItemViewToBeRemoved(itemModel)
  if self.m_selectedBoardPosition == itemModel:GetPosition() then
    self:_UpdateIndicator()
    self:UpdateBoardInfoBar()
  end
  local itemView = self:GetItemView(itemModel)
  itemView.toBeRemoved = true
end

function BaseInteractiveBoardView:GetSelectedItemModel()
  return self.m_selectedBoardPosition and self.m_model:GetItem(self.m_selectedBoardPosition)
end
