# TimeSpeed礼包触发条件详细分析文档

## 概述

TimeSpeed礼包（时间加速礼包）是游戏中的CD类礼包，主要包含"冷却终结器"等时间加速道具。该礼包通过多种触发条件来判断玩家是否需要时间加速功能，并在合适的时机弹出礼包购买界面。

## 礼包类型定义

### 1. 礼包分类

根据代码分析，TimeSpeed相关的礼包主要分为两类：

```lua
-- 礼包类型定义 (BundleDefinition.lua:16-35)
EBundleType = {
  CD = "cd",        -- CD加速礼包
  CDFill = "cdFill" -- CD填充礼包
}

-- UI配置 (BundleDefinition.lua:62-73)
cd = {
  window = UIPrefabConfigName.CDBundleWindow,
  entryPrefabName = UIPrefabConfigName.CDBundleButton,
  bundleType = EBundleType.CD,
  maxRewardCount = 5
},
cdFill = {
  window = UIPrefabConfigName.CDFillBundleWindow,
  entryPrefabName = UIPrefabConfigName.CDFillBundleButton,
  bundleType = EBundleType.CDFill,
  maxRewardCount = 5
}
```

### 2. 道具内容

从游戏文本可以看出，TimeSpeed礼包主要包含：

```json
// game_text_tw.json:2234-2239
"nocd_pack_title": "超級加速禮包",
"item_timespeed_1_name": "冷卻終結器",
"item_timespeed_1_desc": "使用冷卻終結器，讓所有的生產器處於滿格狀態！",
"item_cdspeed_desc2": "所有生產器都將填滿庫存並準備噴發！"
```

## 触发条件系统

### 1. 主要触发类型

TimeSpeed礼包有两个主要的触发类型：

```lua
-- 触发类型定义 (BundleDefinition.lua:36-47)
EBundleTriggerType = {
  ClickCDGenerator = "pd_cd",      -- 点击冷却中的生产器
  PdCDNumber = "pd_cd_number"      -- 冷却生产器数量达标
}
```

### 2. 触发条件详细分析

#### 2.1 PdCDNumber触发条件

**代码位置**: `BundleTriggerData.lua:23-26`

```lua
function BundleTriggerData:CanTrigger(eTriggerType, triggerArg, bundleModel)
  if eTriggerType == self.trigger then
    if eTriggerType == EBundleTriggerType.PdCDNumber then
      local configCount = self.m_dataGroup:GetGeneralConfig("pd_cd_number", EConfigParamType.Int) or 4
      return configCount <= (triggerArg or 0)
    end
  end
end
```

**触发逻辑**：
- **条件**: 棋盘上冷却中的生产器数量 >= 配置阈值（默认4个）
- **参数**: `triggerArg` 为当前冷却中的生产器数量
- **配置**: 通过服务器配置 `pd_cd_number` 可调整阈值

#### 2.2 ClickCDGenerator触发条件

**代码位置**: `BundleManager.lua:317-344`

```lua
function BundleManager:OnItemRecharge(itemModel)
  local itemDataModel = GM.ItemDataModel
  -- 统计所有冷却中的生产器
  local arrItem = GM.MainBoardModel:FilterItems(function(itemModel)
    if not itemDataModel:IsPd(itemModel:GetType()) then
      return false
    end
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and itemSpread:ShowCountDown() then
      return true
    end
    return false
  end)
  
  -- 触发PdCDNumber检查
  GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.PdCDNumber, #arrItem)
  
  local targetChainId = GM.ItemDataModel:GetChainId(itemModel:GetType())
  arrItem = GM.MainBoardModel:FilterItems(function(itemModel)
    local chainId = itemDataModel:GetChainId(itemModel:GetType())
    if targetChainId ~= chainId then
      return false
    end
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread ~= nil and not itemSpread:ShowCountDown() then
      return true
    end
  end)
  
  -- 如果同链条没有可用生产器，触发ClickCDGenerator
  if Table.IsEmpty(arrItem) then
    GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.ClickCDGenerator)
  end
end
```

**触发逻辑**：
- **触发时机**: 玩家点击生产器进行充能时
- **条件1**: 先检查冷却中生产器总数是否达到PdCDNumber阈值
- **条件2**: 如果点击的生产器所在链条没有其他可用生产器，触发ClickCDGenerator

### 3. 触发时机分析

#### 3.1 OnItemRecharge调用时机

**代码位置**: 需要查找ItemSpread或相关组件中调用OnItemRecharge的地方

该函数在以下情况被调用：
- 玩家点击生产器进行充能操作
- 生产器从空状态恢复到有库存状态
- 使用道具对生产器进行充能

#### 3.2 触发优先级

根据代码逻辑，触发优先级为：
1. **PdCDNumber**: 优先检查整体冷却生产器数量
2. **ClickCDGenerator**: 在特定条件下（同链条无可用生产器）触发

## 礼包弹出机制

### 1. 弹出链式处理

**代码位置**: `BundleManager.lua:430-471`

```lua
function BundleManager:TryStartBundlePopupChain(eTriggerType, triggerArg, checkFunc, endFunc, bOpenImmediately)
  if not self.m_bSceneViewLoaded then
    Log.Error("SceneViewLoaded 之前不应调用触发礼包！")
    return
  end
  
  local maxNum = self:GetBundleTriggerMaxNum(eTriggerType)
  local bTriggerd, viewName, bundleType = self:TryTriggerActivityBundle(eTriggerType, triggerArg, nil, bOpenImmediately)
  if bTriggerd and viewName then
    -- 设置窗口监听和链式处理逻辑
    return true
  end
  return false
end
```

**处理流程**：
1. **前置检查**: 确保场景视图已加载
2. **获取配置**: 获取该触发类型的最大弹出数量
3. **尝试触发**: 调用TryTriggerActivityBundle尝试触发礼包
4. **链式处理**: 如果成功触发，设置窗口关闭后的链式处理逻辑

### 2. 礼包触发顺序

**代码位置**: `BundleManager.lua:281-298`

```lua
function BundleManager:TryTriggerActivityBundle(eTriggerType, triggerArg, lastBundleType, bOpenImmediately)
  local bStartCheck = lastBundleType == nil
  local arrOrderList = self:GetBundleTriggerOrderList(eTriggerType)
  for _, bundleType in ipairs(arrOrderList) do
    if bStartCheck then
      local bundleModel = self:GetModel(bundleType)
      if bundleModel and bundleModel.TryTriggerBundle then
        local bTriggerd, viewName = bundleModel:TryTriggerBundle(eTriggerType, triggerArg, bOpenImmediately)
        if bTriggerd and viewName then
          return bTriggerd, viewName, bundleType
        end
      end
    elseif bundleType == lastBundleType then
      bStartCheck = true
    end
  end
  return false
end
```

**触发顺序**：
根据 `BundleTypeOrderList` 定义的顺序：
1. Starter (新手礼包)
2. Energy (体力礼包)
3. **CD (CD加速礼包)**
4. **CDFill (CD填充礼包)**
5. OrderGroup (订单组礼包)
6. MultiTier (多层礼包)
7. Chain (链式礼包)
8. OnePlusN (1+N礼包)

## 礼包可用性检查

### 1. 基础可用性检查

**代码位置**: `BundleActivityBaseModel.lua:269-301`

```lua
function BundleActivityBaseModel:_IsGroupDataEligible(dataGroup)
  local groupId = dataGroup:GetGroupId()
  local curTime = GM.GameModel:GetServerTime()
  
  -- 检查礼包是否过期
  if dataGroup:GetBundleEndTime() ~= nil and curTime > dataGroup:GetBundleEndTime() then
    return false
  end
  
  -- 检查购买次数限制
  local curBuyNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.MaxBuyNum)) or 0
  local maxBuyNum = dataGroup:GetMaxBuyNum()
  if maxBuyNum ~= nil and curBuyNum >= maxBuyNum then
    return false
  end
  
  -- 检查购买冷却时间
  local buyCD = dataGroup:GetBuyCD()
  if buyCD ~= nil and curTime >= triggertime + dataGroup:GetLastDuration() and 
     curTime < triggertime + dataGroup:GetLastDuration() + buyCD then
    return false
  end
  
  -- 检查每日购买次数
  local maxDailyBuyNum = dataGroup:GetDailyBuyNum()
  if maxDailyBuyNum == 0 then
    return false
  end
  
  return true
end
```

**检查项目**：
1. **时间有效性**: 礼包未过期
2. **购买次数**: 未达到最大购买次数限制
3. **冷却时间**: 不在购买冷却期内
4. **每日限制**: 未达到每日购买次数限制

### 2. 弹窗显示检查

**代码位置**: `BundleActivityBaseModel.lua:369-387`

```lua
function BundleActivityBaseModel:_CanTriggerBundleWindow(dataGroup, eTriggerType)
  local groupId = dataGroup:GetGroupId()
  local curTime = GM.GameModel:GetServerTime()
  
  -- 检查弹窗冷却时间
  if dataGroup:GetPopCD() then
    local lastPopTime = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastPopTime)) or 0
    if curTime < lastPopTime + dataGroup:GetPopCD() then
      return false
    end
  end
  
  -- 检查每日显示次数
  local curDay = GM.GameModel:GetServerDay()
  local lastShowDay = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastShowDay)) or 0
  if lastShowDay ~= curDay then
    return true
  end
  
  local dailyShowNum = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.DailyShowNum)) or 0
  if dataGroup:GetDailyShowNum() and dailyShowNum >= dataGroup:GetDailyShowNum() then
    return false
  end
  
  return true
end
```

**检查项目**：
1. **弹窗冷却**: 距离上次弹窗时间足够长
2. **每日显示**: 未达到每日最大显示次数

## 教程系统集成

### 1. CD加速教程

**代码位置**: `TutorialExecuterCDSpeed.lua:63-77`

```lua
function Executer:_CanExecuteStep1()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return false
  end
  local items = ItemSpeeder.GetEffectedItems(GM.MainBoardModel)
  local canTutorial = false
  for _, item in ipairs(items) do
    local spread = item:GetComponent(ItemSpread)
    local remainDuration = (1 - spread:GetTimerAmount()) * spread:GetTimerDuration()
    if spread:GetStorageRestNumber(true) == 0 and 10 < remainDuration then
      canTutorial = true
    end
  end
  return canTutorial
end
```

**教程触发条件**：
- 在棋盘游戏模式下
- 存在可被时间加速器影响的物品
- 物品库存为0且剩余冷却时间超过10秒

### 2. 教程配置

**代码位置**: `TutorialConfig.lua:325-339`

```lua
{
  id = "cd_speed",
  desc = "CD道具",
  condition = {
    {
      type = "OrderGroupFinished",
      args = {2, 3}  -- 第2章第3个订单组完成
    },
    {
      type = "MainLevel", 
      args = {5}     -- 主等级达到5级
    }
  },
  executer = "CDSpeed"
}
```

**教程前置条件**：
- 完成第2章第3个订单组
- 主等级达到5级

## 配置系统

### 1. 服务器配置结构

TimeSpeed礼包的行为主要由服务器配置控制，支持动态调整触发条件。

**配置示例**：
```json
{
  "bundle_cd": {
    "groups": [
      {
        "groupId": 1,
        "trigger": "pd_cd",
        "generalConfig": {
          "pd_cd_number": 4,
          "pop_cd": 3600,
          "daily_show_num": 3
        },
        "rewards": [
          {"type": "timespeed_1", "amount": 3},
          {"type": "gem", "amount": 100}
        ]
      }
    ]
  }
}
```

**配置字段说明**：
- `pd_cd_number`: PdCDNumber触发所需的最小冷却生产器数量
- `pop_cd`: 弹窗冷却时间（秒）
- `daily_show_num`: 每日最大显示次数
- `rewards`: 礼包包含的奖励内容

### 2. 本地配置

**代码位置**: `BundleDefinition.lua:62-73`

本地配置定义了礼包的UI表现和基础参数：

```lua
cd = {
  window = UIPrefabConfigName.CDBundleWindow,        -- 礼包窗口预制体
  entryPrefabName = UIPrefabConfigName.CDBundleButton, -- 入口按钮预制体
  bundleType = EBundleType.CD,                       -- 礼包类型
  maxRewardCount = 5                                 -- 最大奖励数量
}
```

## 数据持久化

### 1. 数据库键定义

**代码位置**: `BundleDefinition.lua:2-13`

```lua
EBundleDBKey = {
  TriggerTime = "triggerTime",           -- 触发时间
  MaxBuyNum = "maxBuyNum",              -- 最大购买次数
  DailyBuyNum = "dailyBuyNum",          -- 每日购买次数
  LastBuyDay = "lastBuyDay",            -- 最后购买日期
  LastShowDay = "lastShowDay",          -- 最后显示日期
  DailyShowNum = "dailyShowNum",        -- 每日显示次数
  LastPopTime = "lastPopTime",          -- 最后弹窗时间
  TriggerDailyShowNum = "tgDailyShowNum_%s", -- 触发类型每日显示次数
  TriggerLastShowDay = "tgLastShowDay"   -- 触发类型最后显示日期
}
```

### 2. 数据存储机制

**代码位置**: `BundleActivityBaseModel.lua:475-490`

```lua
function BundleActivityBaseModel:TryRefreshTriggerDailyNum(groupId)
  local lastDay = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.TriggerLastShowDay)) or 0
  local curDay = GM.GameModel:GetServerDay()
  if lastDay < curDay then
    self:_ResetAllTriggerDailyNum(groupId)
    self:_SetBundleDBData(groupId, EBundleDBKey.TriggerLastShowDay, tostring(curDay))
  end
end

function BundleActivityBaseModel:_ResetAllTriggerDailyNum(groupId)
  for _, triggerType in pairs(EBundleTriggerType) do
    if self:_GetTriggerDailyNum(triggerType, groupId) ~= 0 then
      self:_SetTriggerDailyNum(0, triggerType, groupId)
    end
  end
end
```

**数据管理特点**：
- **每日重置**: 每天自动重置触发次数计数
- **分类统计**: 按触发类型分别统计显示次数
- **持久化存储**: 所有数据保存到本地数据库

## 触发流程详细分析

### 1. 完整触发链路

```mermaid
graph TD
    A[玩家点击生产器] --> B[OnItemRecharge调用]
    B --> C[统计冷却中生产器数量]
    C --> D{数量 >= pd_cd_number?}
    D -->|是| E[触发PdCDNumber]
    D -->|否| F[检查同链条可用生产器]
    F --> G{同链条有可用生产器?}
    G -->|否| H[触发ClickCDGenerator]
    G -->|是| I[不触发]
    E --> J[TryStartBundlePopupChain]
    H --> J
    J --> K[获取触发配置]
    K --> L[TryTriggerActivityBundle]
    L --> M[按优先级遍历礼包类型]
    M --> N{CD礼包可触发?}
    N -->|是| O[检查弹窗条件]
    N -->|否| P[检查CDFill礼包]
    O --> Q{满足弹窗条件?}
    Q -->|是| R[显示礼包窗口]
    Q -->|否| S[触发成功但不显示]
    P --> T[类似CD礼包流程]
```

### 2. 关键检查点

#### 2.1 生产器状态检查

**代码位置**: `BundleManager.lua:319-328`

```lua
local arrItem = GM.MainBoardModel:FilterItems(function(itemModel)
  if not itemDataModel:IsPd(itemModel:GetType()) then
    return false  -- 必须是生产器类型
  end
  local itemSpread = itemModel:GetComponent(ItemSpread)
  if itemSpread ~= nil and itemSpread:ShowCountDown() then
    return true   -- 显示倒计时的生产器（冷却中）
  end
  return false
end)
```

**检查条件**：
- 物品类型必须是生产器（Pd）
- 生产器必须处于冷却状态（ShowCountDown() = true）

#### 2.2 链条可用性检查

**代码位置**: `BundleManager.lua:331-343`

```lua
local targetChainId = GM.ItemDataModel:GetChainId(itemModel:GetType())
arrItem = GM.MainBoardModel:FilterItems(function(itemModel)
  local chainId = itemDataModel:GetChainId(itemModel:GetType())
  if targetChainId ~= chainId then
    return false  -- 必须是同一链条
  end
  local itemSpread = itemModel:GetComponent(ItemSpread)
  if itemSpread ~= nil and not itemSpread:ShowCountDown() then
    return true   -- 不在冷却中的生产器
  end
end)

if Table.IsEmpty(arrItem) then
  GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.ClickCDGenerator)
end
```

**检查逻辑**：
- 获取点击生产器的链条ID
- 查找同链条中不在冷却的生产器
- 如果同链条没有可用生产器，触发ClickCDGenerator

### 3. 触发频率控制

#### 3.1 每日触发次数限制

**代码位置**: `BundleActivityBaseModel.lua:388-406`

```lua
function BundleActivityBaseModel:_CanTriggerBundleWindow(dataGroup, eTriggerType)
  -- 检查触发类型的每日显示次数
  self:TryRefreshTriggerDailyNum(dataGroup:GetGroupId())
  local triggerDailyShowNum = self:_GetTriggerDailyNum(eTriggerType, dataGroup:GetGroupId())
  local maxTriggerDailyShowNum = dataGroup:GetTriggerDailyShowNum(eTriggerType)
  if maxTriggerDailyShowNum and triggerDailyShowNum >= maxTriggerDailyShowNum then
    return false
  end

  return true
end
```

**限制机制**：
- 每种触发类型有独立的每日显示次数限制
- 每天自动重置计数器
- 达到限制后当天不再显示该类型触发的礼包

#### 3.2 弹窗冷却机制

**代码位置**: `BundleActivityBaseModel.lua:369-378`

```lua
if dataGroup:GetPopCD() then
  local lastPopTime = tonumber(self:_GetBundleDBData(groupId, EBundleDBKey.LastPopTime)) or 0
  if curTime < lastPopTime + dataGroup:GetPopCD() then
    return false
  end
end
```

**冷却机制**：
- 每个礼包组有独立的弹窗冷却时间
- 在冷却期内不会重复弹出
- 冷却时间可通过服务器配置调整

## 用户体验优化

### 1. 智能触发时机

TimeSpeed礼包的触发设计考虑了用户的实际需求：

**场景1: 大量生产器冷却**
- 当4个或更多生产器同时冷却时触发
- 此时用户最需要时间加速功能

**场景2: 关键链条阻塞**
- 当用户点击的生产器链条完全阻塞时触发
- 帮助用户解决生产瓶颈

### 2. 防骚扰机制

**多重限制保护**：
- 弹窗冷却时间（默认1小时）
- 每日显示次数限制
- 购买后的冷却期
- 教程完成前不触发

### 3. 渐进式引导

**教程集成**：
- 在合适的游戏进度引入概念
- 通过实际需求场景进行教学
- 避免过早打扰新手玩家

## 总结

### 核心特点

1. **需求驱动触发**
   - 基于实际游戏状态判断用户需求
   - 在用户遇到时间瓶颈时精准触发
   - 避免无意义的打扰

2. **多层次检查机制**
   - 生产器状态检查
   - 链条可用性分析
   - 数量阈值判断
   - 时间和频率限制

3. **灵活的配置系统**
   - 服务器端可动态调整触发参数
   - 支持A/B测试和精细化运营
   - 本地配置管理UI表现

4. **完善的数据统计**
   - 详细记录触发和购买数据
   - 支持按触发类型分类统计
   - 为产品优化提供数据支持

该系统通过精心设计的触发条件和用户体验优化，在提供有价值的付费内容的同时，最大程度地减少了对用户游戏体验的干扰。
