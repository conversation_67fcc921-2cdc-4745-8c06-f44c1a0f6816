CoinRaceEntry = setmetatable({}, HudGeneralButton)
CoinRaceEntry.__index = CoinRaceEntry

function CoinRaceEntry:Init(model)
  self.m_model = model
  if self.m_model == nil then
    return
  end
  self.m_activityType = self.m_model:GetType()
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self.m_spineAnimation:Init()
  self.m_lastUpdateTime = 0
  self:_UpdateRankText()
  self:UpdatePerSecond()
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self._UpdateRankText)
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.OpenMainWindow, {
    obj = self,
    method = self._UpdateRankText
  })
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.ScoreChanged, {
    obj = self,
    method = self._UpdateRankText
  })
end

function CoinRaceEntry:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
  HudGeneralButton.OnDestroy(self)
end

function CoinRaceEntry:UpdatePerSecond()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  elseif self.gameObject.activeSelf then
    self.gameObject:SetActive(false)
  end
  local serTime = GM.GameModel:GetServerTime()
  if serTime - self.m_lastUpdateTime > 120 then
    self.m_lastUpdateTime = serTime
    self:_UpdateRankText()
  end
end

function CoinRaceEntry:OnBtnClicked()
  local state = self.m_model:GetState()
  if state == ActivityState.Preparing then
    GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, false, false)
  elseif state == ActivityState.Started then
    if self.m_model:IsInRace() then
      self.m_model:TryOpenMainWindow()
    else
      GM.UIManager:OpenView(self.m_activityDefinition.NoticeWindowPrefabName, self.m_activityType, false, true)
    end
  end
end

function CoinRaceEntry:_UpdateRankText()
  if self.m_model == nil or not self.m_model:HasNetwork() then
    return
  end
  self.m_exclamationGo:SetActive(self.m_model:CanShowExclaimation())
  local rank = not self.m_model:IsInRace() and 1 or self.m_model:GetMyRank(true)
  if rank == nil or rank == 0 or rank == self.m_rank then
    return
  end
  self.m_spineAnimation:SetSkin(tostring(rank))
  self.m_spineAnimation:PlayAnimation("idle", nil, true)
  self.m_rank = rank
  self.m_rank = rank
end
