TestModel = {}
TestModel.__index = TestModel
local arrSwitchTestPlayerPrefs = {
  {
    EPlayerPrefKey.TestLockServerKey,
    "清除数据不切换服务器",
    true
  },
  {
    EPlayerPrefKey.TestSkipStartVideo,
    "跳过开场视频"
  },
  {
    EPlayerPrefKey.TestShowTextKey,
    "显示文案key"
  },
  {
    EPlayerPrefKey.TestUnlockAllTask,
    "解锁全剧情"
  },
  {
    EPlayerPrefKey.TestSuperTap,
    "母体棋子超级点击"
  },
  {
    EPlayerPrefKey.TestIgnoreTutorial,
    "禁止所有引导"
  },
  {
    EPlayerPrefKey.ShowItemTestInfo,
    "显示测试信息"
  },
  {
    EPlayerPrefKey.TestEnableBackwardProgress,
    "允许回退进度"
  },
  {
    EPlayerPrefKey.TestCheckRuntimeLanguage,
    "运行时检查多语言"
  },
  {
    EPlayerPrefKey.TestAlwaysBubble,
    "泡泡概率MAX"
  },
  {
    EPlayerPrefKey.TestIgnorePopupChain,
    "禁止所有弹窗"
  },
  {
    EPlayerPrefKey.TestFavoriteItemContent,
    "收藏棋子栏"
  },
  {
    EPlayerPrefKey.TestShowAllOrderBubbles,
    "显示所有场景订单气泡"
  },
  {
    EPlayerPrefKey.TestUnityIAPFail,
    "Unity模拟支付失败"
  }
}

function TestModel:GetSwitchTestPlayerPrefs()
  return arrSwitchTestPlayerPrefs
end

local ETestDataKey = {
  FileReplace = "fileReplace",
  OrderGroup = "orderGroup",
  DeployedOrderGroup = "deployedOrderGroup",
  RecordOrderGroupId = "recordOrderGroupId"
}

function TestModel:Init()
  self.CanCompleteAll = false
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.Test)
  local strFileReplaceData = self.m_dbTable:GetValue(ETestDataKey.FileReplace, DB_VALUE_KEY)
  self.m_mapTestFileReplaces = json.decode(strFileReplaceData) or {}
  local mapInvalidConfigNames = {}
  for configName, option in pairs(self.m_mapTestFileReplaces) do
    if option ~= TEST_NO_SUFFIX and not TextAssetConfigName.HasConfig(configName .. "_" .. tostring(option)) then
      mapInvalidConfigNames[configName] = true
    end
  end
  for invalidConfigName, _ in pairs(mapInvalidConfigNames) do
    self.m_mapTestFileReplaces[invalidConfigName] = nil
  end
  self:LoadTestOrderGroups()
end

function TestModel:LateInit()
  self:RefreshTestItemContent()
end

function TestModel:Update()
  if Input.GetKey(KeyCode.BackQuote) and not GM.UIManager:IsViewOpen(UIPrefabConfigName.TestCommandView) then
    GM.UIManager:OpenView(UIPrefabConfigName.TestCommandView)
  end
end

function TestModel:ClearData(exit)
  self.m_clearing = true
  local uuid = GM.UserModel:GetInstallUuid()
  GM.DBTableManager:Clear()
  GM.DBTableManager:TrySaveAll()
  self:ClearTestPlayerPrefs()
  GM.UserModel:SetInstallUuid(uuid)
  if exit == nil or exit then
    PlatformInterface.ExitGame()
  end
end

function TestModel:ClearServerData(exitGame, callback)
  callback = callback or function()
  end
  GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "TIP", "Are you sure you want to clear server data? You have to play from scratch.", "Confirm", "Cancel", function(tbWindow)
    TestMessage.ClearData(function(success, tbResp)
      if success and tbResp.rcode == 0 then
        GM.TestModel:ClearData(false)
        if exitGame then
          GM.skipDBSerialize = true
          GM:RestartGame(nil, EBIProjectType.RestartGameAction.Test)
        end
        callback(true)
      else
        GM.UIManager:ShowPrompt("清除后台数据失败")
        callback(false)
      end
    end)
  end, function(tbWindow)
    tbWindow:Close()
    callback(false)
  end, nil, function(window)
    local curOrder = 29999
    window:SetSortingOrder(curOrder)
  end)
end

function TestModel:ClearTestPlayerPrefs()
  local mapTempData = {}
  for _, data in ipairs(arrSwitchTestPlayerPrefs) do
    mapTempData[data[1]] = PlayerPrefs.GetInt(data[1], data[3] and 1 or 0)
  end
  local server, schema
  if PlayerPrefs.GetInt(EPlayerPrefKey.TestLockServerKey, 1) == 1 then
    server = NetworkConfig.GetSelectedServer()
    schema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema, "")
  end
  local favoriteItems = PlayerPrefs.GetString(EPlayerPrefKey.FavoriteItems)
  local group = PlayerPrefs.GetInt(EPlayerPrefKey.TestABGroup, 0)
  local luaDebugger = PlayerPrefs.GetInt("lua.debugger", 2)
  local luaDebugAddress = PlayerPrefs.GetString(EPlayerPrefKey.TestDebugAddress, "localhost")
  local isMusicMute = GM.AudioModel:GetAudioMute(AudioType.Music)
  local isEffectMute = GM.AudioModel:GetAudioMute(AudioType.Effect)
  PlayerPrefs.DeleteAll()
  GM.SimplePrefs:Clear()
  for playerprefkey, value in pairs(mapTempData) do
    PlayerPrefs.SetInt(playerprefkey, value)
  end
  if server then
    NetworkConfig.SetSelectedServer(server)
    PlayerPrefs.SetString(EPlayerPrefKey.TestServerSchema, schema)
  end
  PlayerPrefs.SetInt(EPlayerPrefKey.TestABGroup, group)
  PlayerPrefs.SetString(EPlayerPrefKey.FavoriteItems, favoriteItems)
  PlayerPrefs.SetInt("lua.debugger", luaDebugger)
  PlayerPrefs.SetString(EPlayerPrefKey.TestDebugAddress, luaDebugAddress)
  GM.AudioModel:SetAudioMute(AudioType.Music, isMusicMute, true)
  GM.AudioModel:SetAudioMute(AudioType.Effect, isEffectMute, true)
end

function TestModel:DeleteDataBase(exit)
  GM.DBTableManager:ClearTables()
  PlayerPrefs.DeleteAll()
  GM.SimplePrefs:Clear()
  if exit == nil or exit then
    PlatformInterface.ExitGame()
  end
end

function TestModel:IsClearing()
  return self.m_clearing == true
end

function TestModel:UpdateServerFunction(callback)
  local schema = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema, "")
  if not (GameConfig.IsTestMode() and NetworkConfig.IsTestServer4()) or StringUtil.IsNilOrEmpty(schema) then
    return
  end
  local url = "https://cola-test-04.global.corp.merge.fun/server/sync_schema?name=" .. schema
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(url, "GET", 8000, 2)
  reqCtx.MaxRetry = 0
  reqCtx:SetHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader())
  reqCtx:SetHeader(NetworkConfig.SchemaHeaderKey, tostring(GM.UserModel:GetUserId()))
  local reqCallback = function()
    if reqCtx.Rcode ~= ResultCode.Succeeded then
      GM.UIManager:ShowPrompt("test4更新function失败:" .. tostring(reqCtx.ErrorMsg))
    end
    callback()
  end
  reqCtx:SetCallback(reqCallback)
  reqCtx:Send()
end

function TestModel:AddItemOnBoard(boardModel, position, code)
  local newItem = boardModel:GenerateItem(position, code)
  boardModel.event:Call(BoardEventType.PopCachedItem, {New = newItem})
end

function TestModel:AddItem(strInput)
  local currentBoardModel = BoardModelHelper.GetActiveModel()
  if currentBoardModel == nil then
    GM.UIManager:ShowPrompt("当前不在棋盘场景")
    return
  end
  local arrValues = StringUtil.Split(strInput, ",")
  if #arrValues == 0 then
    GM.UIManager:ShowPrompt("输入为空或不合法")
    return
  end
  local failedValues, targetGameMode, targetBoardModel
  for _, value in ipairs(arrValues) do
    if GM.TestModel:ItemCodeValid(value) then
      targetGameMode = ItemUtility.GetModeByCode(value)
      targetBoardModel = BoardModelHelper.GetModelByGameMode(targetGameMode)
      if targetGameMode ~= EGameMode.Board and targetBoardModel ~= nil then
        targetBoardModel:CacheItems({value}, CacheItemType.Stack)
      elseif targetGameMode == EGameMode.Board then
        GM.MainBoardModel:CacheItems({value}, CacheItemType.Stack)
      end
    else
      failedValues = (failedValues or "") .. value .. " "
    end
  end
  if failedValues ~= nil then
    GM.UIManager:ShowPrompt("添加棋子失败:" .. failedValues)
  else
    GM.UIManager:ShowPrompt("添加棋子成功:" .. strInput)
  end
end

function TestModel:AddOrderItems()
  local boardModel = BoardModelHelper.GetActiveModel()
  if not boardModel then
    GM.UIManager:ShowTestPrompt("请在棋盘使用此功能")
    return
  end
  if not boardModel.GetOrderCodeRequireCount then
    GM.UIManager:ShowPrompt("此棋盘无订单模块")
    return
  end
  local orderItems = boardModel:GetOrderCodeRequireCount()
  for k, v in pairs(orderItems) do
    for i = 1, v do
      local position = boardModel:FindEmptyPositionInValidOrder()
      if position == nil then
        boardModel:CacheItems({k}, CacheItemType.Stack)
      else
        GM.TestModel:AddItemOnBoard(boardModel, position, k)
      end
    end
  end
end

function TestModel:AddNextOrderItems()
  local boardModel = BoardModelHelper.GetActiveModel()
  if not boardModel then
    GM.UIManager:ShowPrompt("请在棋盘使用此功能")
    return
  end
  if not boardModel.GetOrderModel then
    GM.UIManager:ShowPrompt("此棋盘无订单模块")
    return
  end
  local orderModel = boardModel:GetOrderModel()
  if not orderModel then
    GM.UIManager:ShowPrompt("没有订单！")
    return
  end
  local arrNextConfigs = orderModel:GetNextFixedOrderConfigs()
  if Table.IsEmpty(arrNextConfigs) then
    GM.UIManager:ShowPrompt("没有固定订单了！")
    return
  end
  local getRequirementsTb = function(orderConfig)
    local requirements = {}
    local prefix = "Requirement_"
    local tb
    for i = 1, 3 do
      tb = orderConfig[prefix .. i]
      if tb ~= nil and tb.Type ~= CLEAN_ITEM_CODE then
        table.insert(requirements, tb)
      end
    end
    return requirements
  end
  local requirements
  for _, nextConfig in ipairs(arrNextConfigs) do
    requirements = getRequirementsTb(nextConfig)
    for _, v in pairs(requirements) do
      for i = 1, v.Count do
        local position = boardModel:FindEmptyPositionInValidOrder()
        if position == nil then
          boardModel:CacheItems({
            v.Type
          }, CacheItemType.Stack)
        else
          GM.TestModel:AddItemOnBoard(boardModel, position, v.Type)
        end
      end
    end
  end
end

function TestModel:RemoveItemOnBoard(x, y)
  local boardModel = BoardModelHelper.GetActiveModel()
  local position = boardModel:CreatePosition(x, y)
  self:RemoveItem(position)
end

function TestModel:RemoveAll()
  local count = 0
  local boardModel = BoardModelHelper.GetActiveModel()
  for itemModel, _ in pairs(boardModel:GetAllBoardItems(true)) do
    count = count + 1
    self:RemoveItem(itemModel:GetPosition())
  end
  GM.UIManager:ShowPrompt("删除了" .. count .. "个棋子")
end

function TestModel:RemoveAllNormal()
  local count = 0
  local boardModel = BoardModelHelper.GetActiveModel()
  if not boardModel then
    GM.UIManager:ShowPrompt("请在棋盘使用此功能")
    return
  end
  local itemCode
  local arrEqs = {}
  for itemModel, _ in pairs(boardModel:GetAllBoardItems(true)) do
    itemCode = itemModel:GetCode()
    if StringUtil.StartWith(itemCode, "eq_") then
      arrEqs[#arrEqs + 1] = itemModel
    elseif not StringUtil.StartWith(itemCode, "pd_") and boardModel:CanItemMove(itemModel) then
      self:RemoveItem(itemModel:GetPosition())
      count = count + 1
    end
  end
  local itemModel
  local arrInventoryItemsToRemove = {}
  for i = 1, boardModel:GetStoredItemCount() do
    itemModel = boardModel:GetStoredItem(i)
    itemCode = itemModel:GetCode()
    if StringUtil.StartWith(itemCode, "eq_") then
      arrEqs[#arrEqs + 1] = itemModel
    elseif not StringUtil.StartWith(itemCode, "pd_") then
      arrInventoryItemsToRemove[#arrInventoryItemsToRemove + 1] = itemModel
    end
  end
  local itemStoreModel = boardModel:GetStoreModel()
  for _, itemModel in ipairs(arrInventoryItemsToRemove) do
    itemStoreModel:RemoveItemByItemModel(itemModel)
    boardModel:RemoveItem(itemModel)
  end
  count = count + #arrInventoryItemsToRemove
  EventDispatcher.DispatchEvent(EEventType.OnInventoryCapacityUpdate)
  local countInCook = 0
  local itemCook, cookState
  for _, eqModel in ipairs(arrEqs) do
    itemCook = eqModel:GetComponent(ItemCook)
    if itemCook then
      countInCook = countInCook + itemCook:TestDeleteContent()
    end
  end
  boardModel:UpdateOrderState()
  GM.UIManager:CloseView(UIPrefabConfigName.InventoryWindow)
  GM.UIManager:ShowPrompt("删除了" .. count + countInCook .. "个棋子，其中厨具中删了" .. countInCook .. "个")
end

function TestModel:RemoveItem(position)
  local boardModel = BoardModelHelper.GetActiveModel()
  if boardModel == nil then
    return
  end
  local item = boardModel:GetItem(position)
  if item == nil then
    return
  end
  boardModel:RemoveItem(item)
  boardModel.event:Call(BoardEventType.CostItem, {Source = item})
end

function TestModel:RemoveCachedQueue()
  local boardModel = BoardModelHelper.GetActiveModel()
  if boardModel == nil then
    return
  end
  while boardModel:GetCachedItemCount() > 0 do
    boardModel:RemoveCachedItem(1)
  end
  EventDispatcher.DispatchEvent(EEventType.ChangeCachedItems)
end

function TestModel:GetItemTypesInSequence()
  if self.m_arrItemTypeInSeq == nil then
    self.m_arrItemTypeInSeq = GM.ItemDataModel:GetAllItemTypes()
    local sortFunc = function(a, b)
      local chainA = GM.ItemDataModel:GetChainId(a)
      local chainB = GM.ItemDataModel:GetChainId(b)
      if StringUtil.StartWith(chainA, "ds_") and StringUtil.StartWith(chainB, "ds_") then
        local configA = GM.ItemDataModel:GetModelConfig(a)
        local configB = GM.ItemDataModel:GetModelConfig(b)
        local bookOrderA = configA.BookOrder or 0
        local bookOrderB = configB.BookOrder or 0
        if bookOrderA ~= bookOrderB then
          return bookOrderA < bookOrderB
        end
        local subOrderA = configA.BookSubOrder or 0
        local subOrderB = configB.BookSubOrder or 0
        if subOrderA ~= subOrderB then
          return subOrderA < subOrderB
        end
      end
      if chainA ~= chainB then
        if StringUtil.StartWith(chainA, "it_") and StringUtil.StartWith(chainB, "it_") or StringUtil.StartWith(chainA, "pd_") and StringUtil.StartWith(chainB, "pd_") then
          local keyA = self:_GetItemGroupKey(a)
          local keyB = self:_GetItemGroupKey(b)
          local numKeyA = tonumber(keyA)
          local numKeyB = tonumber(keyB)
          if keyA ~= keyB then
            if numKeyA and numKeyB then
              return numKeyA < numKeyB
            else
              return keyA < keyB
            end
          end
        end
        if StringUtil.StartWith(chainA, "pd_") and StringUtil.StartWith(chainB, "it_") then
          return true
        end
        if StringUtil.StartWith(chainA, "it_") and StringUtil.StartWith(chainB, "pd_") then
          return false
        end
        return chainA < chainB
      end
      return GM.ItemDataModel:GetChainLevel(a) < GM.ItemDataModel:GetChainLevel(b)
    end
    table.sort(self.m_arrItemTypeInSeq, sortFunc)
    self.m_arrMapItemTypeKeyInSeq = {}
    self.m_mapItemType = {}
    for _, v in ipairs(self.m_arrItemTypeInSeq) do
      local group = self:_GetItemGroupKey(v)
      if self.m_mapItemType[group] == nil then
        self.m_mapItemType[group] = {}
        self.m_arrMapItemTypeKeyInSeq[#self.m_arrMapItemTypeKeyInSeq + 1] = group
      end
      self.m_mapItemType[group][#self.m_mapItemType[group] + 1] = v
    end
    for _, v in pairs(self.m_mapItemType) do
      table.sort(v, sortFunc)
    end
    local priorityGroup = {other = 1, box = 2}
    table.sort(self.m_arrMapItemTypeKeyInSeq, function(a, b)
      local priorityA = priorityGroup[a]
      local priorityB = priorityGroup[b]
      if priorityA and priorityB then
        return priorityA < priorityB
      elseif priorityA and not priorityB then
        return true
      elseif priorityB and not priorityA then
        return false
      end
      local numberA = tonumber(a)
      local numberB = tonumber(b)
      if numberA and numberB then
        return numberA < numberB
      elseif numberA and not numberB then
        return true
      elseif numberB and not numberA then
        return false
      end
      return a < b
    end)
  end
  return self.m_arrItemTypeInSeq
end

function TestModel:GetItemTypesInMap()
  if not self.m_mapItemType then
    self:GetItemTypesInSequence()
  end
  return self.m_mapItemType
end

function TestModel:GetMapItemTypeKeysInSequence()
  if not self.m_arrMapItemTypeKeyInSeq then
    self:GetItemTypesInSequence()
  end
  return self.m_arrMapItemTypeKeyInSeq
end

function TestModel:_GetItemGroupKey(itemCode)
  local chainId = GM.ItemDataModel:GetChainId(itemCode)
  if StringUtil.StartWith(chainId, "it_") then
    local first = string.find(chainId, "_")
    return string.sub(chainId, first + 1, string.find(chainId, "_", first + 1) - 1)
  elseif StringUtil.StartWith(chainId, "pd_") or StringUtil.StartWith(chainId, "eq_") then
    local pos = string.find(chainId, "_")
    if pos == nil then
      Log.Error("棋子id 错误：" .. itemCode)
    end
    return string.sub(chainId, pos and pos + 1 or 0)
  elseif StringUtil.StartWith(chainId, "ds_") then
    local config = GM.ItemDataModel:GetModelConfig(itemCode)
    local order = config.BookOrder or 0
    return tostring(order)
  elseif chainId == "ene" or chainId == "gem" or chainId == "gold" then
    return "property"
  elseif string.find(chainId, "box") ~= nil then
    return "box"
  elseif string.match(itemCode, "eb[0-9]+_") then
    return "extraBoard"
  end
  return "other"
end

local mapInvalidCodes = {
  [ItemType.Cobweb] = true,
  [ItemType.PaperBox] = true,
  [ItemType.Bubble] = true,
  [ItemType.RewardBubble] = true
}

function TestModel:ItemCodeValid(code)
  local result = mapInvalidCodes[code] == nil and pcall(function()
    local tempItem = ItemModelFactory.CreateWithCode(nil, nil, code, false)
    Log.Assert(tempItem ~= nil, "TestModel:ItemCodeValid:" .. tostring(code))
    tempItem:Destroy()
  end)
  return result
end

function TestModel:GetFavoriteItems()
  if self.m_arrFavoriteItems == nil then
    self.m_arrFavoriteItems = StringUtil.Split(PlayerPrefs.GetString(EPlayerPrefKey.FavoriteItems, ""), ",")
    self.m_arrFavoriteItems = Table.ListRemoveDuplicate(self.m_arrFavoriteItems)
    self.m_arrFavoriteItems = Table.ListSelect(self.m_arrFavoriteItems, function(item)
      return self:ItemCodeValid(item)
    end)
  end
  return self.m_arrFavoriteItems
end

function TestModel:SaveFavoriteItems()
  local string = table.concat(self.m_arrFavoriteItems, ",")
  local stringB = PlayerPrefs.GetString(EPlayerPrefKey.FavoriteItems, "")
  PlayerPrefs.SetString(EPlayerPrefKey.FavoriteItems, table.concat(self.m_arrFavoriteItems, ","))
end

function TestModel:AddItemToFavorite(code)
  if not self:ItemCodeValid(code) then
    return false
  end
  self:GetFavoriteItems()
  if self:IsFavoriteItem(code) then
    return false
  end
  self.m_arrFavoriteItems[#self.m_arrFavoriteItems + 1] = code
  self:SaveFavoriteItems()
  EventDispatcher.DispatchEvent(EEventType.FavoriteItemChanged)
  return true
end

function TestModel:RemoveItemFromFavorite(code)
  local index = self:IsFavoriteItem(code)
  if not index then
    return false
  end
  table.remove(self.m_arrFavoriteItems, index)
  self:SaveFavoriteItems()
  EventDispatcher.DispatchEvent(EEventType.FavoriteItemChanged)
  return true
end

function TestModel:IsFavoriteItem(code)
  return Table.ListContain(self:GetFavoriteItems(), code)
end

function TestModel:OpenFavoriteContent()
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.TestFavoriteItemContent), GM.UIManager:GetCanvasRoot(), Vector3.zero, function(go)
    local content = go:GetLuaTable()
    content:Init()
  end)
end

function TestModel:RefreshTestItemContent()
  local showFav = PlayerPrefs.GetInt(EPlayerPrefKey.TestFavoriteItemContent, 0) == 1
  local hasFav = TestFavoriteItemContent.GetInstance() ~= nil
  if showFav and not hasFav then
    GM.TestModel:OpenFavoriteContent()
  elseif hasFav and not showFav then
    TestFavoriteItemContent.GetInstance():OnCloseButtonClicked()
  end
end

function TestModel:OpenAllItemContent()
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.TestAllItemContent), GM.UIManager:GetCanvasRoot(), Vector3.zero, function(go)
    local content = go:GetLuaTable()
    content:Init()
  end)
end

local BACKUP_DATA_FILE = "testbackup.txt"
local BACKUP_LIST_KEY = "BackupList"
local BACKUP_NEXT_ID = "BackupNextId"
local BACKUP_DIR = "backup"

function TestModel:InitBackupInfo()
  self.m_backupDict = SimpleStringDict.Create(BACKUP_DATA_FILE)
  local backupList = self.m_backupDict:Get(BACKUP_LIST_KEY)
  if not StringUtil.IsNilOrEmpty(backupList) then
    self.m_backupList = json.decode(backupList)
  else
    self.m_backupList = {}
  end
  self.m_backupNextId = tonumber(self.m_backupDict:Get(BACKUP_NEXT_ID)) or 0
end

function TestModel:GetBackupList()
  return self.m_backupList
end

function TestModel:AddNewBackup(backupName)
  self.m_backupNextId = self.m_backupNextId + 1
  local dirName = self:_GetBackupDirectory(self.m_backupNextId)
  if not Directory.Exists(dirName) then
    Directory.CreateDirectory(dirName)
  end
  local diInfo = DirectoryInfo(FileUtils.WritablePath)
  local fileInfos = diInfo:GetFiles("DBTableManager")
  for i = 0, fileInfos.Length - 1 do
    if not self:IsBackupFileName(fileInfos[i].Name) then
      fileInfos[i]:CopyTo(Path.Combine(dirName, fileInfos[i].Name))
    end
  end
  local info = {
    id = self.m_backupNextId,
    name = backupName,
    version = GameConfig.GetCurrentVersion(),
    server = NetworkConfig.GetSelectedServer(),
    time = GM.GameModel:GetServerTime()
  }
  table.insert(self.m_backupList, info)
  self.m_backupDict:Set(BACKUP_LIST_KEY, json.encode(self.m_backupList))
  self.m_backupDict:Set(BACKUP_NEXT_ID, tostring(self.m_backupNextId))
  self.m_backupDict:Serialize()
  return info
end

function TestModel:DeleteBackup(backupId)
  local success = false
  local removeIndex = 0
  for index, info in ipairs(self.m_backupList) do
    if info.id == backupId then
      removeIndex = index
      success = true
      break
    end
  end
  if success then
    table.remove(self.m_backupList, removeIndex)
    Directory.Delete(self:_GetBackupDirectory(backupId), true)
    self.m_backupDict:Set(BACKUP_LIST_KEY, json.encode(self.m_backupList))
    self.m_backupDict:Serialize()
  end
  return success
end

function TestModel:_GetBackupDirectory(id)
  return Path.Combine(FileUtils.WritablePath, BACKUP_DIR .. "/" .. id)
end

function TestModel:IsBackupFileName(fileName)
  return fileName == BACKUP_DATA_FILE
end

function TestModel:IsBackupDirectoryName(directoryName)
  return directoryName == BACKUP_DIR
end

function TestModel:UseBackup(id)
  if Directory.Exists(self:_GetBackupDirectory(id)) then
    TestMessage.ClearData(function(success, tbResp)
      if success and tbResp.rcode == 0 then
        self:ClearData(false)
        local uuid = GM.UserModel:GetInstallUuid()
        local fileInfos = DirectoryInfo(self:_GetBackupDirectory(id)):GetFiles()
        for i = 0, fileInfos.Length - 1 do
          fileInfos[i]:CopyTo(FileUtils.WritablePath .. fileInfos[i].Name, true)
        end
        local isSuccess
        GM.DBTableManager:CreateFile()
        if fileInfos[0].Name == DatabaseModel.LocalFileName then
          GM.DatabaseModel:Init()
          isSuccess = GM.DBTableManager:_LoadTables(true)
          if isSuccess then
            GM.DBTableManager:TrySaveAll(true)
          end
          GM.DatabaseModel:DeleteDatabase()
        else
          isSuccess = GM.DBTableManager:_LoadTables()
        end
        if isSuccess then
          GM.UserModel:SetInstallUuid(uuid)
          GM.UserModel:SetUserId(0)
          GM.UserModel:Set(EUserLocalDataKey.SSOToken, "")
          GM:RestartGame(nil, EBIProjectType.RestartGameAction.Test)
        else
          GM.UserModel:SetInstallUuid(uuid)
          GM.UIManager:ShowPrompt("该数据文件无效,即将清除本地数据并退出游戏！")
          DelayExecuteFunc(function()
            PlatformInterface.ExitGame()
          end, 1.5)
        end
      else
        GM.UIManager:ShowPrompt("failed.")
      end
    end)
  end
end

function TestModel:RemoveServerBackupData(schema, dataName, callback)
  TestMessage.DeleteBackupData(schema, dataName, function(success, tbResp)
    if callback then
      if success and tbResp.rcode == 0 then
        callback(true)
      else
        callback(false)
      end
    end
  end)
end

function TestModel:SaveBackupDataToServer(schema, dataName, callback)
  TestMessage.UploadBackupData(schema, dataName, function(success, tbResp)
    if callback then
      if success and tbResp.rcode == 0 then
        callback(true)
      else
        callback(false)
      end
    end
  end)
end

function TestModel:UseServerBackupData(schema, dataName, callback)
  TestMessage.UseBackupData(schema, dataName, function(success, tbResp)
    if callback then
      if success and tbResp.rcode == 0 then
        callback(true)
      else
        callback(false)
      end
    end
  end)
end

function TestModel:SetABTestGroupInfo(groupNameInfo)
  if StringUtil.IsNilOrEmpty(groupNameInfo) then
    return
  end
  local arrGroups = json.decode(groupNameInfo)
  if not Table.IsEmpty(arrGroups) then
    local str = ""
    for _, oneGroupData in ipairs(arrGroups) do
      str = str .. oneGroupData.name .. ", "
    end
    self.abTestGroupInfo = str
  end
end

function TestModel:GetTestFileReplaces()
  return self.m_mapTestFileReplaces
end

function TestModel:SaveTestFileReplaces(mapTestFileReplaces)
  self.m_mapTestFileReplaces = mapTestFileReplaces or {}
  self.m_dbTable:Set(ETestDataKey.FileReplace, DB_VALUE_KEY, json.encode(self.m_mapTestFileReplaces))
end

function TestModel:ReleaseChapterTemporarily()
  local maxChapter = #GM.ChapterDataModel.m_arrOriginChapterSequence
  GM.ChapterDataModel:ConsiderTaskChapter(maxChapter)
  GM.ChapterDataModel:ConsiderOrderDay(MainOrderDataModel.GetMaxOrderDayOfChapter(maxChapter))
end

function TestModel:EditorSetPosition(eTimelineAction, strTimelineData, slotId)
  local roomView = GM.ChapterManager:GetActiveRoomView()
  if not roomView then
    return nil
  end
  local stepData = TimelineStep.Create({
    Slot = slotId,
    Type = eTimelineAction,
    Content = strTimelineData
  })
  stepData:Init()
  if eTimelineAction == ETimelineAction.CameraMove then
    roomView:MoveCamera(stepData.pos, stepData.scale, stepData.duration, nil)
  elseif eTimelineAction == ETimelineAction.RoleAction then
    roomView:PlayRoleAnimaion(stepData.animationName, stepData.pos, stepData.bReverse, stepData.waitTime, nil)
  elseif eTimelineAction == ETimelineAction.SlotActions then
    roomView:PlayEffectOnSlot(slotId, stepData.arrSlotActions[1].animationConfig)
  end
end

function TestModel:PutMainRoleToCenter()
  local roomView = GM.ChapterManager:GetActiveRoomView()
  if roomView then
    roomView:PutMainRoleToCenter()
  end
end

function TestModel:ForceRemoveCurrentOrders()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local arrOrderIdsToRemove = {}
  for orderId, order in pairs(orderModel:GetOrders()) do
    arrOrderIdsToRemove[#arrOrderIdsToRemove + 1] = orderId
  end
  for _, orderIdToRemove in ipairs(arrOrderIdsToRemove) do
    orderModel:ForceRemoveOrder(orderIdToRemove)
  end
end

function TestModel:LoadTestOrderGroups()
  local strData = self.m_dbTable:GetValue(ETestDataKey.OrderGroup, DB_VALUE_KEY)
  self.m_arrTestOrderGroups = not StringUtil.IsNull(strData) and json.decode(strData) or {}
  local strDeployedData = self.m_dbTable:GetValue(ETestDataKey.DeployedOrderGroup, DB_VALUE_KEY)
  self.m_arrDeployedTestOrderGroups = not StringUtil.IsNull(strDeployedData) and json.decode(strDeployedData) or {}
end

function TestModel:GetTestOrderGroups()
  return self.m_arrTestOrderGroups
end

function TestModel:SaveOrderGroups()
  self.m_dbTable:Set(ETestDataKey.OrderGroup, DB_VALUE_KEY, json.encode(self.m_arrTestOrderGroups))
end

function TestModel:_SaveDeployedOrderGroups()
  self.m_dbTable:Set(ETestDataKey.DeployedOrderGroup, DB_VALUE_KEY, json.encode(self.m_arrDeployedTestOrderGroups or {}))
end

function TestModel:_SaveRecordOrderGroupId(id)
  self.m_dbTable:Set(ETestDataKey.RecordOrderGroupId, DB_VALUE_KEY, id)
end

function TestModel:_GetRecordOrderGroupId()
  return self.m_dbTable:GetValue(ETestDataKey.RecordOrderGroupId, DB_VALUE_KEY)
end

local TEST_GROUP_ID = 999

function TestModel:DeployOrderGroups(arrOrderGroupData)
  local mainOrderModel = GM.MainBoardModel:GetOrderModel()
  local curOrderGroupId = mainOrderModel:_GetCurOrderGroupId()
  if curOrderGroupId == TEST_GROUP_ID then
    self:UndeployOrderGroups()
  end
  self:ForceRemoveCurrentOrders()
  mainOrderModel:_SetCurOrderGroupId(TEST_GROUP_ID)
  local arrGroupConfigs = {}
  for groupIndex, orderGroupData in ipairs(arrOrderGroupData) do
    local arrOrderConfigs = {}
    for orderIndex, arrRequirementData in ipairs(orderGroupData.arrOrderData) do
      local orderConfig = {
        Id = "test_" .. groupIndex .. "_" .. orderIndex,
        GroupId = TEST_GROUP_ID,
        ChapterId = GM.TaskManager:GetOngoingChapterId()
      }
      if not StringUtil.IsNilOrEmpty(arrRequirementData[1].Type) then
        orderConfig.Requirement_1 = arrRequirementData[1]
      end
      if not StringUtil.IsNilOrEmpty(arrRequirementData[2].Type) then
        if orderConfig.Requirement_1 then
          orderConfig.Requirement_2 = arrRequirementData[2]
        else
          orderConfig.Requirement_1 = arrRequirementData[2]
        end
      end
      if orderConfig.Requirement_1 then
        arrOrderConfigs[orderIndex] = orderConfig
      end
    end
    arrGroupConfigs[groupIndex] = arrOrderConfigs
  end
  self.m_arrDeployedTestOrderGroups = arrGroupConfigs
  self:_SaveDeployedOrderGroups()
  if curOrderGroupId ~= TEST_GROUP_ID then
    self:_SaveRecordOrderGroupId(curOrderGroupId)
  end
  local arrNextConfigs = self:GetNextOrderGroupConfigs()
  local arrNewOrders = MainOrderCreatorFixed.Create(mainOrderModel, arrNextConfigs)
  mainOrderModel:_OnCreatedOrders(arrNewOrders)
  GM.ShopModel:RefreshFlashSaleByDayRefresh()
  return true
end

function TestModel:GetNextOrderGroupConfigs()
  if not self.m_arrDeployedTestOrderGroups or not self.m_arrDeployedTestOrderGroups[1] then
    return nil
  end
  local arrNextConfigs = self.m_arrDeployedTestOrderGroups[1]
  Table.ListRemove(self.m_arrDeployedTestOrderGroups, arrNextConfigs)
  self:_SaveDeployedOrderGroups()
  return arrNextConfigs
end

function TestModel:UndeployOrderGroups()
  local mainOrderModel = GM.MainBoardModel:GetOrderModel()
  local curOrderGroupId = mainOrderModel:_GetCurOrderGroupId()
  if curOrderGroupId ~= TEST_GROUP_ID then
    GM.UIManager:ShowPrompt("当前还未部署！")
    return false
  end
  self.m_arrDeployedTestOrderGroups = nil
  self:_SaveDeployedOrderGroups()
  self:ForceRemoveCurrentOrders()
  mainOrderModel:_SetCurOrderGroupId(tonumber(self:_GetRecordOrderGroupId()))
  mainOrderModel:_TryFillOrders()
  GM.ShopModel:RefreshFlashSaleByDayRefresh()
  return true
end
