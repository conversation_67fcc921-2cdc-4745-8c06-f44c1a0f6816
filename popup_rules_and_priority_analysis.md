# 弹窗规则及弹窗优先级完整分析文档

## 概述

该项目采用了复杂的弹窗管理系统，通过PopupChain（弹窗链）机制来控制各种弹窗的显示顺序和优先级。系统包含多种弹窗类型，每种都有特定的触发条件和优先级规则。

## 核心架构

### 1. 弹窗状态管理

**代码位置**: `UIManager.lua:1-8`
```lua
EViewState = {
  Invalid = -1,     -- 无效状态
  Cached = 0,       -- 缓存状态
  WaitingOpen = 1,  -- 等待打开
  Loading = 2,      -- 加载中
  Opened = 3,       -- 已打开
  Closing = 4       -- 关闭中
}
```

### 2. 弹窗类型定义

**代码位置**: `BaseView.lua:1-6`
```lua
EViewType = {
  SceneView = 1,    -- 场景视图
  Window = 2,       -- 窗口弹窗
  Tutorial = 3,     -- 教程弹窗
  Other = 4         -- 其他类型
}
```

### 3. 特殊弹窗层级

**代码位置**: `UIDefinition.lua:6-21`
```lua
ESpecialViewSortingOrder = {
  MaskLayer = 28000,              -- 遮罩层
  EventLock = 28000,              -- 事件锁定层
  HudHighlight = 25500,           -- HUD高亮
  Prompt = 32000,                 -- 提示信息
  FlyElement = 32000,             -- 飞行元素
  TutorialMask = 26000,           -- 教程遮罩
  TutorialHighlight = 26500,      -- 教程高亮
  Transition = 27000,             -- 过渡效果
  DataConflictWindow = 27500,     -- 数据冲突窗口
  UpdateHintWindow = 27600,       -- 更新提示窗口
  AccountNoticeWindow = 27700,    -- 账户通知窗口
  SystemGeneralWindow = 28400,    -- 系统通用窗口
  ForceRestartWindow = 28500,     -- 强制重启窗口
  TestWindow = 29000              -- 测试窗口
}
```

## PopupChain弹窗链系统

### 1. 弹窗Helper优先级顺序

**代码位置**: `PopupChain.lua:41-68`
```lua
EPopupHelper = {
  DataBalance = 1,                    -- 数据平衡 (最高优先级)
  SkipTimeAutoEffect = 2,             -- 跳过时间自动效果
  ProgressActivityReward = 3,         -- 进度活动奖励
  ScrollBackToOrder = 4,              -- 滚动回订单
  FlambeTime = 5,                     -- Flambe时间
  Level = 6,                          -- 等级提升
  Task = 7,                           -- 任务
  OrderGroup = 8,                     -- 订单组
  OrderDay = 9,                       -- 订单日
  ItemTypeDelete = 10,                -- 物品类型删除
  ItemRecycle = 11,                   -- 物品回收
  Tutorial = 12,                      -- 教程
  Notice = 13,                        -- 通知
  Rate = 14,                          -- 评分
  Bundle = 15,                        -- 礼包
  EnergyBoost = 16,                   -- 体力加成
  Album = 17,                         -- 相册
  BP = 18,                            -- 通行证
  SurpriseChestActivity = 19,         -- 惊喜宝箱活动
  BakeOut = 20,                       -- 烘焙活动
  CoinRace = 21,                      -- 金币竞赛
  PkRace = 22,                        -- PK竞赛
  Dig = 23,                           -- 挖掘活动
  ProgressActivity = 24,              -- 进度活动
  BlindChest = 25,                    -- 盲盒
  ExtraBoard = 26                     -- 额外棋盘 (最低优先级)
}
```

**优先级规则**: 数字越小，优先级越高。系统按照这个顺序依次检查每个Helper是否需要弹窗。

### 2. 弹窗触发前置条件

**代码位置**: `PopupChain.lua:156-159`
```lua
function PopupChain:_StartPopup()
  -- 必须满足所有条件才能触发弹窗
  if not GM.UIManager.allWindowClosed or           -- 所有窗口已关闭
     GM.UIManager:IsEventLock(true) or             -- 没有事件锁定
     GM.UIManager:IsMaskVisible() or               -- 遮罩不可见
     GM.TimelineManager:IsPlayingTimeline() or     -- 没有播放时间线
     GM.TutorialModel:HasAnyStrongTutorialOngoing() or  -- 没有强制教程进行中
     GM.ChapterManager.curActiveChapterName ~= GM.TaskManager:GetOngoingChapterName() or  -- 章节匹配
     GM.SceneManager.isChanging then               -- 场景没有切换中
    return
  end
  -- ... 弹窗检查逻辑
end
```

### 3. 自动运行模式特殊处理

**代码位置**: `PopupChain.lua:161-169`
```lua
if IsAutoRun() then
  arrHelpers = {
    self.m_levelUpHelper,                           -- 等级提升
    self.m_helpers[EPopupHelper.OrderGroup],        -- 订单组
    self.m_helpers[EPopupHelper.FlambeTime],        -- Flambe时间
    self.m_helpers[EPopupHelper.Task],              -- 任务
    self.m_helpers[EPopupHelper.ProgressActivityReward]  -- 进度活动奖励
  }
end
```

**特点**: 自动运行模式下只允许特定的关键弹窗，避免干扰自动化流程。

## 主要弹窗Helper详细分析

### 1. DataBalancePopupHelper (优先级: 1)

**代码位置**: `DataBalancePopupHelper.lua:1-19`
```lua
DataBalancePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true  -- 只在主场景弹出
  },
  canIgnorePopup = false       -- 不能被忽略
}, BasePopupHelper)

function DataBalancePopupHelper:CheckPopup()
  if GM.DataBalanceModel:NeedBalance() then
    return UIPrefabConfigName.DataBalanceWindow
  end
end
```

**特点**: 最高优先级，用于处理数据平衡问题，不能被忽略。

### 2. LevelUpPopupHelper (优先级: 6)

**代码位置**: `LevelUpPopupHelper.lua:1-28`
```lua
LevelUpPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true  -- 主场景和棋盘场景都可弹出
  },
  canIgnorePopup = false        -- 不能被忽略
}, BasePopupHelper)

function LevelUpPopupHelper:_OnLevelUp()
  self.m_canPopLevelUpWindow = true
end
```

**特点**: 等级提升时触发，在多个场景都可弹出，不能被忽略。

### 3. BundlePopupHelper (优先级: 15)

**代码位置**: `BundlePopupHelper.lua:1-79`
```lua
BundlePopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Board] = true,
    [EPopupScene.Main] = true   -- 两个主要场景都可弹出
  }
}, BasePopupHelper)

function BundlePopupHelper:CheckPopup()
  if not Table.IsEmpty(self.m_waitingPopUpWindow) then
    for i, v in ipairs(self.m_waitingPopUpWindow) do
      if v.popScene and v.popScene ~= GM.SceneManager:GetGameMode() then
        -- 场景不匹配，跳过
      else
        local toPopWindow = table.remove(self.m_waitingPopUpWindow, i)
        return toPopWindow.name, toPopWindow.args
      end
    end
  end
end
```

**特点**: 
- 维护弹窗队列 `m_waitingPopUpWindow`
- 支持场景过滤
- 先进先出的队列机制

### 4. TaskPopupHelper (优先级: 7)

**代码位置**: `TaskPopupHelper.lua:40-60`
```lua
function TaskPopupHelper:CheckPopup()
  if self.m_canClaimProgressReward then
    self.m_canClaimProgressReward = false
    return UIPrefabConfigName.TaskGroupFinishWindow
  end
  if self.m_canPopChapterFinishWindow then
    self.m_canPopChapterFinishWindow = false
    GM.TaskManager:SetPopedChapterFinishWin()
    if GM.TaskManager:IsLastChapter() then
      self:SetNeedCheckPopup(true)
      self.m_nextWin = UIPrefabConfigName.TaskClearWindow
      self.m_canPopDayClearWindow = false
    end
    return UIPrefabConfigName.ChapterFinishWindow
  end
  -- ... 更多检查逻辑
end
```

**特点**: 
- 多种任务相关弹窗
- 链式弹窗机制
- 状态管理复杂

## 弹窗场景控制

### 1. 场景定义

**代码位置**: `PopupChain.lua:1-5`
```lua
EPopupScene = {
  Main = EGameMode.Main,    -- 主场景
  Board = EGameMode.Board,  -- 棋盘场景
  Spree = "spree"          -- 狂欢场景
}
```

### 2. 场景过滤机制

每个Helper都可以定义 `canPopScene` 来控制在哪些场景下可以弹出：

```lua
-- 示例：只在主场景弹出
canPopScene = {
  [EPopupScene.Main] = true
}

-- 示例：在多个场景都可弹出
canPopScene = {
  [EPopupScene.Main] = true,
  [EPopupScene.Board] = true
}
```

## 弹窗层级管理

### 1. SortingOrder计算

**代码位置**: `UIManager.lua:305-317`
```lua
function UIManager:_SetSortingOrder(view)
  if view.sortingOrder then
    view:SetSortingOrder(view.sortingOrder)  -- 使用预设层级
  else
    local curOrder = self:_GetTopViewSortingOrder() + self:GetDeltaSortingOrder()
    view:SetSortingOrder(curOrder)  -- 动态计算层级
  end
end

function UIManager:GetDeltaSortingOrder()
  return 10  -- 每个新弹窗增加10层级
end
```

### 2. 特殊弹窗层级

某些系统级弹窗有固定的高层级：
- **TestWindow**: 29000 (最高)
- **ForceRestartWindow**: 28500
- **SystemGeneralWindow**: 28400
- **Prompt**: 32000 (提示信息最高)

## 事件锁定机制

### 1. 事件锁定类型

**代码位置**: `UIManager.lua:538-551`
```lua
function UIManager:IsEventLock(ignorePopupLock)
  if self.m_lockCountWithoutTarget > 0 then
    return true  -- 全局锁定
  end
  if self.m_bEventLockUntilNextPopup and not ignorePopupLock then
    return true  -- 锁定直到下次弹窗
  end
  for _, count in pairs(self.m_lockTargetCountMap) do
    if 0 < count then
      return true  -- 目标锁定
    end
  end
  return false
end
```

### 2. 锁定机制作用

- **防止重复弹窗**: 在动画播放期间锁定
- **保护用户体验**: 避免弹窗干扰关键操作
- **流程控制**: 确保弹窗按正确顺序显示

## 弹窗队列管理

### 1. OpenViewWhenIdle机制

**代码位置**: `UIManager.lua:216-223`
```lua
function UIManager:OpenViewWhenIdle(viewName, ...)
  Log.Info("UIManager OpenView " .. tostring(viewName))
  table.insert(self.m_mapViewDataList[EViewState.WaitingOpen], {
    viewName = viewName,
    args = table.pack(...)
  })
  self:_UpdateViewData()
end
```

**特点**:
- 将弹窗加入等待队列
- 在合适时机自动弹出
- 避免同时弹出多个窗口

### 2. 弹窗前置检查

**代码位置**: `UIManager.lua:278-286`
```lua
function UIManager:_OnViewOpen(sourceState, view, ...)
  if not view:BeforeOpenCheck(...) then
    self:_ShiftViewData(view.name, sourceState, EViewState.Cached, view)
    if GameConfig.IsTestMode() then
      self:ShowPrompt("弹窗开启检查失败:" .. tostring(view.name))
      Log.Info("弹窗开启检查失败:" .. tostring(view.name))
    end
    return
  end
  -- ... 继续打开弹窗
end
```

**检查机制**:
- 每个弹窗都可以实现 `BeforeOpenCheck` 方法
- 动态判断是否满足弹出条件
- 失败时自动缓存，不强制弹出

## 特殊弹窗规则

### 1. Bundle礼包弹窗

**代码位置**: `BundlePopupHelper.lua:27-44`
```lua
function BundlePopupHelper:_AddWindowToPopupChain(msg)
  if not Table.IsEmpty(msg) then
    if msg.notRepeat then
      if not GM.UIManager:IsViewExisting(msg.name) then
        local isRepeatWindow = false
        for k, v in ipairs(self.m_waitingPopUpWindow) do
          if v.name == msg.name then
            self.m_waitingPopUpWindow[k] = msg  -- 更新现有窗口
            isRepeatWindow = true
            break
          end
        end
        if not isRepeatWindow then
          table.insert(self.m_waitingPopUpWindow, msg)  -- 添加新窗口
        end
      end
    else
      table.insert(self.m_waitingPopUpWindow, msg)  -- 直接添加
    end
  end
end
```

**特殊规则**:
- **notRepeat机制**: 防止重复添加相同弹窗
- **队列更新**: 相同弹窗会更新参数而不是重复添加
- **存在性检查**: 已显示的弹窗不会重复加入队列

### 2. Tutorial教程弹窗

**代码位置**: `TutorialPopupHelper.lua` (推断)
```lua
TutorialPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  },
  canIgnorePopup = false  -- 教程不能被忽略
}, BasePopupHelper)
```

**特点**:
- 最高优先级之一
- 不能被测试模式忽略
- 阻塞其他弹窗直到完成

### 3. Notice通知弹窗

**代码位置**: `NoticePopupHelper.lua` (推断)
```lua
function NoticePopupHelper:CheckPopup()
  local notices = GM.NoticeModel:GetUnreadNotices()
  if not Table.IsEmpty(notices) then
    return UIPrefabConfigName.NoticeWindow, {notices}
  end
end
```

**特点**:
- 系统通知和公告
- 可能包含多条通知
- 按重要性排序

## 弹窗动画和效果

### 1. 窗口遮罩管理

**代码位置**: `UIManager.lua:591-610`
```lua
function UIManager:_UpdateWindowMask(changeScene)
  local topWindow = self:GetOpenedTopViewByType(EViewType.Window)
  local hasMask, maskAlpha = self:_ShouldShowWindowMask()
  if hasMask then
    if not self.m_bWindowMaskOn then
      self.m_windowMaskCanvas.gameObject:SetActive(true)
      self.m_bWindowMaskOn = true
      if self.m_windowMaskTween ~= nil then
        self.m_windowMaskTween:Kill()
      end
      self.m_windowMaskTween = self.m_windowMaskImg:DOFade(maskAlpha, 0.16666666666666666)
      self.m_windowMaskTween:OnComplete(function()
        self.m_windowMaskTween = nil
      end)
    end
  end
end
```

**遮罩特点**:
- 动态透明度调整
- 多窗口时加深遮罩
- 平滑的淡入淡出动画

### 2. 弹窗动画类型

**代码位置**: `BaseView.lua:7-8`
```lua
EViewCloseAnimType = {
  None = 1,      -- 无动画
  Animator = 2   -- 使用Animator动画
}
```

**代码位置**: `BaseWindow.lua:29-35`
```lua
function BaseWindow:ShowWindowOnLoadFinished()
  if self.m_winAnimator then
    self.m_winAnimator:SetTrigger("Show")  -- 播放显示动画
  end
  if self.m_strOpenWindowEffectName and self.m_strOpenWindowEffectName ~= "" then
    GM.AudioModel:PlayEffect(self.m_strOpenWindowEffectName)  -- 播放音效
  end
end
```

## 提示信息系统

### 1. Prompt提示优先级

**代码位置**: `PromptManager.lua:28-42`
```lua
function PromptManager:_Show(text, uiWorldPos, stayDuration, bTMP, forTest, sortingOrder, specialPrefab)
  local prefabName
  if specialPrefab ~= nil then
    prefabName = specialPrefab
  else
    local bInWindow = GM.UIManager:GetOpenedViewCountByType(EViewType.Window) > 0 and
                     GM.ModeViewController:GetExtraBoardActivityBoardView() == nil
    prefabName = bInWindow and UIPrefabConfigName.PromptOnWindow or UIPrefabConfigName.Prompt
  end
end
```

**智能选择**:
- 根据当前是否有窗口打开选择不同的提示样式
- 窗口内提示和全屏提示使用不同预制体
- 确保提示信息始终可见

### 2. 提示层级管理

**代码位置**: `PromptManager.lua:45-55`
```lua
function PromptManager:_ShowPrompt(go, id, text, uiWorldPos, stayDuration, bTMP, forTest, sortingOrder)
  local prompt = go:GetLuaTable()
  self.m_curOrder = self.m_curOrder + 1
  local targetOrder = (sortingOrder or ESpecialViewSortingOrder.Prompt) + self.m_curOrder
  prompt:SetSortingOrder(targetOrder)
end
```

**层级特点**:
- 基础层级32000（ESpecialViewSortingOrder.Prompt）
- 每个新提示递增层级
- 确保最新提示在最上层

## 测试和调试功能

### 1. 测试模式弹窗控制

**代码位置**: `PopupChain.lua:171`
```lua
(not (GameConfig.IsTestMode() and helper.canIgnorePopup) or
 PlayerPrefs.GetInt(EPlayerPrefKey.TestIgnorePopupChain, 0) ~= 1)
```

**测试功能**:
- 测试模式下可以忽略某些弹窗
- 通过PlayerPrefs控制是否忽略弹窗链
- 便于自动化测试和调试

### 2. 弹窗调试信息

**代码位置**: `UIManager.lua:282-284`
```lua
if GameConfig.IsTestMode() then
  self:ShowPrompt("弹窗开启检查失败:" .. tostring(view.name))
  Log.Info("弹窗开启检查失败:" .. tostring(view.name))
end
```

**调试特点**:
- 测试模式下显示详细的失败信息
- 日志记录弹窗状态变化
- 便于开发者定位问题

## 性能优化机制

### 1. 弹窗缓存机制

**代码位置**: `UIManager.lua:231-235`
```lua
if self:_GetView(viewName, EViewState.Cached) ~= nil then
  self:_ReOpenView(viewName, ...)  -- 重用缓存的弹窗
else
  self:_LoadView(viewName, ...)    -- 加载新弹窗
end
```

**优化特点**:
- 常用弹窗保持在缓存中
- 避免重复加载资源
- 提升弹窗响应速度

### 2. 资源管理

**代码位置**: `UIManager.lua:95-98`
```lua
if self.shouldClearResources then
  ResourceLoader.ClearUnusedAssets()
  self.shouldClearResources = false
end
```

**资源优化**:
- 定期清理未使用的资源
- 防止内存泄漏
- 保持游戏性能稳定

## 总结

### 弹窗优先级总结

1. **系统级弹窗** (最高优先级)
   - DataBalance: 数据平衡
   - ForceRestart: 强制重启
   - Update: 更新提示

2. **核心游戏弹窗** (高优先级)
   - Tutorial: 教程
   - LevelUp: 等级提升
   - Task: 任务完成

3. **活动和奖励弹窗** (中优先级)
   - Bundle: 礼包
   - ProgressActivity: 进度活动
   - Notice: 通知公告

4. **辅助功能弹窗** (低优先级)
   - Rate: 评分
   - Album: 相册
   - ExtraBoard: 额外棋盘

### 设计特点

1. **优先级明确**: 通过数字序号严格控制弹窗顺序
2. **场景感知**: 不同场景下显示不同弹窗
3. **用户体验**: 多重检查机制避免打扰用户
4. **性能优化**: 缓存和资源管理确保流畅体验
5. **调试友好**: 完善的测试和调试功能

该弹窗系统通过精心设计的优先级和规则，在保证重要信息及时传达的同时，最大程度地减少了对用户游戏体验的干扰。
