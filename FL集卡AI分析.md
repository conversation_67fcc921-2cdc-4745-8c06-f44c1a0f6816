# Album Activity 卡册活动详细分析文档

## 概述

Album Activity（卡册活动）是游戏中的一个重要收集类活动，玩家通过开启卡包来收集不同稀有度的卡片，完成卡片套装以获得奖励。该活动包含完整的卡包系统、权重计算、小丑卡兑换、卡片回收等核心功能。

## 核心架构

### 1. 活动定义系统

活动定义系统采用了工厂模式的设计思路，通过 `CreateActivityDefinition` 函数来创建不同类型的卡册活动。这个函数接收三个参数：活动类型、前缀和覆盖表。

**设计思路**：
- 使用基础表（BaseTable）定义所有活动的通用配置
- 通过前缀动态生成各种UI窗口和资源的配置名称
- 支持通过覆盖表来自定义特定活动的配置
- 使用元表机制实现配置的继承和扩展

```lua
-- 活动定义工厂函数
local CreateActivityDefinition = function(activityType, prefix, overrideTable)
  local BaseTable = {
    ActivityDataTableName = VirtualDBTableName[prefix],
    EntryButtonKey = ESceneViewHudButtonKey[prefix],
    StateChangedEvent = EEventType[prefix .. "StateChanged"],
    StartWindowPrefabName = UIPrefabConfigName[prefix .. "StartWindow"],
    EndWindowPrefabName = UIPrefabConfigName[prefix .. "EndWindow"],
    MainWindowPrefabName = UIPrefabConfigName[prefix .. "MainWindow"],
    HelpWindowPrefabName = UIPrefabConfigName[prefix .. "HelpWindow"],
    PackWindowPrefabName = UIPrefabConfigName[prefix .. "PackWindow"],
    TakeSetRewardPrefabName = UIPrefabConfigName[prefix .. "TakeSetRewardWindow"],
    TakeFinishRewardPrefabName = UIPrefabConfigName[prefix .. "TakeFinishRewardWindow"],
    EntryPrefabName = UIPrefabConfigName[prefix .. "Entry"],
    CollectEntryPrefabName = UIPrefabConfigName[prefix .. "CollectEntry"],
    TutorialStartCondition = ETutorialStartCondition.AlbumActivityStart,
    ResourceLabels = {
      AddressableLabel.AlbumCommon,
      AddressableLabel[prefix]
    }
  }
  BaseTable.__index = BaseTable
  AlbumActivityDefinition[activityType] = setmetatable(overrideTable, BaseTable)
  AlbumActivityDefinition[activityType].__index = AlbumActivityDefinition[activityType]
end
```

**核心内容**：
- `ActivityDataTableName`: 数据库表名，用于存储活动数据
- `EntryButtonKey`: HUD按钮的键值，用于在主界面显示活动入口
- `StateChangedEvent`: 状态变化事件，用于通知其他模块活动状态改变
- 各种窗口预制体名称：开始窗口、结束窗口、主窗口、帮助窗口等
- `ResourceLabels`: 资源标签数组，用于资源管理和预加载

### 2. 卡片配置系统

#### 2.1 卡片基础配置

卡片配置采用了表格化的数据结构，每张卡片都有唯一的标识符和属性。配置文件 `AlbumCardConfig.lua` 返回一个包含所有卡片信息的数组。

```lua
-- 卡片配置示例
return {
  {
    cardId = "a1_set1_1",
    order = 1,
    star = 1,
    weight = 400
  },
  {
    cardId = "a1_set1_2",
    order = 2,
    star = 1,
    weight = 400
  },
  {
    cardId = "a1_set3_5",
    order = 5,
    star = 1,
    weight = 100  -- 较低权重
  },
  {
    cardId = "a1_set7_9",
    order = 9,
    star = 5,
    weight = 100  -- 5星卡片，极低权重
  }
}
```

**配置字段详解**：
- `cardId`: 卡片的唯一标识符，通常格式为 "a1_set1_1"，表示第1个活动、第1个套装、第1张卡片
- `order`: 卡片在套装中的排序号，用于UI显示时的排列顺序
- `star`: 卡片的星级，范围从1到5，决定了卡片的稀有度
- `weight`: 抽取权重，数值越高被抽到的概率越大
- `gold`: 可选字段，标记是否为金卡（特殊稀有卡片）

**权重分布规律**：
从配置数据可以看出，权重分布遵循以下规律：
- 1星卡片权重通常为400（高概率）
- 2星卡片权重为200-400（中等概率）
- 3星卡片权重为100-200（较低概率）
- 4星卡片权重为50-200（低概率）
- 5星卡片权重为25-100（极低概率）

#### 2.2 星级系统

星级系统设计了普通卡和金卡两套并行的星级体系。

```lua
-- 星级系统定义
AlbumGoldCardStarBase = 100
AlbumStarType = {
  star1 = 1,
  star2 = 2,
  star3 = 3,
  star4 = 4,
  star5 = 5,
  gstar1 = AlbumGoldCardStarBase + 1,  -- 101
  gstar2 = AlbumGoldCardStarBase + 2,  -- 102
  gstar3 = AlbumGoldCardStarBase + 3,  -- 103
  gstar4 = AlbumGoldCardStarBase + 4,  -- 104
  gstar5 = AlbumGoldCardStarBase + 5   -- 105
}
AlbumStarTypePrefix = {Normal = "star", Gold = "gstar"}
```

**设计思路**：
- 普通卡使用1-5的星级值
- 金卡在普通星级基础上加100，形成101-105的星级值
- 通过 `AlbumGoldCardStarBase` 常量（值为100）来区分普通卡和金卡
- 使用字符串前缀来标识星级类型：`star` 表示普通卡，`gstar` 表示金卡

**实现机制**：
系统通过模运算来获取显示星级，通过整除运算来判断是否为金卡，这样既保持了星级的直观性，又能够区分卡片类型。

## 卡包获取途径

### 1. 游戏内奖励系统

#### 1.1 订单奖励机制

订单奖励是卡包的主要获取途径之一。系统通过配置文件定义了不同章节、不同天数的订单奖励内容。

```lua
-- 订单奖励配置示例
return {
  {
    GroupId = 1,
    ChapterId = 4,
    Day = 34,
    Flambe = "link",
    Rewards = {
      {Currency = "pd_6_3", Amount = 1},
      {Currency = "pd_2_3", Amount = 1},
      {Currency = "eq_5_3", Amount = 2},
      {Currency = "eq_2_2", Amount = 1},
      {Currency = "pack_1_4", Amount = 1}  -- 卡包奖励
    }
  },
  {
    GroupId = 2,
    ChapterId = 4,
    Day = 35,
    Rewards = {
      {Currency = "eq_5_3", Amount = 1},
      {Currency = "pd_6_1", Amount = 4},
      {Currency = "greenbox_1", Amount = 1},
      {Currency = "pack_1_5", Amount = 1}  -- 更高级的卡包
    }
  }
}
```

**设计思路**：
- 每个订单组（OrderGroup）对应特定的章节和天数
- 奖励数组包含多种类型的物品，卡包只是其中一种
- 通过 `Currency` 字段指定奖励类型，`Amount` 字段指定数量
- 支持特殊标记如 `Flambe` 来标识特殊订单类型

**卡包类型分类**：
- `pack_1_1` 到 `pack_1_5`: 普通卡包，数字越大通常包含更高星级的卡片
- `gpack_1_3` 到 `gpack_1_5`: 金卡包，专门用于获取金卡

#### 1.2 其他获取途径

除了订单奖励，卡包还可以通过以下方式获得：
- 活动任务完成奖励
- 商店购买
- 特殊事件奖励
- 登录奖励

### 2. 卡包缓存系统

卡包缓存系统解决了玩家获得卡包但暂时不想开启的需求。

```lua
-- 卡包缓存获取函数
function AlbumActivityModel:AcquireActivityCardPack(Property)
  if not self:IsActivityOpen() then
    return
  end
  -- 初始化缓存数据
  if self.m_cacheProperty[Property[PROPERTY_TYPE]] == nil then
    self.m_cacheProperty[Property[PROPERTY_TYPE]] = 0
  end
  -- 累加卡包数量
  self.m_cacheProperty[Property[PROPERTY_TYPE]] = self.m_cacheProperty[Property[PROPERTY_TYPE]] + Property[PROPERTY_COUNT]
  -- 保存到本地数据库
  self:SaveCacheProperty()
  -- 触发缓存物品事件
  EventDispatcher.DispatchEvent(EEventType.CacheActivityItems, {
    eGameMode = EGameMode.Board,
    hasAnimation = true
  })
end
```

**实现机制**：
- 使用 `m_cacheProperty` 字典存储各种类型卡包的数量
- 当玩家获得卡包时，先存入缓存而不是立即开启
- 支持批量开启和单个开启两种模式
- 通过 JSON 序列化将缓存数据持久化到本地数据库

**设计优势**：
- 允许玩家选择开启卡包的时机
- 支持批量操作，提高用户体验
- 数据持久化确保玩家不会丢失已获得的卡包

## 权重计算系统

### 1. 卡包开启流程

卡包开启是一个复杂的多步骤过程，涉及星级分配、权重计算、保底机制等多个环节。

```lua
-- 卡包开启主函数
function AlbumActivityModel:OpenPackOneConfig(PackType, TestLog)
  local PackConfig = self:GetCardPackConfig(PackType)
  local listTarget = self:FilterCardsByStar(PackConfig, TestLog)
  local GachCardFinishSet = {}
  local bAllAlbum = false
  local GachaCard = {}
  local AllGachaCard = {}
  local openGoldCard = {}
  local mapNewCard = {}

  for _, strStar in ipairs(listTarget) do
    local i32Star = self:GetStarNumber(strStar)
    local CanGachaCards = self:GetGachaCardsByStar(i32Star) or {}
    local ScreenGachaCards, testState = self:CheckPityGachaCard(i32Star, CanGachaCards)
    local ScreenTempCards = self:RemoveDuplicatesCards(AllGachaCard, ScreenGachaCards)

    if #ScreenTempCards == 0 then
      testState = testState * 10
      ScreenTempCards = self:RemoveDuplicatesCards(AllGachaCard, CanGachaCards)
    end

    ScreenGachaCards = ScreenTempCards
    if #ScreenGachaCards == 0 then
      Log.Assert(false, "当前星级没有卡片" .. tostring(i32Star))
    else
      local selectRandom = {}
      for _, eleCardType in ipairs(ScreenGachaCards) do
        local cardConfig = self:GetCardInfo(eleCardType)
        table.insert(selectRandom, cardConfig)
      end
      -- 权重选择核心逻辑
      local strRandCard = Table.ListWeightSelectOne(selectRandom, "weight")
      local bNewCard = false
      if self:GetCardCount(strRandCard.cardId) == 0 then
        mapNewCard[strRandCard.cardId] = true
        bNewCard = true
      end
      local finishSet, curbAllAlbum = self:AcquireCard(strRandCard.cardId, EBIType.AlbumAcquirePackCard)
      table.insert(GachCardFinishSet, finishSet)
      bAllAlbum = bAllAlbum or curbAllAlbum
      table.insert(GachaCard, strRandCard.cardId)
      table.insert(AllGachaCard, strRandCard.cardId)
      if self:IsGoldCard(strRandCard.cardId) then
        table.insert(openGoldCard, strRandCard.cardId)
      end
    end
  end

  -- 按照新卡优先、星级高优先的顺序排序
  table.sort(GachaCard, function(cardId1, cardId2)
    return self:CompareCard(cardId1, cardId2, mapNewCard)
  end)

  GM.BIManager:LogUseItem(PackType, 1, EBIType.AlbumOpenPack, self:GetType())
  return GachaCard, mapNewCard, GachCardFinishSet, bAllAlbum
end
```

**主要流程**：
1. 根据卡包配置确定要产出的卡片数量和星级分布
2. 对每个星级位置，筛选可用的卡片池
3. 应用保底机制，优先选择未收集的卡片
4. 使用权重算法从候选卡片中随机选择
5. 处理重复卡片和新卡片的不同逻辑
6. 更新玩家的收集进度和统计数据

**核心变量说明**：
- `GachaCard`: 本次开包获得的卡片列表
- `AllGachaCard`: 用于去重的所有卡片记录
- `mapNewCard`: 标记哪些是新获得的卡片
- `GachCardFinishSet`: 记录完成的套装信息

### 2. 星级过滤机制

星级过滤是决定卡包内容的关键步骤，它确定了每个位置应该产出什么星级的卡片。

```lua
-- 星级过滤函数
function AlbumActivityModel:FilterCardsByStar(PackConfig, TestLog)
  local FixedSpreadStar = PackConfig.fixedSpread or {}
  local RandomSpreadStar = PackConfig.spreadWeight or {}
  local listTarget = {}

  -- 固定星级分配
  for _, v in pairs(FixedSpreadStar) do
    local strStar = v[PROPERTY_TYPE]
    local eleCount = v[PROPERTY_COUNT]
    for i = 1, eleCount do
      table.insert(listTarget, strStar)
    end
  end

  self:AddTestLog(TestLog, "FixedStarNum", FixedSpreadStar)
  local remainRandomNum = PackConfig.spreadNum - #listTarget
  if remainRandomNum <= 0 then
    return listTarget
  end

  -- 计算随机星级总权重
  local allWeight = 0
  for _, ele in pairs(RandomSpreadStar) do
    allWeight = allWeight + ele.Weight
  end

  -- 随机分配函数
  local AddRandomFunc = function(num, SpreadConfig)
    for i = 1, num do
      local randNum = math.random() * allWeight
      local curWeight = 0
      for _, ele in ipairs(SpreadConfig) do
        curWeight = curWeight + ele.Weight
        if randNum < curWeight then
          table.insert(listTarget, ele.Item)
          break
        end
      end
    end
  end

  AddRandomFunc(remainRandomNum, RandomSpreadStar)
  self:AddTestLog(TestLog, "resultNature", Table.ShallowCopy(listTarget))
  return listTarget
end
```

**固定分配机制**：
- 通过 `FixedSpreadStar` 配置确保某些星级的卡片必定出现
- 例如：每包必定包含1张3星卡片，2张2星卡片
- 这种机制保证了卡包的基础价值

**随机分配机制**：
- 剩余位置通过 `RandomSpreadStar` 配置进行随机分配
- 计算所有随机星级的总权重
- 使用加权随机算法为每个剩余位置分配星级
- 支持动态调整不同星级的出现概率

**权重计算过程**：
1. 遍历随机星级配置，累加总权重
2. 为每个剩余位置生成随机数
3. 根据权重区间确定该位置的星级
4. 重复此过程直到所有位置都分配完毕

### 3. 保底机制

保底机制是为了防止玩家运气过差，长期无法获得新卡片而设计的。

```lua
-- 保底检查函数
function AlbumActivityModel:CheckPityGachaCard(i32Star, CanGachaCards)
  local ScreenGachaCards = {}
  local testState = 0

  if self:CanPositiveBuff(i32Star) then
    -- 保底触发：优先选择未收集的卡片
    for _, card in ipairs(CanGachaCards) do
      if self:GetCardCount(card) == 0 then
        table.insert(ScreenGachaCards, card)
      end
    end
    testState = 2
  else
    -- 正常抽取
    ScreenGachaCards = CanGachaCards
    testState = 1
  end

  return ScreenGachaCards, testState
end

-- 保底触发条件判断
function AlbumActivityModel:CanPositiveBuff(star)
  local insures = self.m_albumConfig.insure
  for _, eleInsure in pairs(insures) do
    local curStar = self:GetStarNumber(eleInsure[PROPERTY_TYPE])
    if curStar == star then
      local limitNumber = eleInsure[PROPERTY_COUNT]
      if self:GetNotNewCardStarCount(star) >= limitNumber - 1 then
        return true
      end
    end
  end
end
```

**触发条件判断**：
- 系统维护每个星级的"未获得新卡次数"计数器
- 当某个星级的计数器达到配置的阈值时，触发保底
- 保底阈值通过 `insure` 配置项设定，不同星级可以有不同的阈值

**保底执行逻辑**：
- 正常情况下，从该星级的所有卡片中随机选择
- 保底触发时，只从该星级的未收集卡片中选择
- 如果未收集卡片池为空，则回退到正常逻辑
- 保底状态会影响测试日志的记录，便于调试和分析

**平衡性考虑**：
- 保底机制确保了玩家的收集体验不会过于糟糕
- 但不会完全消除随机性，保持了抽卡的刺激感
- 不同星级的保底阈值可以独立调整，实现精细化控制

### 4. 权重选择算法

权重选择算法是整个抽卡系统的核心，它决定了在给定权重分布下如何公平地进行随机选择。

```lua
-- 权重选择核心算法
function Table.ListWeightSelectOne(source, weightKey)
  weightKey = weightKey or "Weight"
  local sumWeight = 0
  -- 计算总权重
  for _, item in ipairs(source) do
    sumWeight = sumWeight + item[weightKey]
  end

  -- 生成随机数
  local randomIndex = math.random(math.floor(sumWeight))
  -- 遍历查找对应项
  for index, item in ipairs(source) do
    if randomIndex <= item[weightKey] then
      return item, index
    else
      randomIndex = randomIndex - item[weightKey]
    end
  end

  Log.Assert(false, "Table.ListWeightSelectOne")
  return nil
end
```

**算法原理**：
1. 计算所有候选项的权重总和
2. 生成一个0到总权重之间的随机数
3. 遍历候选项，累加权重直到超过随机数
4. 返回当前遍历到的候选项

**实现细节**：
- 使用 `math.random()` 生成随机数，确保分布的均匀性
- 支持自定义权重字段名，默认为 "Weight"
- 包含断言检查，确保算法的正确性
- 返回选中的项目和其在列表中的索引

**算法优势**：
- 时间复杂度为O(n)，效率较高
- 严格按照权重比例进行选择，保证公平性
- 代码简洁易懂，便于维护和调试

## 小丑卡系统

### 1. 小丑卡获取机制

小丑卡是一种特殊的道具，允许玩家直接兑换指定的卡片，为收集提供了确定性的途径。

```lua
-- 小丑卡获取函数
function AlbumActivityModel:AddJokerCard(property, scene)
  local arrCards = self:GetJokerCards()
  local time = GM.GameModel:GetServerTime()
  for i = 1, property[PROPERTY_COUNT] do
    arrCards[#arrCards + 1] = {
      time = time,
      type = property[PROPERTY_TYPE]  -- EPropertyType.JokerCommonCard 或 EPropertyType.JokerGoldenCard
    }
  end
  -- 按时间排序
  table.sort(arrCards, function(a, b)
    return a.time < b.time
  end)
  self.m_dbTable:Set(JokerCardKey, DBColumnValue, json.encode(arrCards))
  self:GetEvent():Call(AlbumActivityModel.EventKey.JokerCountRefresh)
  self.m_acquiredJokerCard = property[PROPERTY_TYPE]
  EventDispatcher.DispatchEvent(EEventType.AlbumPopStateChanged)
end
```

**存储结构设计**：
- 每张小丑卡记录获得时间和类型
- 支持普通小丑卡和金小丑卡两种类型
- 使用数组存储，按获得时间排序
- 通过JSON序列化持久化到本地数据库

**获取时机**：
- 特殊活动奖励
- 商店购买
- 成就解锁
- 补偿发放

**类型区分**：
- `EPropertyType.JokerCommonCard`: 普通小丑卡，可兑换普通卡片
- `EPropertyType.JokerGoldenCard`: 金小丑卡，可兑换金卡片

### 2. 小丑卡兑换机制

兑换机制确保了小丑卡的正确使用，防止玩家误操作或系统漏洞。

```lua
-- 小丑卡兑换函数
function AlbumActivityModel:JokerExchange(cardId)
  local isGoldCard = self:IsGoldCard(cardId)
  local isGoldJoker = self:IsGoldenJokerCardInUse()

  -- 验证兑换规则：金卡只能用金小丑卡兑换
  if isGoldCard and not isGoldJoker then
    Log.Assert(false, "金卡不能兑换小丑卡")
    GM.UIManager:ShowPrompt("exchange error try again")
    return false
  end

  local arrJokerCards = self:GetJokerCards()
  if Table.IsEmpty(arrJokerCards) then
    Log.Assert(false, "没有小丑卡")
    return
  end

  -- 消耗小丑卡
  if AlbumActivityModel.UseNewJokerCard then
    table.remove(arrJokerCards, #arrJokerCards)  -- 使用最新的
  else
    table.remove(arrJokerCards, 1)  -- 使用最旧的
  end

  self.m_dbTable:Set(JokerCardKey, DBColumnValue, json.encode(arrJokerCards))
  self:AcquireCard(cardId, EBIType.AlbumAcquireJokerCard)
  self:SetNewCardId(cardId)
  EventDispatcher.DispatchEvent(EEventType.ActiveQuestProgressGain, {type = "card", num = 1})
  self:GetEvent():Call(AlbumActivityModel.EventKey.GetNewCard)

  local jokerId = EPropertyType.JokerCommonCard
  if isGoldJoker then
    jokerId = EPropertyType.JokerGoldenCard
  end
  self:ClearNewJokerState()
  GM.BIManager:LogUseItem(jokerId, 1, EBIType.AlbumUseJoker, self:GetType())
  self:GetEvent():Call(AlbumActivityModel.EventKey.JokerCountRefresh)
end
```

**兑换规则验证**：
- 金卡只能使用金小丑卡兑换，普通卡可以使用任意类型小丑卡
- 检查玩家是否拥有足够的小丑卡
- 验证目标卡片是否存在且可兑换

**消耗策略**：
- 支持两种消耗模式：FIFO（先进先出）和LIFO（后进先出）
- 通过 `UseNewJokerCard` 标志控制使用哪种模式
- FIFO模式防止小丑卡过期，LIFO模式让玩家优先使用新获得的小丑卡

**兑换后处理**：
- 直接调用卡片获得逻辑，触发相关的进度更新
- 设置新卡片标记，用于UI显示特效
- 触发相关事件，通知其他系统更新状态
- 记录行为日志，用于数据分析

### 3. 小丑卡过期机制

过期机制防止玩家无限囤积小丑卡，鼓励及时使用。

```lua
-- 小丑卡过期检查
function AlbumActivityModel:HasExpiredJokerCard()
  local arrJokerCards = self:GetJokerCards()
  if #arrJokerCards == 0 then
    return false
  end
  local time = arrJokerCards[1].time
  if time + Sec2Day < GM.GameModel:GetServerTime() then  -- 24小时过期
    return true
  end
  return false
end
```

**过期规则**：
- 小丑卡获得后24小时（Sec2Day）内有效
- 过期检查基于最早获得的小丑卡时间
- 过期的小丑卡不会自动删除，但会在UI中提示

**过期处理**：
- UI显示过期提示信息
- 弹出提醒窗口，建议玩家尽快使用
- 限制某些操作，如关闭兑换窗口
- 记录过期事件，用于数据分析

## 卡片回收系统

### 1. 回收机制设计

回收系统为玩家提供了处理重复卡片的途径，将多余的卡片转换为有价值的奖励。

```lua
-- 卡片回收函数
function AlbumActivityModel:BuyRecyleCard(index, selectCards, star)
  -- 验证卡片是否可回收
  local bWillRemove = {}
  for _, cardId in ipairs(selectCards) do
    bWillRemove[cardId] = true
  end

  -- 标记已回收的卡片
  for _, cardId in ipairs(selectCards) do
    self.m_alReadyrecyleCards[cardId] = self.m_alReadyrecyleCards[cardId] or 0
    self.m_alReadyrecyleCards[cardId] = self.m_alReadyrecyleCards[cardId] + 1
  end

  GM.BIManager:LogUseItem(AlbumStarBIName, star, EBIType.AlbumExchangeStar, index)
  self:UpdateSurplusCardData()

  -- 设置冷却时间
  self.m_recyleCardData[tostring(index)] = GM.GameModel:GetServerTime() + self.m_recyleConfigs[index].cdTime

  -- 给予奖励
  local rewards = self.m_recyleConfigs[index].reward
  RewardApi.CryptRewards(rewards, true)
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.AlbumStarRewards, EGameMode.Board, CacheItemType.Stack)

  self:SaveCycleCardData()
  return true
end
```

**回收原理**：
- 玩家可以将多张重复卡片按照一定比例兑换奖励
- 不同星级的卡片有不同的回收价值
- 回收后的卡片不会消失，而是标记为"已回收"状态
- 支持多种回收配置，提供不同的兑换选项

**回收验证**：
- 检查玩家是否拥有足够的可回收卡片
- 验证回收配置是否在冷却时间内
- 确保选择的卡片确实可以回收
- 防止重复提交和并发问题

**奖励发放**：
- 根据回收配置给予相应奖励
- 支持多种奖励类型：道具、货币、卡包等
- 奖励通过统一的奖励系统发放，确保一致性
- 记录回收行为，用于数据统计和分析

### 2. 最优组合算法

最优组合算法解决了如何用最少的卡片满足回收需求的问题，这是一个典型的多重背包问题。

```lua
-- CombNode类定义
local CombNode = {}
CombNode.__index = CombNode

function CombNode:New()
  local obj = {}
  setmetatable(obj, CombNode)
  obj.reached = false
  obj.count = {}
  for i = 1, 5 do
    obj.count[i] = 0
  end
  return obj
end

function CombNode:Compare(other)
  if not self.reached and other.reached then
    return true
  elseif self.reached and not other.reached then
    return false
  elseif not self.reached and not other.reached then
    return false
  else
    local selfSum = 0
    local otherSum = 0
    for i = 1, 5 do
      selfSum = selfSum + self.count[i]
      otherSum = otherSum + other.count[i]
    end
    return selfSum > otherSum
  end
end

-- 最优组合算法主函数
function AlbumActivityModel:FindBestCombination(needStar)
  local maxNeedStar = needStar + 4
  local maxCardStar = self.m_maxCardStar
  local dp = {}

  -- 初始化动态规划数组
  for i = 0, maxNeedStar do
    dp[i] = CombNode:New()
  end

  -- 计算各星级卡片数量
  local cards_type_count = {}
  for i = 1, maxCardStar do
    cards_type_count[i] = #self.m_SurplusCardList[i]
  end

  dp[0].reached = true

  -- 二进制优化的多重背包
  local cards = {}
  for i = 1, maxCardStar do
    local count = cards_type_count[i]
    local offset = 1
    while 0 < count do
      if count >= offset then
        table.insert(cards, {
          value = i * offset,
          type = i
        })
        count = count - offset
      else
        table.insert(cards, {
          value = i * count,
          type = i
        })
        count = 0
      end
      offset = offset * 2
    end
  end

  -- 动态规划求解
  for _, card in ipairs(cards) do
    for j = maxNeedStar, card.value, -1 do
      local temp = Table.DeepCopy(dp[j - card.value])
      temp.count[card.type] = temp.count[card.type] + card.value // card.type
      if dp[j]:Compare(temp) then
        dp[j] = temp
      end
    end
  end

  -- 找到最优解
  local ans = needStar
  while maxNeedStar >= ans and not dp[ans].reached do
    ans = ans + 1
  end

  if maxNeedStar < ans then
    return {}, 0
  end

  return dp[ans].count, ans
end
```

**问题建模**：
- 每种星级的卡片有不同的价值（等于星级数）
- 玩家拥有每种星级卡片的数量有限
- 目标是用最少的卡片数量达到指定的总价值

**算法设计**：
1. **二进制优化**：将每种卡片按照1、2、4、8...的数量分组，减少状态数量
2. **动态规划**：使用dp数组记录达到每个价值所需的最优方案
3. **状态比较**：通过CombNode类比较不同方案的优劣
4. **方案重构**：从最优状态反推出具体的卡片使用方案

**CombNode设计**：
- `reached`: 标记是否可以达到该价值
- `count`: 记录每种星级卡片的使用数量
- `Compare`: 比较两个方案的优劣，优先选择可达到的方案，其次选择使用卡片数量少的方案

**算法优势**：
- 时间复杂度相对较低，适合实时计算
- 保证找到最优解，不会浪费玩家的卡片
- 支持多种星级的混合使用，灵活性高

## 进度系统

### 1. 收集进度计算

进度系统跟踪玩家的收集状态，为UI显示和奖励发放提供数据支持。

```lua
-- 进度更新函数
function AlbumActivityModel:_UpdateProgress()
  self.m_albumCurProgress = 0
  self.m_albumMaxProgress = 0
  local arrSets
  local curSetProgress = 0
  self.m_mapProgressSets = {}

  for i, setId in ipairs(self.m_albumConfig.setGroup) do
    arrSets = self.m_mapCardSet[setId]
    curSetProgress = 0
    for i, cardId in ipairs(arrSets.cardGroup) do
      if 0 < self:GetCardCount(cardId) then
        curSetProgress = curSetProgress + 1
      end
      self.m_albumMaxProgress = self.m_albumMaxProgress + 1
    end
    self.m_mapProgressSets[setId] = curSetProgress
    self.m_albumCurProgress = self.m_albumCurProgress + curSetProgress
  end
end
```

**多层级进度**：
- **卡片级别**：单张卡片的拥有数量
- **套装级别**：每个套装的完成进度
- **卡册级别**：整个卡册的完成进度

**计算逻辑**：
1. 遍历所有套装配置
2. 对每个套装，统计已收集的卡片数量
3. 累加所有套装的进度得到总进度
4. 更新相关的缓存数据，避免重复计算

**数据结构**：
- `m_mapProgressSets`: 存储每个套装的完成数量
- `m_albumCurProgress`: 当前总收集数量
- `m_albumMaxProgress`: 最大可收集数量

### 2. 套装完成奖励

套装完成奖励鼓励玩家收集完整的卡片套装，而不是随意收集。

```lua
-- 卡片获得处理函数
function AlbumActivityModel:AcquireCard(cardId, scene)
  local count = self:GetCardCount(cardId)
  self.m_dbTable:Set(cardId, DBColumnValue, count + 1)
  local star = self.m_mapCards[cardId].star

  if 0 < count then
    -- 重复卡片处理
    self:AddNotNewCardStarCount(star)
    self:AddSurplusCard(cardId)
  else
    -- 新卡片处理
    self.m_albumCurProgress = self.m_albumCurProgress + 1
    local cardInfo = self:GetCardInfo(cardId)
    self.m_mapProgressSets[cardInfo.setId] = self.m_mapProgressSets[cardInfo.setId] + 1

    -- 检查套装完成
    local curPro, maxPro = self:GetSetCollectProgress(cardInfo.setId)
    if curPro == maxPro then
      setId = cardInfo.setId
      local rewards = self.m_mapCardSet[cardInfo.setId].reward
      RewardApi.CryptRewards(rewards, true)
      RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.AlbumSetRewards, EGameMode.Board, CacheItemType.Stack)
    end

    -- 检查整个卡册完成
    if self.m_albumCurProgress == self.m_albumMaxProgress then
      bAlbum = true
      local albumReward = self.m_albumConfig.reward
      RewardApi.CryptRewards(albumReward, true)
      RewardApi.AcquireRewardsLogic(albumReward, EPropertySource.Give, EBIType.AlbumRewards, EGameMode.Board, CacheItemType.Stack)
    end
  end

  return setId, bAlbum
end
```

**触发检测**：
- 每次获得新卡片时检查是否完成了套装
- 比较套装的当前进度和最大进度
- 只有首次完成时才发放奖励，避免重复发放

**奖励处理**：
- 从套装配置中读取奖励内容
- 通过加密奖励系统确保安全性
- 使用统一的奖励发放逻辑
- 记录奖励发放日志，便于追踪

**连锁反应**：
- 套装完成可能触发卡册完成
- 更新相关的成就和任务进度
- 触发UI更新和特效播放
- 发送事件通知其他系统

## UI系统

### 1. 抽卡动画系统

抽卡动画是提升用户体验的重要组成部分，通过精心设计的动画序列营造开包的仪式感。

```lua
-- 开包动画步骤
function AlbumActivityGachaWindow:StepOpenPack()
  self.m_step = AlbumAnimStep.OpenPack
  self:SetContinueState(false)
  local rect = self.m_packRootRect
  local seq = DOTween.Sequence()

  -- 播放卡包打开动画
  self.m_packSpineAnim.AnimationState:SetAnimation(0, "open", false)
  seq:AppendInterval(1.5)

  -- 计算卡片飞出时间
  local interval = 0.2
  local rotateDelay = 0
  local onceFlyTime = AlbumGachaCard.GetOpenFlyTime()
  local onceRotateTime = AlbumGachaCard.GetOpenRotateTime()
  local totalTime = interval * (#self.m_cardCells - 1) + (onceFlyTime + onceRotateTime)

  -- 新卡特殊处理
  if self.m_newLastIndex < #self.m_cardCells and 0 < self.m_newLastIndex then
    rotateDelay = totalTime - onceFlyTime
    totalTime = rotateDelay + (self.m_newLastIndex - 1) * interval + onceRotateTime + onceFlyTime
  end

  local targetPos = self.m_packRootRect.position + Vector3(0, -500, 0)
  seq:AppendCallback(function()
    for i, cell in ipairs(self.m_cardCells) do
      cell:PlayOpenAnim(targetPos, 0.2, interval * (i - 1), i <= self.m_newLastIndex and rotateDelay or 0)
    end
  end)

  seq:AppendInterval(totalTime)
  seq:AppendCallback(function()
    self:StepCollectCard()
  end)
end
```

**动画阶段设计**：
1. **卡包进入**：卡包从指定位置飞入屏幕中央
2. **等待开启**：卡包播放待机动画，等待玩家点击
3. **开启动画**：卡包打开，卡片依次飞出
4. **卡片展示**：卡片翻转显示正面，新卡片有特殊效果
5. **收集动画**：卡片飞向收集区域，完成收集

**时间控制**：
- 使用DOTween序列控制动画时间
- 支持跳过动画，提高操作效率
- 新卡片有额外的展示时间和特效
- 动画时间可配置，便于调整体验

**特效系统**：
- 新卡片有发光和缩放效果
- 金卡有特殊的背景特效
- 支持不同稀有度的差异化表现
- 音效配合，增强沉浸感

### 2. 卡片收集动画

收集动画将抽到的卡片"送回"玩家的收藏中，提供视觉上的反馈。

```lua
-- 收集动画步骤
function AlbumActivityGachaWindow:StepCollectCard()
  self.m_step = AlbumAnimStep.CollectCard
  local originX = self.m_collectEntryRect.anchoredPosition.x
  local seq = DOTween.Sequence()

  -- 窗口淡出
  self.m_windowCanvasGroup:DOFade(0, 0.2)
  seq:Append(self.m_collectEntryRect:DOAnchorPosX(-100, 0.2):SetEase(Ease.InOutSine))

  local interval = 0.1
  seq:AppendCallback(function()
    for i, cell in ipairs(self.m_cardCells) do
      cell:PlayCollectAnim(self.m_collectEntryRect.position, 0.2, interval * (i - 1))
    end
  end)

  local onceTime = AlbumGachaCard.GetCollectAnimTime()
  local totalTime = interval * (#self.m_cardCells - 1) + onceTime
  seq:AppendInterval(totalTime)
  seq:Append(self.m_collectEntryRect:DOAnchorPosX(originX, 0.2):SetEase(Ease.InSine))
  seq:AppendCallback(function()
    self.m_step = nil
    self:TryClose()
  end)
end
```

**动画设计**：
- 卡片从展示位置飞向收集入口
- 飞行过程中逐渐缩小，模拟收纳效果
- 支持批量收集和单个收集两种模式
- 收集完成后触发相关的UI更新

**性能优化**：
- 当卡片数量过多时减少动画间隔
- 支持立即完成动画，避免等待时间过长
- 合理控制同时播放的动画数量
- 及时销毁动画对象，避免内存泄漏

## 数据持久化

### 1. 数据存储策略

数据持久化确保玩家的收集进度不会丢失，支持跨设备同步和离线游戏。

```lua
-- 数据加载函数
function AlbumActivityModel:LoadDefaultConfig()
  -- 缓存的卡包数据
  self.m_cacheProperty = {}
  local strCache = self.m_dbTable:GetValue(CacheProperty, "value")
  if strCache ~= nil and strCache ~= "" then
    self.m_cacheProperty = json.decode(strCache)
  end

  -- 红点数据
  self.m_redPointData = {}
  local strRedCache = self.m_dbTable:GetValue(RedHintKey, "value")
  if strRedCache ~= nil and strRedCache ~= "" then
    self.m_redPointData = json.decode(strRedCache)
  end

  -- 回收数据
  self.m_recyleCardData = {}
  local strRecyleCache = self.m_dbTable:GetValue(RecyleCardKey, "value")
  if strRecyleCache ~= nil and strRecyleCache ~= "" then
    self.m_recyleCardData = json.decode(strRecyleCache)
  end

  -- 已回收卡片数据
  self.m_alReadyrecyleCards = {}
  local strCycleCardsCache = self.m_dbTable:GetValue(AlreadyCycleCardKey, "value")
  if strCycleCardsCache ~= nil and strCycleCardsCache ~= "" then
    self.m_alReadyrecyleCards = json.decode(strCycleCardsCache)
  end

  -- 小丑卡数据
  self.m_jokerCards = {}
  local strJokerCache = self.m_dbTable:GetValue(JokerCardKey, "value")
  if strJokerCache ~= nil and strJokerCache ~= "" then
    self.m_jokerCards = json.decode(strJokerCache)
  end
end
```

**存储内容分类**：
- **卡片数据**：每张卡片的拥有数量
- **缓存数据**：未开启的卡包数量
- **红点数据**：新获得卡片的提示信息
- **回收数据**：回收功能的冷却时间
- **小丑卡数据**：小丑卡的数量和获得时间

**序列化方案**：
- 使用JSON格式存储复杂数据结构
- 简单数值直接存储，减少序列化开销
- 支持数据版本控制，便于后续升级
- 包含数据校验，防止篡改和损坏

**读取策略**：
- 游戏启动时一次性加载所有数据
- 使用缓存减少数据库访问次数
- 支持增量更新，只保存变化的数据
- 异常情况下的数据恢复机制

### 2. 数据同步机制

数据同步确保玩家在不同设备上的游戏进度保持一致。

**同步时机**：
- 玩家登录时下载服务器数据
- 重要操作后立即上传数据
- 定期自动同步，防止数据丢失
- 游戏退出时保存所有变更

**冲突处理**：
- 服务器数据优先原则
- 支持数据合并，避免进度丢失
- 异常情况下的人工介入机制
- 详细的同步日志，便于问题排查

## 总结

### 核心特性

1. **多层权重系统**
   - 固定分配确保基础价值
   - 随机权重提供变化性
   - 权重可配置，便于调整平衡性

2. **完善的保底机制**
   - 防止极端的坏运气情况
   - 不同星级独立的保底计数
   - 保底触发时优先选择新卡片

3. **灵活的小丑卡系统**
   - 提供确定性的获取途径
   - 支持普通和金卡两种类型
   - 过期机制鼓励及时使用

4. **智能的回收系统**
   - 最优化算法减少卡片浪费
   - 多种回收选项满足不同需求
   - 冷却机制防止过度回收

5. **丰富的UI动画**
   - 精心设计的开包仪式感
   - 差异化的稀有度表现
   - 性能优化的动画系统

### 技术亮点

1. **动态规划算法**
   - 解决多重背包问题
   - 二进制优化提高效率
   - 保证最优解的正确性

2. **事件驱动架构**
   - 模块间松耦合设计
   - 支持灵活的功能扩展
   - 便于维护和调试

3. **完整的数据持久化**
   - 多层次的数据存储
   - 可靠的同步机制
   - 异常情况的恢复能力

4. **高效的权重算法**
   - O(n)时间复杂度
   - 严格的概率分布
   - 支持动态权重调整

### 平衡性设计

1. **稀有度控制**
   - 星级权重递减设计
   - 金卡的特殊获取途径
   - 保底机制的合理阈值

2. **经济平衡**
   - 回收系统的价值转换
   - 小丑卡的稀缺性控制
   - 奖励的合理分配

3. **用户体验**
   - 避免过度的随机性
   - 提供多样化的获取途径
   - 保持长期的收集动力

该系统通过精心设计的概率机制、完善的保底系统和丰富的交互体验，为玩家提供了既有随机性又有确定性的卡片收集游戏，在保持游戏乐趣的同时确保了公平性和可持续性。
