ExtraBoardActivityEventType = {StateChanged = 1}
ExtraBoardActivityModel = setmetatable({
  eModelType = ActivityModelType.ExtraBoard
}, BaseActivityModel)
ExtraBoardActivityModel.__index = ExtraBoardActivityModel
ExtraBoardActivityModel.CurrentMergeLevel = "currentMergeLevel"
ExtraBoardActivityModel.ItemGived = "extraBoardItemGived"
ExtraBoardActivityModel.CobwebRound = "cobwebRound"
ExtraBoardActivityModel.CobwebItemList = "cobwebItemList"
ExtraBoardActivityModel.CobwebCompleteFlag = "completed"

function ExtraBoardActivityModel:Init(activityType, activityDataTable, itemDataTable, itemLayerDataTable, itemCacheDataTable)
  self.m_itemDataTable = itemDataTable
  self.m_itemLayerDataTable = itemLayerDataTable
  self.m_itemCacheDataTable = itemCacheDataTable
  self.m_activityDefinition = ExtraBoardActivityDefinition[activityType]
  self.m_tokenHelper = ActivityTokenHelper.Create(self, activityDataTable, EFlyElementLabelStyle.Default)
  BaseActivityModel.Init(self, activityType, activityDataTable)
end

function ExtraBoardActivityModel:Destroy()
  BaseActivityModel.Destroy(self)
  if self.m_boardModel ~= nil then
    self.m_boardModel:Destroy()
  end
  if self.m_tokenHelper ~= nil then
    self.m_tokenHelper:Destroy()
  end
end

function ExtraBoardActivityModel:GetActivityDefinition()
  return self.m_activityDefinition
end

function ExtraBoardActivityModel.GetActiveModel()
  local model
  for activityType, definition in pairs(ExtraBoardActivityDefinition) do
    model = GM.ActivityManager:GetModel(activityType)
    if model ~= nil and model:GetState() == ActivityState.Started then
      return model
    end
  end
end

function ExtraBoardActivityModel:_LoadOtherServerConfig(config)
  local serverConfig = config.extraBoardConfig and config.extraBoardConfig[1] or {}
  self.m_itemPrefix = serverConfig.itemPrefix
  self.m_maxLevel = serverConfig.maxLevel
  self.m_boardName = serverConfig.board
  self.m_cobwebName = serverConfig.cobweb
  self.m_maxReward = serverConfig.maxReward
  self.m_config.arrMergeLine = {}
  local itemType
  for i = 1, self.m_maxLevel do
    itemType = string.format("%s_1_%d", self.m_itemPrefix, i)
    self.m_config.arrMergeLine[i] = itemType
  end
  if self.m_tokenHelper ~= nil then
    self.m_tokenHelper:LoadConfig(config)
  end
  self:_LoadCobwebConfig()
end

function ExtraBoardActivityModel:_DropData()
  BaseActivityModel._DropData(self)
  if self.m_boardModel ~= nil then
    self.m_boardModel:Destroy()
    self.m_boardModel:DropData()
    self.m_boardModel = nil
  else
    self.m_itemDataTable:Drop()
    self.m_itemLayerDataTable:Drop()
    self.m_itemCacheDataTable:Drop()
  end
end

function ExtraBoardActivityModel:GetBoardEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.ExtraBoardActivity,
    entryPrefabName = self.m_activityDefinition.BoardEntryPrefabName,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

function ExtraBoardActivityModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.ExtraBoardActivity,
    entryPrefabName = self.m_activityDefinition.EntryPrefabName,
    hudKey = self.m_activityDefinition.EntryButtonKey,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

function ExtraBoardActivityModel.GetButtonTarget(propertyType)
  local bExtraBoard, activityType = ExtraBoardActivityModel.IsExtraBoardActivityItem(propertyType)
  if bExtraBoard then
    local window = GM.UIManager:GetOpenedViewByName(ExtraBoardActivityDefinition[activityType].MainWindowPrefabName)
    if window ~= nil then
      local activityModel = GM.ActivityManager:GetModel(activityType)
      local itemLevel = activityModel:GetLevelByItemCode(propertyType)
      if itemLevel ~= 0 then
        return window:GetMergeLineItemIconArea(itemLevel)
      end
    elseif GM.SceneManager:GetGameMode() == EGameMode.Board then
      local boardView = MainBoardView.GetInstance()
      if boardView ~= nil then
        local orderArea = boardView:GetOrderArea()
        if orderArea ~= nil then
          return orderArea:GetIconAreaByActivityType(activityType)
        end
      end
    elseif GM.SceneManager:GetGameMode() == EGameMode.Main then
      return TutorialHelper.GetHudButton(ExtraBoardActivityDefinition[activityType].EntryButtonKey)
    end
  end
end

function ExtraBoardActivityModel:GetAllStateChangedEvent()
  return {
    self.m_activityDefinition.StateChangedEvent
  }
end

function ExtraBoardActivityModel:IsActivityOpen()
  return self:GetState() == ActivityState.Started
end

function ExtraBoardActivityModel:_OnStateChanged()
  local state = self:GetState()
  if state == ActivityState.Started or state == ActivityState.Ended then
    if self:IsCobwebOpen() and self:GetCobwebRound() == 0 then
      self:EnterNextCobwebRound()
    end
    if self.m_boardModel == nil then
      self:_LoadActivityBoardModel()
    end
    local bItemGived = self.m_dbTable:GetValue(ExtraBoardActivityModel.ItemGived, "value") or 0
    if bItemGived == 0 and state == ActivityState.Started then
      self:TutorialCacheItems()
      self:LogActivity(EBIType.ActivityRankUp, 0)
    end
  else
    if self.m_boardModel ~= nil then
      self.m_boardModel:Destroy()
    end
    self.m_boardModel = nil
  end
  self.m_endRewards = nil
  self.event:Call(ExtraBoardActivityEventType.StateChanged)
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
end

function ExtraBoardActivityModel:_LoadActivityBoardModel()
  local horizontalTiles, verticalTiles, initCodeMap = self:GetInitBoardInfo()
  self.m_boardModel = ExtraBoardActivityBoardModel.Create(self.m_itemDataTable, self.m_itemLayerDataTable, self.m_itemCacheDataTable, initCodeMap, horizontalTiles, verticalTiles, self:GetType())
  self:InitUpdateLevel()
end

function ExtraBoardActivityModel:GetBoardModel()
  return self.m_boardModel
end

function ExtraBoardActivityModel:GetInitCodeMap()
  local boardName = self.m_boardName
  local fileConfig = require("Data.Config.ExtraBoardActivityBoardConfig")
  local codeMap = fileConfig[boardName]
  if Table.IsEmpty(codeMap) then
    Log.Error("棋盘名称不存在， 请检查:" .. (boardName or "nil"))
    return 6, 8, {}
  end
  local verticalTiles = #codeMap
  local horizontalTiles = #codeMap[1]
  codeMap = Table.DeepCopy(codeMap)
  for y, line in pairs(codeMap) do
    for x, code in pairs(line) do
      codeMap[y][x] = self:GetReplacedExtraBoardItem(code)
    end
  end
  return horizontalTiles, verticalTiles, codeMap
end

function ExtraBoardActivityModel:GetInitBoardInfo()
  local horizontalTiles, verticalTiles, initCodeMap = self:GetInitCodeMap()
  return horizontalTiles, verticalTiles, initCodeMap
end

function ExtraBoardActivityModel:TutorialCacheItems()
  local bItemGived = self.m_dbTable:GetValue(ExtraBoardActivityModel.ItemGived, "value") or 0
  if bItemGived == 1 then
    return
  end
  if self.m_config == nil or self.m_config.arrMergeLine == nil then
    return
  end
  local rewards = {
    {
      [PROPERTY_TYPE] = self.m_config.arrMergeLine[1],
      [PROPERTY_COUNT] = 3
    }
  }
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.ExtraBoardTutorialCacheItem, nil, CacheItemType.ExtraBoard)
  self.m_dbTable:Set(ExtraBoardActivityModel.ItemGived, "value", 1)
end

function ExtraBoardActivityModel:GetCacheCount()
  if self.m_boardModel == nil then
    return 0
  end
  return self.m_boardModel:GetCachedItemCount()
end

function ExtraBoardActivityModel:GetMergeLength()
  return self.m_config and self.m_config.arrMergeLine and #self.m_config.arrMergeLine or 0
end

function ExtraBoardActivityModel:GetItemTypeByIndex(index)
  return self.m_config.arrMergeLine[index]
end

function ExtraBoardActivityModel:SetLevel(level)
  self.m_dbTable:Set(ExtraBoardActivityModel.CurrentMergeLevel, "value", level)
  self:TryAcquireMaxLevelReward()
  self:LogActivity(EBIType.ActivityRankUp, level)
  EventDispatcher.DispatchEvent(EEventType.ExtraBoardActivityItemUnlocked)
end

function ExtraBoardActivityModel:GetRareItemChainId()
  local itemPrefix = self.m_itemPrefix
  return itemPrefix .. "_2"
end

function ExtraBoardActivityModel:GetRareMaxLevel()
  return GM.ItemDataModel:GetChainMaxLevel(self:GetRareItemChainId())
end

function ExtraBoardActivityModel:GetRareItemTypeByIndex()
  local chainId = self:GetRareItemChainId()
end

function ExtraBoardActivityModel:GetLevel()
  return self.m_dbTable:GetValue(ExtraBoardActivityModel.CurrentMergeLevel, "value") or 0
end

function ExtraBoardActivityModel:UpdateLevel(itemCode)
  local lvl = self:GetLevelByItemCode(itemCode)
  local curLvl = self:GetLevel()
  if lvl > curLvl then
    self:SetLevel(lvl)
    return true
  end
  return false
end

function ExtraBoardActivityModel:InitUpdateLevel()
  if self.m_boardModel == nil then
    return
  end
  local curLvl = self:GetLevel()
  local lvl = 0
  local tempLvl
  for item, _ in pairs(self.m_boardModel:GetAllBoardItems()) do
    if item ~= nil then
      local code = item:GetCode()
      tempLvl = self:GetLevelByItemCode(code)
      if lvl < tempLvl then
        lvl = tempLvl
      end
    end
  end
  if curLvl < lvl then
    self:SetLevel(lvl)
  end
end

function ExtraBoardActivityModel:GetEndRewards()
  if not Table.IsEmpty(self.m_endRewards) then
    return self.m_endRewards
  end
  local arrRewards = {}
  if self.m_boardModel == nil then
    return arrRewards
  end
  for item, _ in pairs(self.m_boardModel:GetAllBoardItems()) do
    if item ~= nil then
      local tempLvl = self:GetLevelByItemCode(item:GetCode())
      local collectTb = item:GetComponent(ItemCollectable)
      if collectTb then
        Table.ListAppend(arrRewards, collectTb:GetRewards())
      end
      local bubbleTb = item:GetComponent(ItemRewardBubble)
      if bubbleTb then
        Table.ListAppend(arrRewards, {
          {
            [PROPERTY_TYPE] = bubbleTb:GetInnerItemCode(),
            [PROPERTY_COUNT] = 1
          }
        })
      end
      local itemSpread = item:GetComponent(ItemSpread)
      if itemSpread then
        local arrRwds = itemSpread:GetNotSpreadRewards()
        Table.ListAppend(arrRewards, arrRwds)
      end
    end
  end
  arrRewards = RewardApi.GetMergedRewards(arrRewards)
  RewardApi.CryptRewards(arrRewards)
  self.m_endRewards = arrRewards
  return arrRewards
end

function ExtraBoardActivityModel:GetLevelByItemCode(itemCode)
  if not self.m_config or Table.IsEmpty(self.m_config.arrMergeLine) then
    return 0
  end
  itemCode = self:GetReplacedExtraBoardItem(itemCode)
  for i, v in ipairs(self.m_config.arrMergeLine) do
    if v == itemCode then
      return i
    end
  end
  return 0
end

function ExtraBoardActivityModel:GetItemCodeByLevel(lvl)
  if Table.IsEmpty(self.m_config.arrMergeLine) then
    return
  end
  return self.m_config.arrMergeLine[lvl]
end

function ExtraBoardActivityModel:IsMainLineItem(itemCode)
  if StringUtil.IsNilOrEmpty(itemCode) then
    return false
  end
  local level = GM.ItemDataModel:GetChainLevel(itemCode)
  return self:GetItemCodeByLevel(level) == itemCode
end

function ExtraBoardActivityModel:GetMaxLevel()
  return self.m_maxLevel
end

function ExtraBoardActivityModel:IsMaxLevel()
  return self:GetLevel() == self.m_maxLevel
end

function ExtraBoardActivityModel:CanAddScore()
  return self:GetState() == ActivityState.Started
end

function ExtraBoardActivityModel:GetTipWindowName()
  return UIPrefabConfigName.ExtraBoardActivityHelpWindow
end

function ExtraBoardActivityModel:GetResourceLabels()
  return self.m_activityDefinition.ResourceLabels
end

function ExtraBoardActivityModel.IsExtraBoardActivityItem(type)
  for activityType, _ in pairs(ExtraBoardActivityDefinition) do
    local extraBoardModel = GM.ActivityManager:GetModel(activityType)
    if extraBoardModel:GetState() == ActivityState.Started and extraBoardModel:IsSelfItem(type) then
      return true, activityType
    end
  end
end

function ExtraBoardActivityModel.IsExtraBoardMainItem(type)
  for activityType, _ in pairs(ExtraBoardActivityDefinition) do
    local extraBoardModel = GM.ActivityManager:GetModel(activityType)
    if extraBoardModel:GetState() == ActivityState.Started and extraBoardModel:IsMainLineItem(type) then
      return true, activityType
    end
  end
end

function ExtraBoardActivityModel.IsExtraBoardCommonItem(type)
  return StringUtil.StartWith(type, ItemCodePrefix.ExtraBoardCommon)
end

function ExtraBoardActivityModel.GetRealExtraBoardItem(type)
  local extraBoardModel = ExtraBoardActivityModel.GetActiveModel()
  if extraBoardModel ~= nil and ExtraBoardActivityModel.IsExtraBoardCommonItem(type) then
    return extraBoardModel:GetReplacedExtraBoardItem(type)
  end
end

function ExtraBoardActivityModel:IsSelfItem(type)
  return StringUtil.StartWith(type, self.m_itemPrefix) or ExtraBoardActivityModel.IsExtraBoardCommonItem(type)
end

function ExtraBoardActivityModel:GetReplacedExtraBoardItem(type)
  return StringUtil.Replace(type, ItemCodePrefix.ExtraBoardCommon, self.m_itemPrefix .. "_")
end

function ExtraBoardActivityModel.GetPaperBoxSprite()
  for activityType, activityDefinition in pairs(ExtraBoardActivityDefinition) do
    local extraBoardModel = GM.ActivityManager:GetModel(activityType)
    if extraBoardModel:GetState() == ActivityState.Started then
      return activityDefinition.PaperBoxSprite
    end
  end
end

function ExtraBoardActivityModel.GetCobwebSprite()
  for activityType, activityDefinition in pairs(ExtraBoardActivityDefinition) do
    local extraBoardModel = GM.ActivityManager:GetModel(activityType)
    if extraBoardModel:GetState() == ActivityState.Started then
      return activityDefinition.CobwebSprite, activityDefinition.CobwebSpriteOffset
    end
  end
end

function ExtraBoardActivityModel:CanPopEndWindow()
  return self:HasWindowOpenedOnce(ActivityState.Started) and not self:HasWindowOpenedOnce(ActivityState.Ended)
end

function ExtraBoardActivityModel:IsAcquireTypeValid(type)
  return self.m_tokenHelper ~= nil and self.m_tokenHelper:IsAcquireTypeValid(type)
end

function ExtraBoardActivityModel:GetMaxLevelReward()
  return self.m_maxReward
end

function ExtraBoardActivityModel:TryAcquireMaxLevelReward()
  if Table.IsEmpty(self.m_maxReward) or self:GetLevel() < self:GetMaxLevel() then
    return
  end
  RewardApi.CryptRewards(self.m_maxReward)
  RewardApi.AcquireRewardsLogic(self.m_maxReward, EPropertySource.Give, EBIType.ExtraBoardMaxLevelReward, EGameMode.Board, CacheItemType.Stack)
end

function ExtraBoardActivityModel:_LoadCobwebConfig()
  if StringUtil.IsNilOrEmpty(self.m_cobwebName) then
    return
  end
  local cobwebFileConfig = require("Data.Config.ExtraBoardCobwebConfig")
  if cobwebFileConfig[self.m_cobwebName] == nil then
    Log.Error("cobweb配置的名称不存在: " .. self.m_cobwebName)
    return
  end
  self.m_cobwebConfig = Table.DeepCopy(cobwebFileConfig[self.m_cobwebName])
  for y, line in ipairs(self.m_cobwebConfig.line or {}) do
    for x, code in ipairs(line) do
      self.m_cobwebConfig.line[y][x] = self:GetReplacedExtraBoardItem(code)
    end
  end
  for y, line in ipairs(self.m_cobwebConfig.loop or {}) do
    for x, code in ipairs(line) do
      self.m_cobwebConfig.loop[y][x] = self:GetReplacedExtraBoardItem(code)
    end
  end
end

function ExtraBoardActivityModel:IsCobwebOpen()
  return self.m_cobwebConfig ~= nil
end

function ExtraBoardActivityModel:EnterNextCobwebRound()
  local newRound = self:GetCobwebRound() + 1
  local fixRound = #self.m_cobwebConfig.line
  local loopRound = #(self.m_cobwebConfig.loop or {})
  self:LogActivity(EBIType.ExtraBoardFinishCobwebRound, self:GetCobwebRound())
  if loopRound == 0 and newRound > fixRound then
    return
  end
  self.m_dbTable:Set(ExtraBoardActivityModel.CobwebRound, "value", newRound)
  if newRound <= fixRound then
    self:SetCobwebItemList(self.m_cobwebConfig.line[newRound])
  else
    local index = (newRound - fixRound) % loopRound
    index = index == 0 and loopRound or index
    self:SetCobwebItemList(self.m_cobwebConfig.loop[index])
  end
  if 1 < newRound then
    EventDispatcher.DispatchEvent(EEventType.ExtraBoardEnterNextCobwebRound)
  end
end

function ExtraBoardActivityModel:GetCobwebRound()
  return self.m_dbTable:GetValue(ExtraBoardActivityModel.CobwebRound, "value") or 0
end

function ExtraBoardActivityModel:GetCobwebItemList()
  local strData = self.m_dbTable:GetValue(ExtraBoardActivityModel.CobwebItemList, "value")
  if not StringUtil.IsNilOrEmpty(strData) then
    strData = StringUtil.Replace(strData, "@", ",")
    return json.decode(strData) or {}
  end
end

function ExtraBoardActivityModel:SetCobwebItemList(arrItems)
  if Table.IsEmpty(arrItems) then
    self.m_dbTable:Remove(ExtraBoardActivityModel.CobwebItemList, "value")
    return
  end
  local strData = StringUtil.Replace(json.encode(arrItems), ",", "@")
  self.m_dbTable:Set(ExtraBoardActivityModel.CobwebItemList, "value", strData)
end

function ExtraBoardActivityModel:RemoveCobwebItem(index)
  local arrItems = self:GetCobwebItemList()
  if arrItems[index] == nil or arrItems[index] == ExtraBoardActivityModel.CobwebCompleteFlag then
    Log.Error("蛛网序号不存在或者已经完成 index: " .. (index or "nil"))
    return
  end
  arrItems[index] = ExtraBoardActivityModel.CobwebCompleteFlag
  self:SetCobwebItemList(arrItems)
end

function ExtraBoardActivityModel:TryEnterNextCobwebRound()
  if self:HasFinishedCurCobwebRound() then
    self:EnterNextCobwebRound()
  end
end

function ExtraBoardActivityModel:HasFinishedCurCobwebRound()
  local arrItems = self:GetCobwebItemList()
  if Table.IsEmpty(arrItems) then
    return false
  end
  for _, item in ipairs(arrItems) do
    if item ~= ExtraBoardActivityModel.CobwebCompleteFlag then
      return false
    end
  end
  return true
end
