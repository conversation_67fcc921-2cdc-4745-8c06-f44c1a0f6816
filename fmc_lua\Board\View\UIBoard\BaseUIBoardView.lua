BaseUIBoardView = setmetatable({}, BaseInteractiveBoardView)
BaseUIBoardView.__index = BaseUIBoardView

function BaseUIBoardView:Init(boardModel, deleteButton, itemTipButton)
  BaseInteractiveBoardView.Init(self, boardModel)
  self.m_activityType = boardModel:GetActivityType()
  self.m_cacheRoot:Init(self)
  self.m_deleteButton = deleteButton
  self.m_itemTipButton = itemTipButton
  EventDispatcher.AddActiveListener(EEventType.ItemFlyChanged, self, self.TryStartBoardPrompt)
  self:TryStartBoardPrompt()
  if self.m_deleteButton ~= nil then
    self.m_deleteButton:Init(self:GetModel())
  end
  if self.m_itemTipButton ~= nil then
    self.m_itemTipButton:Init(self:GetModel())
  end
  UIUtil.SetLocalPosition(self.m_canvas.transform, BaseBoardModel.TileSize * boardModel:GetHorizontalTiles() / 2)
end

function BaseUIBoardView:_InitTileItemView()
  local boardModel = self.m_model
  local position
  GM.UIManager:SetEventLock(true, self)
  local loadFunc = function()
    for y = 1, boardModel:GetVerticalTiles() do
      for x = 1, boardModel:GetHorizontalTiles() do
        position = boardModel:CreatePosition(x, y)
        self:_AddTile(position)
        local itemModel = boardModel:GetItem(position)
        if itemModel ~= nil then
          self:_AddItemView(itemModel)
        end
      end
      coroutine.yield()
    end
    self.m_coroutine = nil
    self:_UpdateTile()
    GM.UIManager:SetEventLock(false, self)
  end
  self.m_coroutine = coroutine.create(loadFunc)
end

function BaseUIBoardView:OnDestroy()
  BaseInteractiveBoardView.OnDestroy(self)
  self:_CancelBoardPrompt()
  self.m_coroutine = nil
  GM.UIManager:RemoveAllEventLocks(self)
end

function BaseUIBoardView:Update()
  BaseInteractiveBoardView:Update(self)
  if self.m_coroutine ~= nil then
    coroutine.resume(self.m_coroutine)
  end
end

function BaseUIBoardView:UpdateBoardInfoBar(itemModel, inBoard)
  if self.m_deleteButton ~= nil then
    self.m_deleteButton:UpdateItem(itemModel)
  end
  if self.m_itemTipButton ~= nil then
    self.m_itemTipButton:UpdateItem(itemModel)
  end
end

function BaseUIBoardView:IsTopBoardView()
  return GM.UIManager:GetOpenedTopView() == self:_GetBoardWindow()
end

function BaseUIBoardView:_GetBoardWindow()
  Log.Error("GetBoardWindow 是抽象接口")
end

function BaseUIBoardView:_CanStartPrompt()
  return BaseInteractiveBoardView._CanStartPrompt(self) and self:IsTopBoardView()
end

function BaseUIBoardView:_GetBoardPrompts()
  local prompts = {
    BoardPromptMergeItems.Create(),
    UIBoardPromptTapCacheItems.Create(),
    UIBoardPromptTapRewardBubbleItem.Create(),
    UIBoardPromptTapSpreadItem.Create(),
    BoardPromptCollect.Create()
  }
  return prompts
end

function BaseUIBoardView:OnPointerDown(eventData)
  self:_OnPointerDown(eventData)
end

function BaseUIBoardView:OnDrag(eventData)
  self:_OnDrag(eventData)
end

function BaseUIBoardView:OnPointerUp(eventData)
  self:_OnPointerUp(eventData)
end

function BaseUIBoardView:_OnPopCachedItem(message)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPlaceItem)
  local itemView = self:_AddItemView(message.New)
  if itemView ~= nil then
    local worldPosition = self.m_cacheRoot:GetFlyTargetPosition()
    local sourcePosition = self.transform:InverseTransformPoint(worldPosition)
    local targetPosition = itemView.transform.localPosition
    self:_PlayJumpAnimation(itemView, sourcePosition, targetPosition)
  end
  self:TryStartBoardPrompt()
end

function BaseUIBoardView:_OnOpenView()
  BaseInteractiveBoardView._OnOpenView(self)
  if not self:IsTopBoardView() then
    self:_CancelBoardPrompt()
  end
end

function BaseUIBoardView:GetCacheRoot()
  return self.m_cacheRoot
end

function BaseUIBoardView:GetRemoveButton()
  return self.m_itemDeleteButton
end
