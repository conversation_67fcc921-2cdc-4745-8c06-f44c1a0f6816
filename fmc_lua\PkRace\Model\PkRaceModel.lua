PkRaceModel = setmetatable({
  FixedPlayerNumber = 1,
  EntryOpName = "BLPkRaceEntry",
  EntryGapTime = 1800,
  ShowEntryMask = false
}, BaseRaceActivityModel)
PkRaceModel.__index = PkRaceModel
PkRaceModel.RoundConfigKey = "pk_race"

function PkRaceModel:Init(activityType, virtualDBTable)
  self.m_tokenHelper = ActivityTokenHelper.Create(self, virtualDBTable)
  BaseRaceActivityModel.Init(self, activityType, virtualDBTable)
end

function PkRaceModel:Destroy()
  BaseRaceActivityModel.Destroy(self)
  self.m_tokenHelper:Destroy()
end

function PkRaceModel:GetActivityDefinitionByType(activityType)
  return PkRaceDefinition[activityType]
end

function PkRaceModel:GetBoardEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.PkRace,
    entryPrefabName = self.m_activityDefinition.BoardEntryPrefabName,
    checkFun = function()
      return self:CanShowBoardEntry()
    end
  }
end

function PkRaceModel:GetMapEntryShowConfig()
  return {
    statusChangeEvent = self.m_activityDefinition.StateChangedEvent,
    eEntryRootKey = EEntryRootKey.PkRace,
    entryPrefabName = self.m_activityDefinition.EntryPrefabName,
    hudKey = self.m_activityDefinition.EntryButtonKey,
    checkFun = function()
      return self:IsActivityOpen()
    end
  }
end

function PkRaceModel:AcquireActivityToken(score)
  if 0 < score then
    self:_AddMyScore(score, "order")
  end
end

function PkRaceModel:GetActivityTokenNumber()
  return self:GetMyScore()
end

function PkRaceModel:IsActivityOpen()
  return BaseRaceActivityModel.IsActivityOpen(self) and self:GetState() == ActivityState.Started
end

function PkRaceModel:CanShowBoardEntry()
  return self:GetState() == ActivityState.Started and self:IsActivityOpen() and self:IsInRace()
end

function PkRaceModel:GetScoreRatio()
  return self.m_scoreRatio or 1
end

function PkRaceModel:_LoadOtherServerConfig(config)
  BaseRaceActivityModel._LoadOtherServerConfig(self, config)
  local arrConfig = Table.DeepCopy(config[PkRaceModel.RoundConfigKey], true)
  table.sort(arrConfig, function(a, b)
    return a.round < b.round
  end)
  self.m_config.roundConfigs = arrConfig
  local orderTokenConfigs = config.order_token_control
  self.m_scoreRatio = nil
  if orderTokenConfigs ~= nil and orderTokenConfigs[1] ~= nil then
    self.m_scoreRatio = orderTokenConfigs[1].ratio
    if orderTokenConfigs[1].score_min ~= nil or orderTokenConfigs[1].score_max ~= nil then
      Log.Error("order_token_control表配置有误, 1v1活动不允许根据难度分进行分层")
    end
  end
  if not Table.IsEmpty(orderTokenConfigs) and 1 < #orderTokenConfigs then
    Log.Error("order_token_control表配置有误, 1v1活动不允许有多条配置")
  end
  self.m_tokenHelper:LoadConfig(config)
  self:CheckConfigValid()
end

function PkRaceModel:GetOneCompetitorData()
  local arrPlayerData = self:GetAllPlayerData()
  for _, playerData in ipairs(arrPlayerData or {}) do
    if not playerData:IsMySelf() then
      return playerData
    end
  end
end

function PkRaceModel:GetCurrentRoundReward()
  local curRound = self:GetCurrentRound()
  local roundConfig = self.m_config.roundConfigs[curRound]
  if not roundConfig then
    return {}
  end
  local rewards = {
    roundConfig.rankReward1,
    roundConfig.rankReward2
  }
  return rewards
end

function PkRaceModel:GetRoundProgressReward(round)
  local roundConfig = self.m_config.roundConfigs[round]
  if roundConfig ~= nil then
    return roundConfig.roundRewards
  end
end

function PkRaceModel:GetRoundProgressRewardConfigs()
  local arrConfig = {}
  local maxRound = 0
  for round, config in ipairs(self.m_config.roundConfigs) do
    if not Table.IsEmpty(config.roundRewards) then
      table.insert(arrConfig, {
        round = round,
        roundRewards = config.roundRewards,
        uiCode = config.uiCode
      })
      maxRound = math.max(maxRound, round)
    end
  end
  return arrConfig, maxRound
end

function PkRaceModel:TryClaimReward()
  local bSuccess, rewards = BaseRaceActivityModel.TryClaimReward(self)
  local progressRewards
  if bSuccess then
    progressRewards = self:GetRoundProgressReward(self:GetCurrentRound())
    RewardApi.CryptRewards(progressRewards)
    RewardApi.AcquireRewardsLogic(progressRewards, EPropertySource.Give, self.m_activityDefinition.RoundRewardBIType, EGameMode.Board, CacheItemType.Stack)
  end
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
  return bSuccess, rewards, progressRewards
end

function PkRaceModel:TryOpenMainWindow(bEntry)
  if not self:HasNetwork() then
    self:OnNetError(function()
      self:TryOpenMainWindow()
    end)
    return
  end
  GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_type, false, bEntry)
end
