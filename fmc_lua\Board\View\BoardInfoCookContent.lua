BoardInfoCookContent = {}
BoardInfoCookContent.__index = BoardInfoCookContent
ECookStartStatus = {
  Valid = 1,
  NotInOrder = 2,
  EnoughDish = 3
}

function BoardInfoCookContent:Awake()
  EventDispatcher.AddListener(EEventType.ShowItemTestInfoChanged, self, self._OnShowItemTestInfoChanged)
end

function BoardInfoCookContent:UpdateContent(itemCook, showCookStartTip, inBoard, cookTip)
  self:OnDisable()
  self.m_bShowFire = nil
  self.m_nRemainTime = nil
  self.m_noRecipeCanvas.alpha = 1
  self.m_eCookStartStatus = cookTip and cookTip.cookStartStatus or ECookStartStatus.Valid
  self.m_itemCook = itemCook
  local state = itemCook:GetState()
  local recipe = itemCook:GetRecipe()
  local recipeSpriteName = not StringUtil.IsNilOrEmpty(recipe) and GM.ItemDataModel:GetSpriteName(recipe) or nil
  if state == EItemCookState.Prepare or state == EItemCookState.CanCook then
    self.m_prepareRoot:SetActive(true)
    self.m_tipRecipeGo:SetActive(false)
    local materials = itemCook:GetCurMaterialsArray()
    self.m_oneMaterial:SetActive(false)
    self.m_showMaterials = self.m_showMaterials or {}
    for index, materialCode in ipairs(materials) do
      self:_TryAddMaterial(index, materialCode, inBoard, ECookMaterialUIType.Normal)
    end
    for i = #materials + 1, #self.m_showMaterials do
      self.m_showMaterials[i].gameObject:SetActive(false)
    end
    local fadeInDur = 0.3
    local fadeOutDur = 0.2
    local fadeOutDelay = 2.8
    if cookTip and cookTip.canCookDishArray then
      recipe = nil
      self.m_tipIndex = 1
      self.m_promptItemViews = {}
      local moved = false
      local seq = DOTween.Sequence():SetLoops(-1)
      seq:AppendCallback(function()
        for _, itemView in pairs(self.m_promptItemViews) do
          itemView:StopPromptAnimation()
        end
        self.m_promptItemViews = {}
      end)
      seq:AppendInterval(1)
      seq:AppendCallback(function()
        for _, itemView in pairs(self.m_promptItemViews) do
          itemView:StopPromptAnimation()
        end
        self.m_promptItemViews = {}
        local innerSeq = DOTween.Sequence()
        self.m_innerTween = innerSeq
        local curTipDish = cookTip.canCookDishArray[self.m_tipIndex]
        local curDishInfo = cookTip.canCookDishInfo[curTipDish]
        local greenMat = curDishInfo.greenMat
        local _, lackMat = self.m_itemCook:CanCook(curTipDish)
        local index = #materials
        for _, materialCode in ipairs(materials) do
          Table.ListRemove(greenMat, materialCode)
        end
        for i = 1, #greenMat do
          index = index + 1
          self:_TryAddMaterial(index, greenMat[i], inBoard, ECookMaterialUIType.GreenTip)
          Table.ListRemove(lackMat, greenMat[i])
          local canvas = self.m_showMaterials[index].canvas
          canvas.alpha = 0
          innerSeq:Insert(0, canvas:DOFade(1, fadeInDur))
          innerSeq:Insert(fadeOutDelay, canvas:DOFade(0, fadeOutDur))
          local itemView = curDishInfo.greenItemView[greenMat[i]]
          if itemView then
            itemView:PlayPromptAnimation(cookTip.promptTargetPosition, cookTip.promptTargetItem)
            table.insert(self.m_promptItemViews, itemView)
          end
        end
        for i = 1, #lackMat do
          index = index + 1
          self:_TryAddMaterial(index, lackMat[i], inBoard, ECookMaterialUIType.TransTip)
          local canvas = self.m_showMaterials[index].canvas
          canvas.alpha = 0
          innerSeq:Insert(0, canvas:DOFade(1, fadeInDur))
          innerSeq:Insert(fadeOutDelay, canvas:DOFade(0, fadeOutDur))
        end
        if 3 <= #materials and not moved then
          moved = true
          LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_prepareMatContent)
          local newPosX = self.m_prepareMatViewport.rect.width - self.m_prepareMatContent.rect.width
          innerSeq:Insert(0, self.m_prepareMatContent:DOLocalMoveX(newPosX, 0.2))
        end
        self.m_tipRecipeGo:SetActive(true)
        local spriteName = GM.ItemDataModel:GetSpriteName(curTipDish)
        SpriteUtil.SetImage(self.m_tipRecipeImg, spriteName, true)
        local canvas = self.m_tipRecipeCanvas
        canvas.alpha = 0
        innerSeq:Insert(0, canvas:DOFade(1, fadeInDur))
        innerSeq:Insert(fadeOutDelay, canvas:DOFade(0, fadeOutDur))
        innerSeq:Insert(0, self.m_noRecipeCanvas:DOFade(0, fadeInDur))
        innerSeq:Insert(fadeOutDelay, self.m_noRecipeCanvas:DOFade(1, fadeOutDur))
        innerSeq:OnComplete(function()
          self.m_innerTween = nil
        end)
        self.m_tipIndex = self.m_tipIndex + 1
        if self.m_tipIndex > #cookTip.canCookDishArray then
          self.m_tipIndex = 1
        end
      end)
      seq:AppendInterval(fadeOutDelay + fadeOutDur)
      self.m_loopTween = seq
    end
    self.m_noRecipeRoot:SetActive(StringUtil.IsNilOrEmpty(recipe))
    self.m_recipeGrayGo:SetActive(inBoard == false)
    self.m_recipeButton.enabled = inBoard ~= false
    self.m_recipeRoot:SetActive(not StringUtil.IsNilOrEmpty(recipe))
    if not StringUtil.IsNilOrEmpty(recipe) then
      SpriteUtil.SetImage(self.m_recipeImgToCook, recipeSpriteName, true)
    end
    showCookStartTip = showCookStartTip and not GM.ConfigModel:UseBoardCookBubble()
    self.m_cookStartTip:SetActive(showCookStartTip)
    self.m_cookStartAnimator.enabled = showCookStartTip
    if not showCookStartTip then
      self.m_recipeRoot.transform:SetLocalScaleXY(1)
    end
  else
    self.m_prepareRoot:SetActive(false)
  end
  if state == EItemCookState.Cooking and inBoard then
    self.m_cookingRoot:SetActive(true)
    self.m_cookingSkipGo:SetActive(GM.ConfigModel:IsServerControlOpen(EGeneralConfType.SkipPropNotEnough))
    if self.m_skipTween == nil then
      local tween = DOTween.Sequence():SetLoops(-1)
      tween:Append(self.m_cookingSkipImg:DOFade(1, 0.2):SetEase(Ease.OutSine))
      tween:AppendInterval(0.6)
      tween:Append(self.m_cookingSkipImg:DOFade(0, 0.5):SetEase(Ease.InSine))
      tween:AppendInterval(0.6)
      self.m_skipTween = tween
    end
    SpriteUtil.SetImage(self.m_recipeImgCooking, recipeSpriteName, true)
    self.m_cookingSlider.value = self.m_itemCook:GetTimerAmount()
    self:UpdatePerSecond()
  else
    self.m_cookingRoot:SetActive(false)
  end
  if state == EItemCookState.Cooked or state == EItemCookState.Cooking and not inBoard then
    self.m_cookedRoot:SetActive(true)
    SpriteUtil.SetImage(self.m_recipeImgCooked, recipeSpriteName, true)
    if inBoard then
      self.m_cookedDesc.text = GM.GameTextModel:GetText("hint_desc_equipment_finish")
    elseif state == EItemCookState.Cooked then
      self.m_cookedDesc.text = GM.GameTextModel:GetText("hint_desc_equipment_finish_inventory")
    else
      self.m_cookedDesc.text = GM.GameTextModel:GetText("hint_desc_equipment_doing_inventory")
    end
  else
    self.m_cookedRoot:SetActive(false)
  end
  self:_OnShowItemTestInfoChanged()
end

function BoardInfoCookContent:_TryAddMaterial(index, matCode, inBoard, uiType)
  local materialCmp = self.m_showMaterials[index]
  if materialCmp == nil then
    local go = Object.Instantiate(self.m_oneMaterial, self.m_oneMaterial.transform.parent)
    materialCmp = go:GetLuaTable()
    self.m_showMaterials[index] = materialCmp
  end
  materialCmp:Init(self.m_itemCook, matCode, inBoard, uiType)
  materialCmp.gameObject:SetActive(true)
end

function BoardInfoCookContent:_UpdateFlambeFire()
  local state = self.m_itemCook:GetState()
  if state ~= EItemCookState.Cooking then
    return
  end
  local showFire = self.m_itemCook:IsFlambeTime()
  if self.m_bShowFire == showFire then
    return
  end
  self.m_bShowFire = showFire
  self.m_normalClockGo:SetActive(not showFire)
  self.m_fireClockGo:SetActive(showFire)
  if showFire then
    local isLink = GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link
    self.m_modeFireGo:SetActive(not isLink)
    self.m_linkFireGo:SetActive(isLink)
  end
end

function BoardInfoCookContent:Update()
  self:_UpdateFlambeFire()
  local state = self.m_itemCook:GetState()
  if state ~= EItemCookState.Cooking then
    return
  end
  local remainTime = self.m_itemCook:GetRemainCookDuration()
  if not self.m_itemCook:IsFlambeTime() then
    if self.m_numTween ~= nil then
      self.m_cookingRemainTime.text = TimeUtil.ToMSOrHMS(remainTime)
      self.m_numTween:Kill()
      self.m_numTween = nil
    end
    return
  end
  if self.m_nRemainTime == remainTime then
    return
  end
  self.m_nRemainTime = remainTime
  if self.m_numTween ~= nil then
    self.m_numTween:Kill()
    self.m_numTween = nil
  end
  self.m_cookingRemainTime.text = TimeUtil.ToMSOrHMS(remainTime)
  local valueTo = math.max(remainTime - GM.FlambeTimeModel:GetFlambeTimeInstruSpeed() + 1, 0)
  self.m_numTween = DOVirtual.Float(remainTime, valueTo, 1, function(x)
    local value = math.floor(x)
    self.m_cookingRemainTime.text = TimeUtil.ToMSOrHMS(value)
  end):SetEase(Ease.Linear)
end

function BoardInfoCookContent:UpdatePerSecond()
  local state = self.m_itemCook:GetState()
  if state ~= EItemCookState.Cooking then
    return
  end
  local skipProsNumber = GM.PropertyDataManager:GetPropertyNum(EPropertyType.SkipProp)
  local remainTime = self.m_itemCook:GetRemainCookDuration()
  self.m_cookingRemainTime.text = TimeUtil.ToMSOrHMS(remainTime)
  if self.m_cookingSlider.value < self.m_itemCook:GetTimerAmount() then
    self.m_cookingSlider.value = self.m_itemCook:GetTimerAmount()
  end
  self.m_cookingSkipSlider.value = 0 < skipProsNumber and self.m_itemCook:GetSkipTimerAmount(skipProsNumber * SKIPPROP_EFFECT_TIME) or 1
  if self.m_sliderTween ~= nil then
    self.m_sliderTween:Kill()
  end
  local s = DOTween.Sequence()
  s:Append(self.m_cookingSlider:DOValue(self.m_itemCook:GetNextTimerAmount(), 1):SetEase(Ease.Linear))
  if 0 < skipProsNumber then
    s:Join(self.m_cookingSkipSlider:DOValue(self.m_itemCook:GetSkipTimerAmount(skipProsNumber * SKIPPROP_EFFECT_TIME + 1), 1):SetEase(Ease.Linear))
  end
  self.m_sliderTween = s
end

function BoardInfoCookContent:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BoardInfoCookContent:OnDisable()
  if self.m_sliderTween ~= nil then
    self.m_sliderTween:Kill()
    self.m_sliderTween = nil
  end
  if self.m_skipTween ~= nil then
    self.m_skipTween:Kill()
    self.m_skipTween = nil
  end
  if self.m_numTween ~= nil then
    self.m_numTween:Kill()
    self.m_numTween = nil
  end
  if self.m_loopTween ~= nil then
    self.m_loopTween:Kill()
    self.m_loopTween = nil
  end
  if self.m_innerTween ~= nil then
    self.m_innerTween:Kill()
    self.m_innerTween = nil
  end
  if self.m_promptItemViews then
    for _, itemView in pairs(self.m_promptItemViews) do
      itemView:StopPromptAnimation()
    end
    self.m_promptItemViews = nil
  end
  UIUtil.SetAnchoredPosition(self.m_prepareMatContent, 0)
end

function BoardInfoCookContent:OnRecipeClicked()
  if self.m_eCookStartStatus ~= ECookStartStatus.Valid then
    GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "hint_dish_doubleConfirm_title", self.m_eCookStartStatus == ECookStartStatus.NotInOrder and "hint_dish_doubleConfirm_notInOrder" or "hint_dish_doubleConfirm_enoughDish", "btn_common_continue", "common_button_cancel", function(tbWindow)
      tbWindow:Close()
      self.m_itemCook:StartCook()
    end, nil, false)
    return
  end
  self.m_itemCook:StartCook()
end

function BoardInfoCookContent:OnRevertClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.RevertCookConfirmWindow, self.m_itemCook)
end

function BoardInfoCookContent:_OnShowItemTestInfoChanged()
  if not GM.UIManager:CanShowTestUI() then
    self.m_testText.gameObject:SetActive(false)
    return
  end
  local state = self.m_itemCook:GetState()
  self.m_testText.gameObject:SetActive(true)
  self.m_testText.text = state == EItemCookState.Cooked and self.m_itemCook.testInfoCostTime or ""
end

ECookMaterialUIType = {
  Normal = 1,
  GreenTip = 2,
  TransTip = 3
}
BoardInfoCookMaterial = {}
BoardInfoCookMaterial.__index = BoardInfoCookMaterial

function BoardInfoCookMaterial:Init(itemCook, materialCode, inBoard, uiType)
  local spriteName = GM.ItemDataModel:GetSpriteName(materialCode)
  SpriteUtil.SetImage(self.m_itemIcon, spriteName, true)
  UIUtil.SetAlpha(self.m_itemIcon, uiType == ECookMaterialUIType.Normal and 1 or 0.5)
  self.m_itemCook = itemCook
  self.m_materialCode = materialCode
  self.m_normalBgGo:SetActive(uiType == ECookMaterialUIType.Normal)
  self.m_greenBgGo:SetActive(uiType == ECookMaterialUIType.GreenTip)
  self.m_frameGo:SetActive(uiType == ECookMaterialUIType.GreenTip or uiType == ECookMaterialUIType.TransTip)
  self.m_removeGo:SetActive(inBoard and uiType == ECookMaterialUIType.Normal)
  self.m_button.enabled = inBoard and uiType == ECookMaterialUIType.Normal
  self.canvas.alpha = 1
end

function BoardInfoCookMaterial:OnClicked()
  EventDispatcher.DispatchEvent(EEventType.ItemCookMaterialClicked)
  local startWorldPos = self.m_itemIcon.transform.position
  self.m_itemCook:PutBackMaterial(self.m_materialCode, startWorldPos)
end
