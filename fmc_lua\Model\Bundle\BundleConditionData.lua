BundleConditionData = {}
BundleConditionData.__index = BundleConditionData

function BundleConditionData.Create(conditionData)
  local con = setmetatable(conditionData, BundleConditionData)
  con:Init()
  return con
end

function BundleConditionData:Init()
end

function BundleConditionData:IsGroupDataActive()
  if not Table.IsEmpty(self.taskFinished) then
    local chapterId = self.taskFinished[TASK_CHAPTER_ID]
    local taskCount = self.taskFinished[TASK_COUNT]
    if not (chapterId <= GM.TaskManager:GetOngoingChapterId()) or not (taskCount <= GM.TaskManager:GetChapterFinishedCount(chapterId)) then
      return false
    end
  end
  if not Table.IsEmpty(self.activity) then
    if not GM.CheckResourcesStageFinished then
      return false
    end
    local bActive = false
    local activityModel
    for _, activityType in pairs(self.activity) do
      activityModel = GM.ActivityManager:GetModel(activityType)
      if activityModel ~= nil and activityModel:GetState() == ActivityState.Started then
        bActive = true
        break
      end
    end
    if not bActive then
      return false
    end
  end
  return true
end

function BundleConditionData:GetRecentActivityEndTime()
  if not Table.IsEmpty(self.activity) then
    local endTime, activityEndTime, activityModel
    for _, activityType in pairs(self.activity) do
      activityModel = GM.ActivityManager:GetModel(activityType)
      activityEndTime = activityModel and activityModel:GetEndTime()
      if activityModel ~= nil and activityEndTime ~= nil then
        if endTime == nil then
          endTime = activityEndTime
        else
          endTime = math.max(endTime, activityEndTime)
        end
      end
    end
    return endTime
  end
end
