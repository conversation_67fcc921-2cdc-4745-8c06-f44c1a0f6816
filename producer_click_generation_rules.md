# 母棋子点击生产规则详细分析文档

## 概述

母棋子（Producer/Generator）是游戏中的核心生产单位，玩家通过点击母棋子来生产子棋子。系统对母棋子首次获得和后续获得有不同的处理规则，通过特殊配置来优化新手体验和游戏平衡。

## 核心组件架构

### 1. ItemSpread组件

母棋子的生产功能主要由 `ItemSpread` 组件实现：

**代码位置**: `ItemSpread.lua:23-24`
```lua
ItemSpread = setmetatable({}, BaseItemComponent)
ItemSpread.__index = ItemSpread
```

**主要属性**:
- `m_codeWeightPairs`: 生产物品的代码权重对
- `m_weightType`: 权重类型（Fixed/Decremental）
- `m_storageRestNumber`: 剩余库存数量
- `m_storageMaxNumber`: 最大库存容量
- `m_bCanInherit`: 是否可继承特殊配置

### 2. PDItemSPModel特殊处理模型

**代码位置**: `PDItemSPModel.lua:1-2`
```lua
PDItemSPModel = {}
PDItemSPModel.__index = PDItemSPModel
```

该模型专门处理母棋子首次获得时的特殊配置。

## 母棋子首次获得特殊处理

### 1. 特殊处理触发机制

**代码位置**: `ItemSpread.lua:89-99`
```lua
function ItemSpread:_PDSpecial()
  local tapeItems, initialNumber = GM.PDItemSPModel:OnPDCreated(self.m_itemConfig.Type)
  if tapeItems then
    self.m_codeWeightPairs = Table.DeepCopy(tapeItems)
    self.m_weightType = ItemSpreadWeightType.Fixed
    self.m_bCanInherit = true
  end
  if initialNumber then
    self.m_storageRestNumber = initialNumber
  end
end
```

**触发条件**:
- 母棋子创建时调用 `needUnlockType` 参数为 true
- 在 `ItemSpread:Init()` 中第66行调用 `self:_PDSpecial()`

### 2. PDItemSPModel核心逻辑

**代码位置**: `PDItemSPModel.lua:47-69`
```lua
function PDItemSPModel:OnPDCreated(pdCode)
  if not self.m_mapCreatedNumber or not self.m_mapConfigs then
    Log.Error("PDItemSPModel 执行时序出问题，初始化完成前创建棋子！")
    return
  end
  if not self.m_mapConfigs[pdCode] then
    Log.Info("母棋子特殊处理：无该母棋子配置，无需记录。" .. pdCode)
    return
  end
  local curNumer = self.m_mapCreatedNumber[pdCode] or 0
  local maxNumber = self.m_mapMaxIndex[pdCode] or 0
  if curNumer >= maxNumber then
    Log.Info("母棋子特殊处理：个数已超配置最大index，无需记录。" .. pdCode)
    return
  end
  local newNumber = curNumer + 1
  self.m_mapCreatedNumber[pdCode] = newNumber
  self:_Save()
  local config = self.m_mapConfigs[pdCode][newNumber]
  if config then
    Log.Info("母棋子特殊处理：第" .. newNumber .. "个，替换配置。" .. pdCode)
    return config.StartTapeItems, config.InitialNumber
  end
end
```

**处理逻辑**:
1. **计数管理**: 记录每种母棋子的获得次数
2. **配置查找**: 根据获得次数查找对应的特殊配置
3. **返回配置**: 返回特殊的生产序列和初始库存数量

### 3. 特殊配置示例

**代码位置**: `PDItemSPConfig.lua:2-28`
```lua
-- pd_1_4 第1次获得的配置
{
  PD = "pd_1_4",
  Index = 1,
  StartTapeItems = {
    {Code = "it_1_1_1", Weight = 8}  -- 生产8个 it_1_1_1
  },
  InitialNumber = 8  -- 初始库存8个
},
-- pd_1_5 第1次获得的配置
{
  PD = "pd_1_5", 
  Index = 1,
  StartTapeItems = {
    {Code = "it_1_1_2", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_2_1", Weight = 1},
    {Code = "it_1_1_1", Weight = 2},
    {Code = "it_1_1_1", Weight = 3},
    -- ... 更多复杂的生产序列
  },
  InitialNumber = 19  -- 初始库存19个
}
```

**配置特点**:
- **Index**: 表示第几次获得（1=首次，2=第二次，等等）
- **StartTapeItems**: 特殊的生产序列，按顺序固定生产
- **InitialNumber**: 特殊的初始库存数量

## 点击生产核心机制

### 1. OnTap主函数

**代码位置**: `ItemSpread.lua:260-283`
```lua
function ItemSpread:OnTap()
  if self.m_state == ItemSpreadState.OpenFinish then
    return
  end
  local spreadCount = 1
  if GameConfig.IsTestMode() and PlayerPrefs.GetInt(EPlayerPrefKey.TestSuperTap, 0) == 1 then
    spreadCount = 10  -- 测试模式下可连续生产10个
  end
  for i = 1, spreadCount do
    local canContinue = self:_TrySpread()
    if not canContinue then
      break
    end
  end
  -- 触发通知和礼包检查
  if not self:IsDisposable() and self:ShowCountDown() and self:GetStorageRestNumber() == 0 then
    if GM.TutorialModel:IsTutorialFinished(ETutorialId.PDCD1) and 
       GM.TutorialModel:IsTutorialFinished(ETutorialId.PDCD2) then
      local restDuration = self:GetTimerDuration() + self:GetStartTimer() - GM.GameModel:GetServerTime()
      if 1200 <= restDuration then  -- 剩余冷却时间超过20分钟
        GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.ItemCoolDown)
      end
    end
    GM.BundleManager:OnItemRecharge(self.m_itemModel)  -- 触发礼包检查
  end
end
```

### 2. 生产条件检查

**代码位置**: `ItemSpread.lua:689-739`
```lua
function ItemSpread:_CanSpread(isAuto)
  -- 状态检查
  if self.m_state == ItemSpreadState.Closed then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.ItemClosed)
    return false
  end
  if self.m_state == ItemSpreadState.Opening then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.ItemOpening)
    return false
  end
  if self.m_state == ItemSpreadState.Initializing or self:GetStorageRestNumber() == 0 then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.ItemRecharging)
    return false
  end
  
  -- 位置检查
  local position
  local replace = false
  if self:IsDisposable() and self.m_spreadCount >= self.m_dropsTotal - 1 and 
     self.m_itemModel:GetComponent(ItemTransform) == nil and 
     self.m_itemConfig.DropOnSpot == 1 then
    position = self.m_itemModel:GetPosition()  -- 一次性物品在原地替换
    replace = true
  elseif isAuto then
    position = self:_GetAutoSpreadPosition(boardModel)
  else
    position = boardModel:FindEmptyPositionInSpreadOrder(self.m_itemModel:GetPosition())
  end
  
  if position == nil then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.BoardFull)
    return false
  end
  
  -- 体力消耗检查
  local energyBoost = false
  local costNum = 0
  if not isAuto and self:CostEnergy() then
    costNum, energyBoost = ItemSpread.GetCostEnergyNum(self.m_itemModel:GetType())
    -- 体力不足检查...
  end
  
  return true, position, replace, energyBoost, costNum
end
```

**检查项目**:
1. **状态检查**: 母棋子必须处于可生产状态
2. **库存检查**: 必须有剩余库存
3. **位置检查**: 棋盘上必须有空位放置新棋子
4. **体力检查**: 如果需要消耗体力，检查体力是否充足

### 3. 物品代码生成

**代码位置**: `ItemSpread.lua:586-655`
```lua
function ItemSpread:_GenerateItemCode(energyBoost)
  local code = GM.TutorialModel:GetForceSpreadItemCode()
  if code ~= nil then
    return code  -- 教程强制指定的物品
  end
  
  if self.m_codeNextTime then
    local code = self.m_codeNextTime
    self.m_codeNextTime = nil
    self:_LogNextCode()
    return code  -- 双倍体力优化的必喷物品
  end
  
  if self.m_weightType == ItemSpreadWeightType.Fixed then
    -- 固定序列模式（首次获得的特殊配置）
    local firstPair = self.m_codeWeightPairs[1]
    code = firstPair.Code
    firstPair.Weight = firstPair.Weight - 1
    if firstPair.Weight == 0 then
      table.remove(self.m_codeWeightPairs, 1)  -- 当前物品生产完毕，移除
      if #self.m_codeWeightPairs == 0 then
        self:_GenNextSpreadItems()  -- 切换到正常生产模式
      end
    end
    self:_Save()
    return self:_GetUpgradedItemCode(code, energyBoost)
  end
  
  -- 正常随机权重模式
  code = Table.ListWeightSelectOne(self.m_codeWeightPairs).Code
  -- 权重递减处理...
  return self:_GetUpgradedItemCode(code, energyBoost)
end
```

**生成逻辑**:
1. **教程优先**: 教程期间强制生产指定物品
2. **必喷机制**: 双倍体力优化的必喷物品
3. **固定序列**: 首次获得时按特殊配置的固定序列生产
4. **随机权重**: 正常情况下按权重随机生产

## 首次获得与后续获得的区别

### 1. 生产序列差异

**首次获得**:
- 使用 `PDItemSPConfig.lua` 中配置的 `StartTapeItems`
- 按固定顺序生产，每个物品有确定的数量
- 权重类型为 `ItemSpreadWeightType.Fixed`

**后续获得**:
- 使用 `ItemModelConfig.lua` 中的 `GeneratedItems` 或 `TapeItems`
- 按权重随机生产或固定序列
- 权重类型可能为 `ItemSpreadWeightType.Decremental`

### 2. 初始库存差异

**首次获得**:
- 使用特殊配置的 `InitialNumber`
- 通常比正常配置更多，提供更好的新手体验

**后续获得**:
- 使用正常配置的 `InitialNumber`
- 按标准游戏平衡设计

### 3. 配置继承机制

**代码位置**: `ItemSpread.lua:92-94`
```lua
if tapeItems then
  self.m_codeWeightPairs = Table.DeepCopy(tapeItems)
  self.m_weightType = ItemSpreadWeightType.Fixed
  self.m_bCanInherit = true  -- 标记可继承特殊配置
end
```

**继承特点**:
- 首次获得的母棋子设置 `m_bCanInherit = true`
- 特殊配置用完后会切换到正常配置
- 保证新手体验的同时不影响长期游戏平衡

## 数据持久化

### 1. 获得次数记录

**代码位置**: `PDItemSPModel.lua:36-45`
```lua
function PDItemSPModel:_Save()
  local cacheStr = ""
  for pd, number in pairs(self.m_mapCreatedNumber) do
    cacheStr = cacheStr .. pd .. "-" .. number .. ";"
  end
  if 0 < #cacheStr then
    cacheStr = string.sub(cacheStr, 1, #cacheStr - 1)
  end
  GM.MiscModel:Set(EMiscKey.PDItemSPIndex, cacheStr)
end
```

**存储格式**: `"pd_1_4-2;pd_1_5-1;pd_2_4-3"`
- 记录每种母棋子的获得次数
- 用于判断应该使用哪个Index的特殊配置

### 2. 生产状态保存

**代码位置**: `ItemSpread.lua:134-148`
```lua
function ItemSpread:ToSerialization(dbTable)
  dbTable.spreadState = self.m_state
  dbTable.spreadStartTimer = self.m_startTimer
  dbTable.spreadStorageRestNumber = self.m_storageRestNumber
  dbTable.spreadCount = self.m_spreadCount
  dbTable.spreadCodeWeightPairs = ItemUtility.CodeWeightPairsToString(self.m_codeWeightPairs)
  dbTable.spreadWeightType = self.m_weightType
  dbTable.spreadAddItem = self.m_spreadAddItem
  dbTable.spreadInherit = self.m_bCanInherit and 1 or 0
  dbTable.spreadItemBoxChain = table.concat(self.m_spreadItemBoxChain, ";")
end
```

**保存内容**:
- 当前生产状态和剩余库存
- 当前的生产序列配置
- 是否为特殊配置的继承状态

## 教程集成

### 1. 点击母棋子教程

**代码位置**: `TutorialExecuterClickPD.lua:19-32`
```lua
function Executer:TryStartTutorial()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return false
  end
  local itemSpread = self.m_targetItem:GetComponent(ItemSpread)
  if itemSpread:GetStorageRestNumber() > 0 then
    local boardView = MainBoardView.GetInstance()
    if boardView ~= nil then
      boardView:UpdateSelectedItem(self.m_targetItem)
    end
    self:_ExecuteStep1(self.m_targetItem)
    return true
  end
end
```

**教程特点**:
- 确保母棋子有库存时才触发教程
- 自动选中目标母棋子
- 引导玩家点击生产

### 2. Transform教程

**代码位置**: `TutorialExecuterClickPD.lua:2-3`
```lua
local textKey = "tutorial_click_transform_1"
local itemId = "it_1_1_6"
```

专门针对Transform类型母棋子的点击教程，教授玩家如何使用会变形的特殊母棋子。

## 高级生产机制

### 1. 双倍体力优化

**代码位置**: `ItemSpread.lua:627-655`
```lua
function ItemSpread:_GetUpgradedItemCode(code, energyBoost)
  if not energyBoost then
    return ItemSpreadHelper.ReplaceSpreadItem(self, code)
  end
  if not GM.EnergyBoostModel:CanEnergyBoost(self.m_itemModel:GetCode()) then
    return code
  end

  local itemDataModel = GM.ItemDataModel
  local boardModel = self.m_itemModel:GetBoardModel()
  local itemConfig = itemDataModel:IsItemExist(code) and itemDataModel:GetModelConfig(code)
  if not itemConfig or not itemConfig.MergedType then
    return code
  end

  -- 检查订单需求
  local directResult, indirectResult, filledResult, codeCountMap =
    boardModel:GetUnfilledOrderRequirementsSeparately(false)
  local lackCount = (directResult[code] or 0) + (indirectResult[code] or 0)

  if 0 < lackCount then
    self.m_codeNextTime = 1 < lackCount and code or nil  -- 设置必喷机制
    if self.m_codeNextTime then
      self:_LogNextCode()
    end
    return code
  end

  -- 特殊情况：场上只有一个该物品
  if itemDataModel:GetChainLevel(code) == 1 and
     boardModel:GetBoardItemCountByType(code) == 1 and
     (0 < lackCount or (filledResult[code] or 0) <= 0) then
    return code
  end

  local doubleCode = itemConfig.MergedType
  return doubleCode, code, 1  -- 返回升级后的物品
end
```

**优化机制**:
- **需求检测**: 检查当前订单是否需要该物品
- **必喷机制**: 如果需要多个，设置下次必定生产同样物品
- **智能升级**: 如果不需要基础物品，直接生产合成后的高级物品
- **特殊保护**: 场上只有一个时不升级，避免断链

### 2. 自动生产机制

**代码位置**: `ItemSpread.lua:150-159`
```lua
function ItemSpread:Update()
  if self:NeedUpdate() then
    while self:_TrySpread(true) do  -- isAuto = true
    end
  end
end

function ItemSpread:NeedUpdate()
  return self:IsAutoSpread()
end
```

**自动生产特点**:
- 某些母棋子支持自动生产
- 不消耗体力
- 自动寻找合适的生产位置
- 持续生产直到库存耗尽或棋盘满

### 3. Flambe Time特殊机制

**代码位置**: `ItemSpread.lua:161-176`
```lua
function ItemSpread:IsFlambeTime()
  return self.m_state == ItemSpreadState.Opened and
         GM.FlambeTimeModel:IsFlambeTimeSpreader(self.m_itemModel:GetCode())
end

function ItemSpread:_OnFlambeTimeChanged()
  self:UpdatePerSecond()
  self.event:Call(ItemSpreadEventType.UpdateFire)
end
```

**Flambe Time效果**:
- 特定时间段内某些母棋子会有火焰特效
- 可能影响生产速度或生产内容
- 增加游戏的动态性和视觉效果

## 生产失败处理

### 1. 失败原因枚举

**代码位置**: `ItemSpread.lua:1-6`
```lua
SpreadFailedReason = {
  ItemClosed = 1,      -- 物品未开启
  ItemOpening = 2,     -- 物品开启中
  ItemRecharging = 3,  -- 物品充能中
  BoardFull = 4        -- 棋盘已满
}
```

### 2. 失败处理机制

**代码位置**: `ItemSpread.lua:689-725`
```lua
function ItemSpread:_CanSpread(isAuto)
  if self.m_state == ItemSpreadState.Closed then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.ItemClosed)
    return false
  end
  if self.m_state == ItemSpreadState.Opening then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.ItemOpening)
    return false
  end
  if self.m_state == ItemSpreadState.Initializing or self:GetStorageRestNumber() == 0 then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.ItemRecharging)
    return false
  end

  -- 位置检查
  if position == nil then
    self:_NotifySpreadFailed(isAuto, SpreadFailedReason.BoardFull)
    if not isAuto then
      PlatformInterface.Vibrate(EVibrationType.Heavy)  -- 手动点击时震动反馈
    end
    return false
  end
end
```

**失败反馈**:
- 不同失败原因有不同的提示
- 手动点击失败时提供震动反馈
- 自动生产失败时静默处理

## 特殊物品处理

### 1. 一次性物品

**代码位置**: `ItemSpread.lua:711-713`
```lua
if self:IsDisposable() and self.m_spreadCount >= self.m_dropsTotal - 1 and
   self.m_itemModel:GetComponent(ItemTransform) == nil and
   self.m_itemConfig.DropOnSpot == 1 then
  position = self.m_itemModel:GetPosition()
  replace = true
end
```

**处理特点**:
- 最后一次生产时在原地替换
- 不需要寻找新位置
- 适用于有限次数的特殊母棋子

### 2. Transform物品

**代码位置**: `ItemSpread.lua:788-796`
```lua
if self:_ShouldDispose() then
  if self.m_itemModel:GetComponent(ItemTransform) ~= nil then
    self.m_itemModel:GetComponent(ItemTransform):Transform(self:_GetNewItemCost(0), true)
  elseif not replace then
    boardModel:RemoveItem(self.m_itemModel)
    boardModel.event:Call(BoardEventType.CollapseItem, {
      Source = self.m_itemModel
    })
  end
  return false
end
```

**Transform特点**:
- 生产完毕后会变形成其他物品
- 不是简单的移除，而是转换
- 保持游戏的连续性

## 用户体验优化

### 1. 视觉反馈

**代码位置**: `ItemSpread.lua:787`
```lua
PlatformInterface.Vibrate(EVibrationType.Light)  -- 成功生产时的轻微震动
```

**反馈机制**:
- 成功生产：轻微震动
- 生产失败：重度震动
- 音效配合：不同类型的生产音效

### 2. 动画效果

**代码位置**: `BaseInteractiveBoardView.lua:319`
```lua
self:_PlayJumpAnimation(newItemView, sourceItemView.transform.localPosition,
                       newItemView.transform.localPosition, nil,
                       message.BoostLevelSpan, message.BoostEnergySpared)
```

**动画特点**:
- 新物品从母棋子位置跳跃到目标位置
- 支持升级动画（双倍体力时）
- 视觉上清晰表达生产关系

### 3. 状态提示

**代码位置**: `ItemSpreadView.lua:206-217`
```lua
function ItemSpreadView:_OnRechargeFinish()
  if self.m_hintEffectGo ~= nil then
    self.m_hintEffectGo:SetActive(false)
  end
  Object.Instantiate(self.m_rechargeFinishEffectPrefab, self.transform)
  DOVirtual.DelayedCall(0.2, function()
    self.m_itemView:PlayTapAnimation(0.2)
    self:_UpdateHintEffect()
  end)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxMergeCd)
end
```

**提示机制**:
- 充能完成时的特效提示
- 可点击状态的视觉提示
- 音效配合增强反馈

## 总结

### 核心设计理念

1. **新手友好**
   - 首次获得母棋子有特殊的生产序列
   - 更多的初始库存
   - 固定的生产顺序，减少随机性

2. **渐进式复杂度**
   - 从固定序列过渡到随机权重
   - 从简单生产到复杂的双倍体力优化
   - 逐步引入高级机制

3. **智能优化**
   - 根据订单需求智能调整生产内容
   - 必喷机制确保关键物品的供应
   - 双倍体力时自动升级物品

4. **用户体验**
   - 丰富的视觉和触觉反馈
   - 清晰的状态提示
   - 流畅的动画效果

该系统通过精心设计的首次获得特殊处理和渐进式的复杂度提升，既保证了新手玩家的良好体验，又为高级玩家提供了深度的策略空间。
