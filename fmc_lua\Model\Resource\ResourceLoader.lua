FileType = {
  Text = 1,
  Image = 2,
  Audio = 3
}
ResourceLoader = {}
ResourceLoader.__index = ResourceLoader

function ResourceLoader:Init()
  self:ResetLoadCount()
  self.m_bLogLoadRequest = false
end

function ResourceLoader:OnOneLoadStart(key)
  self:_LogLoadResource(key)
  self._toLoadCount = self._toLoadCount + 1
  self.m_arrLoadingRes[#self.m_arrLoadingRes + 1] = key
end

function ResourceLoader:OnOneLoadFinish(key)
  Table.ListRemove(self.m_arrLoadingRes, key)
  self._loadedCount = self._loadedCount + 1
  if self._loadedCount > self._toLoadCount then
    self._loadedCount = self._toLoadCount
    self.m_arrLoadingRes = {}
    if GameConfig.IsTestMode() then
      Log.Error("ResourceLoader load count error " .. tostring(key))
    end
  end
end

function ResourceLoader:GetRemainResource()
  return table.concat(self.m_arrLoadingRes, ",")
end

function ResourceLoader:IsAllResourceLoaded()
  return self._loadedCount >= self._toLoadCount and (not GM.ModeViewController or not GM.ModeViewController.isLoading) and not LuaManager:IsLoadingDynamicConfig()
end

function ResourceLoader:ResetLoadCount()
  if self.m_arrLoadingRes and GameConfig.IsTestMode() then
    for _, fileKey in ipairs(self.m_arrLoadingRes) do
      Log.Error(fileKey .. " is still loading!")
    end
  end
  self._toLoadCount = 0
  self._loadedCount = 0
  self.m_arrLoadingRes = {}
end

function ResourceLoader:GetLoadingPercent()
  if self._toLoadCount == 0 then
    return 0
  end
  return self._loadedCount / self._toLoadCount
end

function ResourceLoader:LoadPrefab(resConfig, parent, position, callback)
  if not resConfig then
    Log.Error("ResourceLoader:LoadPrefab resConfig is nil")
    return
  end
  if parent == nil then
    parent = Root
  end
  callback = self:_WrapCallback(callback)
  local originTraceback = ""
  local callbackCalled = false
  local assetRef = resConfig.assetReference
  self:OnOneLoadStart(resConfig.fileKey)
  GameObjectPool:LoadPrefab(resConfig, parent, position, function(go)
    callbackCalled = true
    if parent == nil or not parent:IsNull() and parent.gameObject and not parent.gameObject:IsNull() then
      if go and not go:IsNull() then
        go:SetActive(true)
        if callback then
          SafeCall(callback, nil, go)
        end
      else
        local info = not go and "not go" or "go is null"
        GM.BIManager:LogErrorInfo(EBIType.LoadPrefabError, resConfig.fileKey .. " response error " .. info)
      end
    end
    self:OnOneLoadFinish(resConfig.fileKey)
  end)
  if not callbackCalled then
    originTraceback = "origin " .. debug.traceback()
  end
end

function ResourceLoader:LoadScene(name, mode, callback)
  local resConfig = GM.DataResource.GameSceneConfig:GetConfig(name)
  if not resConfig then
    Log.Error("no resConfig for " .. name)
    return
  end
  callback = self:_WrapCallback(callback)
  local locked = false
  if GM.UIManager then
    GM.UIManager:SetEventLock(true)
    locked = true
  end
  local startTime = DeviceInfo.GetCpuTime()
  Scheduler.Schedule(function()
    if not self:IsAllResourceLoaded() and DeviceInfo.GetCpuTime() - startTime < 2 then
      return
    end
    Scheduler.UnscheduleTarget(self)
    if locked and GM.UIManager then
      GM.UIManager:SetEventLock(false)
    end
    self:OnOneLoadStart(name)
    self.m_bLogLoadRequest = true
    AddressableLoader.LoadScene(resConfig.assetReference, mode, function(o)
      if GM == nil then
        return
      end
      self.m_bLogLoadRequest = false
      if callback then
        callback(o)
      end
      self:OnOneLoadFinish(name)
    end)
  end, self)
end

function ResourceLoader:UnloadScene(name, callback)
  callback = self:_WrapCallback(callback)
  AddressableLoader.UnloadScene(name, callback)
end

function ResourceLoader:LoadLatestFile(resConfig, callback, allowFail)
  if not callback or not resConfig then
    Log.Error("ResourceLoader:LoadLatestFile " .. (not callback and "callback " or " ") .. (not resConfig and "resConfig " or " ") .. "is nil. \n" .. debug.traceback())
    return
  end
  callback = self:_WrapCallback(callback)
  local fileKey = resConfig.fileKey
  if GM.CDNResourceManager.IsCacheFileExist(fileKey) and GM.CDNResourceManager:IsFileVersionValid(fileKey) then
    local data = self:LoadCDNFile(fileKey)
    if not data then
      self:_LoadBundleFile(fileKey, resConfig.assetReference, callback, allowFail)
    else
      callback(data, fileKey)
    end
  else
    self:_LoadBundleFile(fileKey, resConfig.assetReference, callback, allowFail)
  end
end

function ResourceLoader:LoadCDNFile(fileKey)
  local fullPath = CDNResourceManager.GetCdnPath(fileKey)
  return self:LoadCDNFileByFullPath(fullPath)
end

function ResourceLoader:LoadCDNFileByFullPath(fullPath)
  if not File.Exists(fullPath) then
    return
  end
  local fileType = self._GetFileType(fullPath)
  if fileType == FileType.Text then
    return CDNLoader.LoadText(fullPath)
  else
    Log.Warning("load cache file type not supported. fileKey: " .. fullPath)
  end
end

function ResourceLoader:_LoadBundleFile(fileKey, assetReference, callback, allowFail)
  if not callback then
    Log.Error("ResourceLoader:LoadBundleFile callback is nil")
    return
  end
  callback = self:_WrapCallback(callback)
  if not fileKey and not assetReference then
    Log.Error("ResourceLoader:LoadBundleFile fileKey and assetReference are nil")
    return
  end
  local fileType = self._GetFileType(fileKey)
  if fileType == FileType.Text then
    self:OnOneLoadStart(fileKey)
    AddressableLoader.LoadText(fileKey, assetReference, function(o)
      if GM == nil then
        return
      end
      if callback then
        callback(o, fileKey)
      end
      self:OnOneLoadFinish(fileKey)
    end)
  elseif fileType == FileType.Image then
    self:OnOneLoadStart(fileKey)
    AddressableLoader.LoadSprite(fileKey, assetReference, function(o)
      if GM == nil then
        return
      end
      if o and not o:IsNull() then
        if callback then
          callback(o, fileKey)
        end
      elseif not allowFail then
        GM.BIManager:LogErrorInfo(EBIType.LoadSpriteError, fileKey .. " response error")
      end
      self:OnOneLoadFinish(fileKey)
    end)
  elseif fileType == FileType.Audio then
    self:OnOneLoadStart(fileKey)
    AddressableLoader.LoadAudioClip(fileKey, assetReference, function(o)
      if GM == nil then
        return
      end
      if o and not o:IsNull() and callback then
        callback(o, fileKey)
      end
      self:OnOneLoadFinish(fileKey)
    end)
  else
    Log.Error("load bundle file type not supported. fileKey: " .. fileKey)
    callback(nil, fileKey)
  end
end

function ResourceLoader._GetFileType(filePath)
  local fileExtension = string.lower(PathHelper.GetExtension(filePath))
  if fileExtension == ".json" or fileExtension == ".txt" or fileExtension == ".bytes" then
    return FileType.Text
  elseif fileExtension == ".jpg" or fileExtension == ".png" then
    return FileType.Image
  elseif fileExtension == ".ogg" or fileExtension == ".mp3" or fileExtension == ".wav" then
    return FileType.Audio
  end
  return nil
end

function ResourceLoader:_LogLoadResource(fileKey)
  if not self.m_bLogLoadRequest then
    return
  end
  local logResName = fileKey or "nil"
  CSFirebaseManager:CrashlyticsLog("Load res " .. logResName)
  Log.Warning("Should not load res when scene loading " .. logResName)
end

function ResourceLoader:_WrapCallback(callback)
  return function(...)
    if not GM or GM.destroying then
      return
    end
    if callback then
      callback(...)
    end
  end
end

function ResourceLoader.ClearUnusedAssets(bUnloadUnusedAssets)
  bUnloadUnusedAssets = bUnloadUnusedAssets or false
  GameObjectPool:Clear()
  DelayExecuteFunc(function()
    AddressableLoader.ClearUnusedAssets(bUnloadUnusedAssets)
  end, 0.02)
end
