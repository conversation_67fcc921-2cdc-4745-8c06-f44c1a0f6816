RewardTip = {}
RewardTip.__index = RewardTip

function RewardTip:Show(rewards, rectTrans, offsetX, offsetY, bAutoDir)
  self.gameObject:SetActive(true)
  if bAutoDir then
    self.m_rewardContent:ClearRewards()
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_boardRectTrans)
    local bLeft = rectTrans.position.x <= 0
    UIUtil.SetPivot(self.m_boardRectTrans, Vector2(bLeft and 0.2 or 0.8, 0))
  end
  self.m_rewardContent:Init(rewards)
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_boardRectTrans)
  self.m_rectTrans = rectTrans
  self.m_offsetX = offsetX or -65
  self.m_offsetY = offsetY or 0
  self:UpdatePos()
  self.gameObject.transform:DOKill()
  self.gameObject.transform.localScale = V3Zero
  self.gameObject.transform:DOScale(1, 0.3):SetEase(Ease.OutCubic)
  self.showing = true
end

function RewardTip:Hide(ignoreAni)
  self.showing = false
  self.gameObject.transform:DOKill()
  if ignoreAni then
    self.gameObject.transform:SetLocalScale(0)
    return
  end
  self.gameObject.transform:DOScale(0, 0.3):SetEase(Ease.OutCubic)
end

function RewardTip:UpdatePos()
  if self.m_rectTrans ~= nil then
    self.gameObject.transform.position = self.m_rectTrans.position + Vector3(self.m_offsetX, self.m_offsetY, 0)
  end
end

function RewardTip:GetRewardItem(index)
  return self.m_rewardContent:GetRewardItem(index)
end
