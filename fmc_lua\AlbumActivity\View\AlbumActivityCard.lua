AlbumActivityOneCard = {}
AlbumActivityOneCard.__index = AlbumActivityOneCard

function AlbumActivityOneCard:Init(cardId, model, bRed, bShowNum, num, forceDisplayFront, bPlayAnim, delay)
  self.m_cardId = cardId
  self.m_model = model
  self.m_bshowNum = bShowNum
  self.m_num = num
  self.m_bRed = bRed
  self.m_forceDisplayFront = forceDisplayFront
  self.m_bPlayAnim = bPlayAnim
  self.m_delayTime = delay
  self.m_AlbumOneCardBack:Init(self.m_cardId, self.m_model)
  self.m_AlbumOneCardFront:Init(self.m_cardId, self.m_model)
  if self.m_stars == nil then
    self.m_stars = {}
    self.m_stars[1] = self.m_star1
  end
  self:UpdateContent()
end

function AlbumActivityOneCard:UpdateContent()
  local curCardNum = self.m_model:GetCardCount(self.m_cardId)
  local displayFront = self.m_forceDisplayFront or curCardNum ~= 0
  UIUtil.SetActive(self.m_AlbumOneCardBack.gameObject, not displayFront)
  UIUtil.SetActive(self.m_AlbumOneCardFront.gameObject, displayFront)
  self:UpdateStar(displayFront)
  self.m_AlbumOneCardBack:UpdateContent()
  self.m_AlbumOneCardFront:UpdateContent(self.m_bshowNum, self.m_num)
  self:UpdateNewState(self.m_bRed, self.m_bPlayAnim, self.m_delayTime)
end

function AlbumActivityOneCard:UpdateStar(open)
  local cardInfo = self.m_model:GetCardInfo(self.m_cardId)
  local i32Star = self.m_model:GetShowStar(cardInfo.star)
  for _, starLua in pairs(self.m_stars) do
    UIUtil.SetActive(starLua.gameObject, false)
  end
  local isGoldCard = self.m_model:IsGoldCard(self.m_cardId)
  for star = 1, i32Star do
    if self.m_stars[star] == nil then
      self.m_stars[star] = Object.Instantiate(self.m_star1.gameObject, self.m_star1.transform.parent):GetComponent(typeof(Image))
    end
    self.m_stars[star].sprite = open and self.m_starLightTexture or isGoldCard and self.m_starGoldTexture or self.m_starDarkTexture
    UIUtil.SetActive(self.m_stars[star].gameObject, true)
  end
end

function AlbumActivityOneCard:UpdateNewState(state, bPlayAnim, delayTime)
  UIUtil.SetActive(self.m_newGo, state)
  if state and bPlayAnim then
    DelayExecuteFuncInView(function()
      self.m_newAnimator:SetTrigger("play")
      UIUtil.SetActive(self.m_lightGo, true)
      self.m_lightAnimator:SetTrigger("play")
      DelayExecuteFuncInView(function()
        UIUtil.SetActive(self.m_lightGo, false)
      end, 1, self)
    end, delayTime or 0, self)
  end
end

function AlbumActivityOneCard:GetCardId()
  return self.m_cardId
end

function AlbumActivityOneCard:GetStars()
  return self.m_stars
end

function AlbumActivityOneCard:OnDestroy()
  Scheduler.UnscheduleTarget(self)
end

AlbumActivityOneCardBack = {}
AlbumActivityOneCardBack.__index = AlbumActivityOneCardBack

function AlbumActivityOneCardBack:Init(cardId, model)
  self.m_cardId = cardId
  self.m_model = model
  self:InitInfo()
end

function AlbumActivityOneCardBack:InitInfo()
  local textColor
  if self.m_model:IsGoldCard(self.m_cardId) then
    self.m_cardbackImg.sprite = self.m_goldImg
    textColor = UIUtil.ConvertHexColor2CSColor("CA5D28")
  else
    self.m_cardbackImg.sprite = self.m_backImg
    textColor = UIUtil.ConvertHexColor2CSColor("9B6A42")
  end
  local albumID = self.m_model:GetAlbumConfig()
  self.m_backnameText.text = GM.GameTextModel:GetText(self.m_cardId .. "_name")
  self.m_backnameText.color = textColor
end

function AlbumActivityOneCardBack:UpdateContent()
end

function AlbumActivityOneCardBack:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
end

AlbumActivityOneCardFront = {}
AlbumActivityOneCardFront.__index = AlbumActivityOneCardFront

function AlbumActivityOneCardFront:Init(cardId, model)
  self.m_cardId = cardId
  self.m_model = model
  self:InitInitInfo()
end

function AlbumActivityOneCardFront:InitInitInfo()
  if self.m_model:IsGoldCard(self.m_cardId) then
    self.m_backImg.sprite = self.m_goldTexture
  else
    self.m_backImg.sprite = self.m_normalTexture
  end
  self.m_frontnameText.text = GM.GameTextModel:GetText(self.m_model:GetCardName(self.m_cardId))
end

function AlbumActivityOneCardFront:UpdateContent(bShowNum, num)
  UIUtil.SetActive(self.m_numBackGo, bShowNum == true)
  if num ~= nil or self.m_model:GetCardCount(self.m_cardId, true) > 0 then
    SpriteUtil.SetImage(self.m_cardImage, self.m_cardId)
  end
  if self.m_model:GetCardCount(self.m_cardId, true) <= 1 then
    UIUtil.SetActive(self.m_numBackGo, false)
    return
  end
  local showNum = self.m_model:GetCardCount(self.m_cardId, true) - 1
  if num ~= nil then
    showNum = num
  end
  self.m_cardNumText.text = "+" .. tostring(showNum)
end

function AlbumActivityOneCardFront:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
end
