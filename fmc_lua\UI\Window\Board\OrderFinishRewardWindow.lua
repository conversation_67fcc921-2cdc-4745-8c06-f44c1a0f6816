OrderFinishRewardWindow = setmetatable({}, TransparentBaseWindow)
OrderFinishRewardWindow.__index = OrderFinishRewardWindow
local mapFirstWaveTypes = {
  [EPropertyType.Energy] = true,
  [EPropertyType.Gem] = true,
  [EPropertyType.Gold] = true,
  [EPropertyType.SkipProp] = true
}

function OrderFinishRewardWindow:Init(arrRewards, cellWidth, worldPosition, orderArea, funcTryOrderLeave, funcOnFinish, playLinkAnim)
  self.m_itemCopyGo:SetActive(false)
  local screenPosition = MainBoardView:GetInstance():ConvertWorldPositionToScreenPosition(worldPosition)
  local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
  uiWorldPosition.z = 0
  local arrFirstWaveRewards = {}
  local arrOtherRewards = {}
  for _, reward in ipairs(arrRewards) do
    if mapFirstWaveTypes[reward[PROPERTY_TYPE]] then
      arrFirstWaveRewards[#arrFirstWaveRewards + 1] = reward
    else
      arrOtherRewards[#arrOtherRewards + 1] = reward
    end
  end
  local mapTargetPosX = {}
  local mapButtonCount = {}
  local mapTargetIndex = {}
  local boardCacheRoot
  if arrOtherRewards[1] then
    local curNormalizedPosition = orderArea:GetNormalizedPosition()
    orderArea:ScrollToOrderGroup(false, true)
    for _, reward in ipairs(arrRewards) do
      local buttonTarget = PropertyAnimationManager.GetButtonTarget(reward[PROPERTY_TYPE])
      if buttonTarget then
        mapTargetPosX[reward] = buttonTarget.transform.position.x
        mapButtonCount[buttonTarget] = (mapButtonCount[buttonTarget] or 0) + 1
        mapTargetIndex[reward] = mapButtonCount[buttonTarget]
      elseif GM.ItemDataModel:IsItemExist(reward[PROPERTY_TYPE]) or AlbumActivityModel.IsAlbumPackReward(reward[PROPERTY_TYPE]) then
        boardCacheRoot = orderArea:GetBoardCacheRoot()
        mapTargetPosX[reward] = boardCacheRoot.transform.position.x
        mapButtonCount[boardCacheRoot] = (mapButtonCount[boardCacheRoot] or 0) + 1
        mapTargetIndex[reward] = mapButtonCount[boardCacheRoot]
      else
        mapTargetPosX[reward] = -100
        mapTargetIndex[reward] = 1
      end
    end
    orderArea:ScrollToNormalizedPosition(curNormalizedPosition, false)
    table.sort(arrRewards, function(reward1, reward2)
      return mapTargetPosX[reward1] > mapTargetPosX[reward2]
    end)
  end
  local arrPos = self:_GetPos(cellWidth, uiWorldPosition, #arrRewards)
  local arrItems = {}
  for index, reward in ipairs(arrRewards) do
    local rewardItem = self:_CreateOneItem(reward, arrPos[#arrRewards - index + 1], mapTargetPosX[reward], mapTargetIndex[reward])
    arrItems[#arrItems + 1] = rewardItem
  end
  local orderGroupButton = orderArea:GetOrderGroupButton()
  orderGroupButton:FlyProgressFromPos(worldPosition)
  local arrFirstWaveItems = {}
  local arrOtherItems = {}
  for _, rewardItem in ipairs(arrItems) do
    if mapFirstWaveTypes[rewardItem.reward[PROPERTY_TYPE]] then
      arrFirstWaveItems[#arrFirstWaveItems + 1] = rewardItem
    else
      arrOtherItems[#arrOtherItems + 1] = rewardItem
    end
  end
  self.m_arrOtherItems = arrOtherItems
  self.m_orderArea = orderArea
  self.m_playLinkAnim = playLinkAnim
  if arrFirstWaveItems[1] then
    for _, rewardItem in ipairs(arrFirstWaveItems) do
      rewardItem:ScaleIn()
      rewardItem:Disappear(0.4)
    end
    DelayExecuteFuncInView(function()
      for _, rewardItem in ipairs(arrFirstWaveItems) do
        rewardItem:Collect()
      end
    end, 0.2, self, false)
  end
  if not arrOtherItems[1] then
    DelayExecuteFuncInView(function()
      self.m_bFinal = true
      funcTryOrderLeave()
      DelayExecuteFuncInView(function()
        self:Close()
        funcOnFinish()
      end, 0.2, self, false)
    end, 0.5, self, false)
    return
  end
  for _, rewardItem in ipairs(arrOtherItems) do
    rewardItem:ScaleIn()
  end
  local delay = arrFirstWaveItems[1] and 0.7 or 0.4
  local itemInterval = 0.1
  local collectDelay = 0.8
  local extraCollectInterval = 0
  local index = 0
  for i = #arrOtherItems, 1, -1 do
    local rewardItem = arrOtherItems[i]
    index = index + 1
    extraCollectInterval = extraCollectInterval + rewardItem:GetExtraCollectInterval()
    local delay2 = delay + index * itemInterval + extraCollectInterval
    DelayExecuteFuncInView(function()
      rewardItem:FlyToEntryTop(uiWorldPosition.y + 180)
      if i == 1 then
        orderArea:ScrollToOrderGroup(true, true)
      end
    end, delay2, self, false)
    DelayExecuteFuncInView(function()
      rewardItem:Collect()
    end, delay2 + collectDelay, self, false)
  end
  DelayExecuteFuncInView(function()
    self.m_bFinal = true
    self.m_orderLeaveTween = funcTryOrderLeave(self.m_bSkipped)
    DelayExecuteFuncInView(function()
      self:Close()
      funcOnFinish()
    end, 1, self, false)
  end, math.max(0, delay + #arrOtherItems * itemInterval + collectDelay + extraCollectInterval - 0.3), self, false)
end

function OrderFinishRewardWindow:_OnSkipClick()
  if not GM.ConfigModel:CanNewOrderRewardAnimationSkip() then
    return
  end
  if self.m_bFinalSkipped then
    return
  end
  self.m_bSkipped = true
  local final = self.m_bFinal
  if final and self.m_orderLeaveTween then
    self.m_orderLeaveTween:Complete(true)
    self.m_orderLeaveTween = nil
  elseif not final and self.m_arrOtherItems[1] then
    self.m_orderArea:ScrollToOrderGroup(false, true)
  end
  Scheduler.FireAndUnscheduleTarget(self)
  if final then
    self.m_bFinalSkipped = true
    PropertyAnimationManager.FinishAllTweens()
  end
  for _, rewardItem in ipairs(self.m_arrOtherItems) do
    rewardItem:Disappear(0)
  end
  if final and GM.ConfigModel:CanNewOrderRewardAnimationBack() and not GM.UIManager:IsViewExisting(UIPrefabConfigName.FlambeTimeWindow) and not self.m_playLinkAnim then
    GM.UIManager:OpenView(UIPrefabConfigName.ScrollBackToOrderWindow)
  end
end

local MIN_ITEM_WIDTH = 80

function OrderFinishRewardWindow:_GetPos(cellWidth, uiWorldPosition, count)
  if count <= 1 then
    return {uiWorldPosition}
  end
  local arrPos = {}
  local needWidth = MIN_ITEM_WIDTH * count
  if 80 <= cellWidth - needWidth then
    cellWidth = math.min(cellWidth, needWidth)
    local left = cellWidth * -0.5
    local delta = cellWidth / (count - 1)
    for i = 0, count - 1 do
      arrPos[#arrPos + 1] = uiWorldPosition + Vector3(left + i * delta, 0, 0)
    end
  else
    local countPerRow = math.ceil(count / 2)
    needWidth = MIN_ITEM_WIDTH * countPerRow
    cellWidth = math.min(cellWidth, needWidth)
    local left = cellWidth * -0.5
    local delta = cellWidth / (countPerRow - 1)
    for i = 0, countPerRow - 1 do
      arrPos[#arrPos + 1] = uiWorldPosition + Vector3(left + i * delta, 50, 0)
    end
    countPerRow = count - countPerRow
    needWidth = MIN_ITEM_WIDTH * countPerRow
    for i = 0, countPerRow - 1 do
      arrPos[#arrPos + 1] = uiWorldPosition + Vector3(left + i * delta, -50, 0)
    end
  end
  return arrPos
end

function OrderFinishRewardWindow:_CreateOneItem(reward, pos, targetX, btnIndex)
  local rewardItemGo = GameObject.Instantiate(self.m_itemCopyGo, self.m_rootRect)
  rewardItemGo:SetActive(true)
  local rewardItem = rewardItemGo:GetLuaTable()
  rewardItem:Init(reward, targetX, btnIndex)
  local trans = rewardItemGo.transform
  trans.localPosition = pos
  trans.localScale = V3Zero
  return rewardItem
end

OrderFinishRewardItem = {}
OrderFinishRewardItem.__index = OrderFinishRewardItem

function OrderFinishRewardItem:Init(reward, targetX, btnIndex)
  self.reward = reward
  self.targetX = targetX
  self.m_btnIndex = btnIndex
  local image, setNativeSize = RewardApi.GetRewardIconNameAndIsSetNativeSize(reward[PROPERTY_TYPE])
  SpriteUtil.SetImage(self.m_image, image)
  self.m_numText.text = "+" .. reward[PROPERTY_COUNT]
end

function OrderFinishRewardItem:OnDestroy()
  if self.m_disappearTween then
    self.m_disappearTween:Kill()
    self.m_disappearTween = nil
  end
  if self.m_scaleTween then
    self.m_scaleTween:Kill()
    self.m_scaleTween = nil
  end
  if self.m_flyTween then
    self.m_flyTween:Kill()
    self.m_flyTween = nil
  end
end

function OrderFinishRewardItem:ScaleIn()
  self.m_scaleTween = self.transform:DOScale(1, 0.3):SetEase(Ease.OutBack, 3):OnComplete(function()
    self.m_scaleTween = nil
  end)
end

function OrderFinishRewardItem:Collect()
  if not RewardApi.CheckRewardTypeValid(self.reward[PROPERTY_TYPE]) then
    return
  end
  local viewData = {
    arrWorldPos = {
      self.transform.localPosition
    },
    noDelayTime = true,
    spriteScale = 1,
    arrNoDiffusion = {true},
    inverseOrder = self.m_btnIndex ~= nil and 1 < self.m_btnIndex
  }
  RewardApi.AcquireRewardsInView({
    self.reward
  }, viewData)
end

function OrderFinishRewardItem:Disappear(delay)
  self.m_disappearTween = self.m_canvasGroup:DOFade(0, 0.3):SetDelay(delay or 0.5):OnComplete(function()
    self.m_disappearTween = nil
  end)
  self.m_bDisappeared = true
end

function OrderFinishRewardItem:FlyToEntryTop(targetY)
  local screenPosition = MainBoardView:GetInstance():ConvertWorldPositionToScreenPosition(Vector3(self.targetX, 0, 0))
  local uiWorldPosition = PositionUtil.UICameraScreen2World(screenPosition)
  local v2StartPos = Vector2(self.transform.localPosition.x, self.transform.localPosition.y)
  local v2EndPos = Vector2(uiWorldPosition.x, targetY)
  local v2Control1 = v2StartPos + Vector2(50, 200)
  local v2Control2 = v2EndPos + Vector2(50, 100)
  local config = {
    from = v2StartPos,
    control1 = v2Control1,
    control2 = v2Control2,
    to = v2EndPos,
    posType = BezierPosType.Local,
    easeType = BezierEaseType.In
  }
  self.m_bezier:MoveTo(config, 0.6)
  local s = DOTween.Sequence()
  s:AppendInterval(0.5)
  s:AppendCallback(function()
    if not self.m_bDisappeared then
      self.m_effectGo:SetActive(true)
    end
  end)
  s:Append(self.transform:DOScale(0.8, 0.1))
  s:Append(self.transform:DOScale(1.3, 0.1))
  s:Append(self.transform:DOScale(1, 0.1))
  s:Join(self.m_canvasGroup:DOFade(0, 0.1):SetDelay(0.1))
  s:AppendCallback(function()
    self.m_flyTween = nil
  end)
  self.m_flyTween = s
end

function OrderFinishRewardItem:GetExtraCollectInterval()
  return self.m_btnIndex > 1 and 0.2 or 0
end
