EnergyBoostButton = setmetatable({}, HudGeneralButton)
EnergyBoostButton.__index = EnergyBoostButton

function EnergyBoostButton:Awake()
  self.m_model = GM.EnergyBoostModel
  HudGeneralButton.Awake(self)
  EventDispatcher.AddListener(EEventType.EnergyBoostModeChanged, self, self.UpdateContent)
  self:UpdateContent()
end

function EnergyBoostButton:UpdateContent()
  self:OnGameModeChanged()
  self:_UpdateIcon()
  self.m_countdownGo:SetActive(not GM.EnergyBoostModel:IsSwitchModeOn() and GM.EnergyBoostModel:IsTimeLimitedOn())
  self:UpdatePerSecond()
end

function EnergyBoostButton:UpdatePerSecond()
  self.m_countdownText.text = TimeUtil.ParseTimeDescription(GM.EnergyBoostModel:GetTimeLimitedLeftTime(), nil, nil, false)
end

function EnergyBoostButton:_UpdateIcon()
  self.m_numText.text = self.m_model:IsEnergyBoostUserOn() and 2 or 1
end

function EnergyBoostButton:_NeedDisplay()
  return HudGeneralButton._NeedDisplay(self) and self.m_model:IsEnergyBoostConfigOn()
end

function EnergyBoostButton:OnBtnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.EnergyBoostSettingWindow)
end
