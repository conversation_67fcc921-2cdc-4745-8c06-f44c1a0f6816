ItemSpreadHelper = {}
ItemSpreadHelper.__index = ItemSpreadHelper
ESpreadReplaceType = {
  item = "item",
  ene = "ene",
  gold = "gold",
  gem = "gem",
  score = "score_"
}
local Level2Energy = "ene_2"

function ItemSpreadHelper.ReplaceSpreadItem(itemSpread, itemCode)
  if StringUtil.StartWith(itemCode, ESpreadReplaceType.score) then
    return ItemSpreadHelper._ReplaceSpreadScore2Item(itemSpread, itemCode)
  elseif itemCode == ESpreadReplaceType.item then
    return ItemSpreadHelper._ReplaceSpreadOrderItem(itemSpread)
  elseif ESpreadReplaceType[itemCode] ~= nil then
    return ItemSpreadHelper._ReplaceSpreadPropertyItem(itemCode)
  else
    return itemCode
  end
end

function ItemSpreadHelper._ReplaceSpreadOrderItem(itemSpread)
  local boardModel = itemSpread:GetItemModel():GetBoardModel()
  local requirements = boardModel:GetUnfilledOrderRequirements()
  local requireChains = {}
  for requirement, _ in pairs(requirements) do
    if not GM.ItemDataModel:IsDishes(requirement) then
      local chain = GM.ItemDataModel:GetChainId(requirement)
      local level = GM.ItemDataModel:GetChainLevel(requirement)
      local splited = StringUtil.Split(chain, "_")
      if 3 < #splited then
        chain = splited[1] .. "_" .. splited[2] .. "_" .. splited[3]
        level = 4
      end
      if requireChains[chain] == nil or level > requireChains[chain] then
        requireChains[chain] = level
      end
    end
  end
  local chainKeys = Table.GetKeys(requireChains)
  if #chainKeys == 0 then
    local configs = itemSpread:GetGeneratedItems()
    for _, config in ipairs(configs) do
      if ESpreadReplaceType[config.Code] ~= nil and config.Code ~= ESpreadReplaceType.item then
        return ItemSpreadHelper._ReplaceSpreadPropertyItem(config.Code)
      end
    end
    return ItemSpreadHelper._ReplaceSpreadPropertyItem(ESpreadReplaceType.ene)
  end
  local chain = Table.ListRandomSelectOne(chainKeys)
  local level
  if GM.ItemDataModel:IsMainProduct(chain) then
    level = math.random(2, 4)
  else
    level = math.random(1, 3)
  end
  level = math.min(level, requireChains[chain])
  return chain .. "_" .. level
end

function ItemSpreadHelper._ReplaceSpreadPropertyItem(itemCode)
  local level = math.random(1, 2)
  return itemCode .. "_" .. level
end

function ItemSpreadHelper._ReplaceSpreadScore2Item(itemSpread, itemCode)
  local nTargetScore = tonumber(string.sub(itemCode, string.len(ESpreadReplaceType.score) + 1))
  if not nTargetScore then
    Log.Error("棋子宝箱：目标分数解析错误：" .. itemCode)
    return Level2Energy
  end
  Log.Info("棋子宝箱：目标分数：" .. nTargetScore)
  local boardModel = itemSpread:GetItemModel():GetBoardModel()
  local status = boardModel:GetUnfilledOrderRequirementsConsiderView()
  local directNonDishLack = status.directNonDishLack
  local indirectNonDishLack = status.indirectNonDishLack
  local directDishes, directNonDishes, indirectDishes, indirectNonDishes = boardModel:GetOrderRequirements(true)
  local chain, level
  local unfilledChain = {}
  for _, items in ipairs({directNonDishLack, indirectNonDishLack}) do
    for item, _ in pairs(items) do
      chain = GM.ItemDataModel:GetChainId(item)
      level = GM.ItemDataModel:GetChainLevel(item)
      if not unfilledChain[chain] or level > unfilledChain[chain] then
        unfilledChain[chain] = level
      end
    end
  end
  local filledChain = {}
  for _, items in ipairs({directNonDishes, indirectNonDishes}) do
    for item, _ in pairs(items) do
      chain = GM.ItemDataModel:GetChainId(item)
      level = GM.ItemDataModel:GetChainLevel(item)
      if not unfilledChain[chain] and (not filledChain[chain] or level > filledChain[chain]) then
        filledChain[chain] = level
      end
    end
  end
  local curCandidateChains = itemSpread:GetCurItemBoxSpreadChain()
  Log.Info("棋子宝箱：点击前的喷发的合成线 " .. table.concat(curCandidateChains, ";"))
  if not next(unfilledChain) and not next(filledChain) then
    if #curCandidateChains == 0 then
      Log.Info("棋子宝箱：当前无订单，棋子宝箱之前未喷发，兜底")
      return Level2Energy
    else
      Log.Info("棋子宝箱：当前无订单，棋子宝箱之前已喷发，仍喷发同合成线棋子")
      local chainLevelMap = {}
      for _, chain in ipairs(curCandidateChains) do
        chainLevelMap[chain] = GM.ItemDataModel:GetChainMaxLevel(chain)
      end
      local itemCode, chain = ItemSpreadHelper._GetTargetScoreItemFromChains(nTargetScore, chainLevelMap)
      return itemCode or Level2Energy
    end
  end
  local removed = false
  for i = #curCandidateChains, 1, -1 do
    chain = curCandidateChains[i]
    if not unfilledChain[chain] and not filledChain[chain] then
      table.remove(curCandidateChains, i)
      Log.Info("棋子宝箱：之前喷发的合成线 " .. chain .. " 当前已无需求，移除")
      removed = true
    end
  end
  if removed then
    itemSpread:SetItemBoxSpreadChain(curCandidateChains)
  end
  local itemBoxMaxType = GM.SystemConfigModel:GetConfig(SystemConfigKey.ItemBoxMaxType) or 1
  if itemBoxMaxType <= #curCandidateChains then
    local chainLevelMap = {}
    for i = 1, #curCandidateChains do
      chain = curCandidateChains[i]
      chainLevelMap[chain] = unfilledChain[chain] or filledChain[chain]
    end
    Log.Info("棋子宝箱：之前喷发的合成线已够多，从这些合成线中选棋子")
    local itemCode, chain = ItemSpreadHelper._GetTargetScoreItemFromChains(nTargetScore, chainLevelMap)
    return itemCode or Level2Energy
  end
  local itemCode, chain = ItemSpreadHelper._GetTargetScoreItemFromChains(nTargetScore, unfilledChain)
  if itemCode then
    Log.Info("棋子宝箱：从订单需求且未满足棋子中找到候选项 " .. itemCode)
    if not Table.ListContain(curCandidateChains, chain) then
      curCandidateChains[#curCandidateChains + 1] = chain
      itemSpread:SetItemBoxSpreadChain(curCandidateChains)
      Log.Info("棋子宝箱：喷发合成线新增 " .. chain)
    end
    return itemCode
  end
  itemCode, chain = ItemSpreadHelper._GetTargetScoreItemFromChains(nTargetScore, filledChain)
  if itemCode then
    Log.Info("棋子宝箱：从订单需求但已满足棋子中找到候选项 " .. itemCode)
    if not Table.ListContain(curCandidateChains, chain) then
      curCandidateChains[#curCandidateChains + 1] = chain
      itemSpread:SetItemBoxSpreadChain(curCandidateChains)
      Log.Info("棋子宝箱：喷发合成线新增 " .. chain)
    end
    return itemCode
  end
  Log.Info("棋子宝箱：兜底")
  return Level2Energy
end

function ItemSpreadHelper._GetTargetScoreItemFromChains(nTargetScore, chainLevelMap)
  local candidates = {}
  local result
  for chain, levelLimit in pairs(chainLevelMap) do
    result = ItemSpreadHelper._GetNearScoreItemFromChain(nTargetScore, chain, levelLimit)
    if result then
      candidates[#candidates + 1] = {
        item = result.item,
        score = result.score,
        chain = chain
      }
    end
  end
  result = Table.ListRandomSelectOne(candidates)
  if result then
    Log.Info("棋子宝箱：从当前可选合成线 " .. result.chain .. " 中取到 " .. result.item .. "，分数为 " .. result.score)
    return result.item, result.chain
  end
  candidates = {}
  for chain, levelLimit in pairs(chainLevelMap) do
    local generator = GM.ItemDataModel:GetItemGenerators(ItemUtility.GetItemType(chain, levelLimit))
    if generator ~= nil and #generator == 1 and StringUtil.StartWith(generator[1], "it") then
      local newCandidate = generator[1]
      local newChain = GM.ItemDataModel:GetChainId(newCandidate)
      local newLevelLimit = GM.ItemDataModel:GetChainLevel(newCandidate)
      result = ItemSpreadHelper._GetNearScoreItemFromChain(nTargetScore, newChain, newLevelLimit)
      if result then
        candidates[#candidates + 1] = {
          item = result.item,
          score = result.score,
          chain = chain
        }
      end
    end
  end
  result = Table.ListRandomSelectOne(candidates)
  if result then
    Log.Info("棋子宝箱：从当前可选合成线 " .. result.chain .. " 的上一级合成线中取到 " .. result.item .. "，分数为 " .. result.score)
    return result.item, result.chain
  end
end

function ItemSpreadHelper._GetNearScoreItemFromChain(nTargetScore, chain, levelLimit)
  local itemList = {}
  local item
  for i = 1, levelLimit do
    item = ItemUtility.GetItemType(chain, i)
    itemList[#itemList + 1] = {
      item = item,
      score = GM.ItemDataModel:GetItemScore(item)
    }
  end
  table.sort(itemList, function(a, b)
    return a.score < b.score
  end)
  if itemList[#itemList].score > 0 and itemList[#itemList].score <= nTargetScore + 0.5 then
    return itemList[#itemList]
  elseif itemList[1].score > nTargetScore + 0.5 then
    return nil
  elseif 1 < #itemList then
    for i = 1, #itemList - 1 do
      if nTargetScore >= itemList[i].score and nTargetScore < itemList[i + 1].score then
        if nTargetScore - itemList[i].score <= itemList[i + 1].score - nTargetScore or itemList[i + 1].score - nTargetScore > 0.5 then
          return itemList[i]
        else
          return itemList[i + 1]
        end
      elseif nTargetScore < itemList[i].score and itemList[i].score - nTargetScore < 0.5 then
        return itemList[i]
      end
    end
  end
end

function ItemSpreadHelper.NeedConfirmBeforeTransform(itemSpread, itemConfig)
  if itemConfig.DropsTotal ~= 1 or itemConfig.DropOnSpot ~= 1 then
    return false
  end
  local genChains = {}
  local generatedItems = itemConfig.GeneratedItems or {}
  for _, generatedItem in ipairs(generatedItems) do
    genChains[GM.ItemDataModel:GetChainId(generatedItem.Code)] = true
  end
  generatedItems = itemConfig.Transform or {}
  for _, generatedItem in ipairs(generatedItems) do
    genChains[GM.ItemDataModel:GetChainId(generatedItem.Currency)] = true
  end
  local isRequired = false
  local boardModel = itemSpread:GetItemModel():GetBoardModel()
  local directDishes, directNonDishes, indirectDishes, indirectNonDishes = boardModel:GetOrderRequirements(true)
  local chain
  for _, requireItems in ipairs({directNonDishes, indirectNonDishes}) do
    for requireItem, _ in pairs(requireItems) do
      chain = GM.ItemDataModel:GetChainId(requireItem)
      if genChains[chain] ~= nil then
        isRequired = true
        break
      end
    end
    if isRequired then
      break
    end
  end
  if not isRequired then
    return true, "transformtip_desc1"
  end
  local remainRequirements, codeCountMap = boardModel:GetUnfilledOrderRequirements()
  local remainRequireCount
  local alreadyEnough = true
  for requireItem, requireCount in pairs(remainRequirements) do
    chain = GM.ItemDataModel:GetChainId(requireItem)
    if genChains[chain] ~= nil then
      remainRequireCount = ItemUtility.GetRemainRequireToLevel1CountMinusBelowLevelItems(requireItem, requireCount, codeCountMap)
      if 0 < remainRequireCount then
        alreadyEnough = false
        break
      end
    end
  end
  if alreadyEnough then
    return true, "transformtip_desc2"
  end
  return false
end
