{"user": {"gem_Buy": {"value": "353", "key": "gem_Buy"}, "User_Level": {"value": "23", "key": "User_Level"}, "gem_Give": {"value": "111", "key": "gem_Give"}, "gold_Give": {"value": "576", "key": "gold_Give"}, "skipprop_Give": {"value": "918", "key": "skipprop_Give"}, "gold_Buy": {"value": 0, "key": "gold_Buy"}, "exp_Buy": {"value": 0, "key": "exp_Buy"}, "taskprgs_Give": {"value": "13", "key": "taskprgs_Give"}, "skipprop_Buy": {"value": 0, "key": "skipprop_Buy"}, "exp_Give": {"value": "185", "key": "exp_Give"}}, "local": {"LastSyncTime": {"value": "1750659525", "key": "LastSyncTime"}, "VId": {"value": "81617190795921ea09448a96a419cc0c", "key": "VId"}, "RegisterVersion": {"value": "1.17.1", "key": "RegisterVersion"}, "UserId": {"value": "920859", "key": "UserId"}, "Name": {"value": "agag afgw", "key": "Name"}, "DataInconsistent": {"value": "false", "key": "DataInconsistent"}, "Authorization": {"value": "Gw0OAAAAAAANAGJoAAAAACNhNmU3ZWR6l/zzCS6qZT4M24FKq5wP", "key": "Authorization"}, "Icon": {"value": "head1", "key": "Icon"}, "DId": {"value": "81617190795921ea09448a96a419cc0c", "key": "DId"}}, "cached_requests2": {}, "shopItem": {"1750645030003": {"shopType": "FlashSale", "leftCount": 5, "costType": "gem", "startCount": 5, "id": "1750645030003", "itemCode": "it_3_1_8", "costCount": 77}, "1750645030005": {"shopType": "FlashSale", "leftCount": 5, "costType": "gem", "startCount": 5, "id": "1750645030005", "itemCode": "it_3_1_7", "costCount": 41}, "1750645030007": {"shopType": "FlashSale", "leftCount": 5, "costType": "gem", "startCount": 5, "id": "1750645030007", "itemCode": "it_4_1_8", "costCount": 82}, "1750645030004": {"shopType": "FlashSale", "leftCount": 5, "costType": "gem", "startCount": 5, "id": "1750645030004", "itemCode": "it_3_1_9", "costCount": 145}, "1750645030002": {"shopType": "DailySpecial", "leftCount": 1, "costType": "gem", "id": "1750645030002", "itemCode": "freebox_1", "costCount": 0}, "1750645030006": {"shopType": "FlashSale", "leftCount": 5, "costType": "gem", "startCount": 5, "id": "1750645030006", "itemCode": "it_3_2_5", "costCount": 52}, "1750645030008": {"shopType": "FlashSale", "leftCount": 5, "costType": "gem", "startCount": 5, "id": "1750645030008", "itemCode": "it_5_2_7", "costCount": 102}, "1750645030001": {"shopType": "DailySpecial", "leftCount": 1, "costType": "gem", "id": "1750645030001", "itemCode": "enebox_1", "costCount": 20}}, "orders": {"50160": {"groupId": 3, "avatarId": 2, "chapterId": 5, "requirementStr": "it_1_2_6;ds_grillsf_5", "rewards": "gold-219", "id": "50160", "createTime": 1750606744, "cleanGoldCount": 0, "type": 1}, "50210": {"groupId": 3, "avatarId": 7, "chapterId": 5, "requirementStr": "it_2_2_5;ds_grillmt_11", "rewards": "gold-302", "id": "50210", "createTime": 1750606744, "cleanGoldCount": 0, "type": 1}, "50200": {"groupId": 3, "avatarId": 6, "chapterId": 5, "requirementStr": "it_2_3_5;ds_e1icytre_2", "rewards": "gold-275", "id": "50200", "createTime": 1750606744, "cleanGoldCount": 0, "type": 1}}, "tutorial": {"tutorial_coin_race_entry": {"id": "tutorial_coin_race_entry", "state": 2, "ongoingDatas": ""}, "merge2": {"id": "merge2", "state": 2, "ongoingDatas": ""}, "task1_2": {"id": "task1_2", "state": 2, "ongoingDatas": ""}, "bubble": {"id": "bubble", "state": 2, "ongoingDatas": ""}, "tutorial_coin_race_first_main_window": {"id": "tutorial_coin_race_first_main_window", "state": 2, "ongoingDatas": ""}, "task1_4": {"id": "task1_4", "state": 2, "ongoingDatas": ""}, "tutorial_cg": {"id": "tutorial_cg", "state": 2, "ongoingDatas": ""}, "additem_+8": {"id": "additem_+8", "state": 2, "ongoingDatas": ""}, "tutorial_battlepass": {"id": "tutorial_battlepass", "state": 2, "ongoingDatas": ""}, "clickPD": {"id": "clickPD", "state": 2, "ongoingDatas": ""}, "shop": {"id": "shop", "state": 2, "ongoingDatas": ""}, "tutorial_producer_inventory": {"id": "tutorial_producer_inventory", "state": 1, "ongoingDatas": ""}, "order10010": {"id": "order10010", "state": 2, "ongoingDatas": ""}, "cd_speed": {"id": "cd_speed", "state": 2, "ongoingDatas": ""}, "order10030": {"id": "order10030", "state": 2, "ongoingDatas": ""}, "order10020": {"id": "order10020", "state": 2, "ongoingDatas": ""}, "tutorial_pk_race_start": {"id": "tutorial_pk_race_start", "state": 2, "ongoingDatas": ""}, "tutorial_energy_boost": {"id": "tutorial_energy_boost", "state": 2, "ongoingDatas": ""}, "merge1": {"id": "merge1", "state": 2, "ongoingDatas": ""}, "order10050": {"id": "order10050", "state": 2, "ongoingDatas": ""}, "order10070": {"id": "order10070", "state": 2, "ongoingDatas": ""}, "tutorial_extraboard_cobweb_merge": {"id": "tutorial_extraboard_cobweb_merge", "state": 1, "ongoingDatas": ""}, "task1_3": {"id": "task1_3", "state": 2, "ongoingDatas": ""}, "order10140": {"id": "order10140", "state": 2, "ongoingDatas": ""}, "cook2": {"id": "cook2", "state": 2, "ongoingDatas": ""}, "CD_pd_1_7": {"id": "CD_pd_1_7", "state": 2, "ongoingDatas": ""}, "cook1": {"id": "cook1", "state": 2, "ongoingDatas": ""}, "tutorial_coin_race_second_main_window": {"id": "tutorial_coin_race_second_main_window", "state": 2, "ongoingDatas": ""}, "additem_old_user": {"id": "additem_old_user", "state": 2, "ongoingDatas": ""}, "weakGesture": {"id": "weakGesture", "state": 1, "ongoingDatas": ""}, "order10040": {"id": "order10040", "state": 2, "ongoingDatas": ""}, "tutorial_coin_race_order": {"id": "tutorial_coin_race_order", "state": 2, "ongoingDatas": ""}, "cook3": {"id": "cook3", "state": 2, "ongoingDatas": ""}, "order_item_info": {"id": "order_item_info", "state": 2, "ongoingDatas": ""}, "order10080": {"id": "order10080", "state": 2, "ongoingDatas": ""}, "tutorial_extraboard_start": {"id": "tutorial_extraboard_start", "state": 2, "ongoingDatas": ""}, "tutorial_blind_chest": {"id": "tutorial_blind_chest", "state": 2, "ongoingDatas": ""}, "CD_pd_2_6": {"id": "CD_pd_2_6", "state": 2, "ongoingDatas": ""}, "tutorial_battlepass_loop": {"id": "tutorial_battlepass_loop", "state": 1, "ongoingDatas": ""}, "order_group": {"id": "order_group", "state": 2, "ongoingDatas": ""}, "timeline": {"id": "timeline", "state": 2, "ongoingDatas": ""}, "cache": {"id": "cache", "state": 2, "ongoingDatas": ""}, "energy": {"id": "energy", "state": 2, "ongoingDatas": ""}, "task1_1": {"id": "task1_1", "state": 2, "ongoingDatas": ""}, "tutorial_digactivity_seconddig": {"id": "tutorial_digactivity_seconddig", "state": 2, "ongoingDatas": ""}, "tutorial_extraboard_cobweb_unlock": {"id": "tutorial_extraboard_cobweb_unlock", "state": 1, "ongoingDatas": ""}, "tutorial_extraboard_item_delete": {"id": "tutorial_extraboard_item_delete", "state": 1, "ongoingDatas": ""}, "toboard": {"id": "toboard", "state": 2, "ongoingDatas": ""}, "tutorial_digactivity_firstdig": {"id": "tutorial_digactivity_firstdig", "state": 2, "ongoingDatas": ""}}, "shop": {"refreshCost": {"value": 10, "key": "refreshCost"}, "dailyRefreshTime": {"value": 1750645030, "key": "dailyRefreshTime"}, "flashRefreshTime": {"value": 1750645030, "key": "flashRefreshTime"}, "eventEnergyBuyCost": {"value": 10, "key": "eventEnergyBuyCost"}, "energyBuyCost": {"value": 10, "key": "energyBuyCost"}, "refreshCostResetTime": {"value": 1750645030, "key": "refreshCostResetTime"}, "energyRefreshTime": {"value": 1750645030, "key": "energyRefreshTime"}}, "biSync": {"GameDuration": {"value": "318010", "key": "GameDuration"}, "ConsumedEnergy": {"value": "25409", "key": "ConsumedEnergy"}, "ConsumedFreeGem": {"value": "255", "key": "ConsumedFreeGem"}, "ConsumedPaidGem": {"value": "2222", "key": "ConsumedPaidGem"}}, "config": {"key": {"value": {"bakeOut": {"config": {"sTime": 1750299000, "rTime": 1750903800, "eTime": 1750903200, "bakeout_rank_parameter": [{"settlementTime": 300, "delayTime": 90, "retainTime": 601200, "noRegisterTime": 300, "uploadTime": 60, "maxNum": 20}], "staticInclude": ["bakeout_rank_reward#1st", "bakeout_rank_exchange#default", "bakeout_rank_parameter#1st"], "id": 100022, "bakeout_rank_exchange": [{"cost": 10, "time": 1}, {"cost": 11, "time": 2}, {"cost": 14, "time": 3}, {"cost": 17, "time": 4}, {"cost": 21, "time": 5}, {"cost": 28, "time": 6}, {"cost": 37, "time": 7}, {"cost": 51, "time": 8}, {"cost": 72, "time": 9}, {"cost": 100, "time": 10}], "bakeout_rank_reward": [{"start_rank": 1, "end_rank": 1, "rewards": ["gem-25", "energy-100", "skiptime_1-1"]}, {"start_rank": 2, "end_rank": 2, "rewards": ["gem-20", "energy-80", "additem_1-1"]}, {"start_rank": 3, "end_rank": 3, "rewards": ["gem-15", "energy-50"]}, {"start_rank": 4, "end_rank": 6, "rewards": ["gem-10"]}, {"start_rank": 7, "end_rank": 10, "rewards": ["energy-50"]}, {"start_rank": 11, "end_rank": 15, "rewards": ["skipprop-50"]}, {"start_rank": 16, "end_rank": 20, "rewards": ["skipprop-25"]}]}, "md5": "589a1da32255775b93d7f2ace5305150"}, "coinRace": {"config": {"sTime": 1750644000, "rTime": 1751162400, "eTime": 1750989600, "sLv": 7, "id": 112, "generalActivityConf": [{"confType": "divided", "param_int": 22}], "coin_race": [{"target": 20, "round": 1, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 20, "Currency": "skipprop"}, {"Amount": 20, "Currency": "energy"}], "rankReward3": [{"Amount": 10, "Currency": "skipprop"}, {"Amount": 10, "Currency": "energy"}], "rankReward2": [{"Amount": 15, "Currency": "skipprop"}, {"Amount": 15, "Currency": "energy"}]}, {"target": 25, "round": 2, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 25, "Currency": "skipprop"}, {"Amount": 30, "Currency": "energy"}], "rankReward3": [{"Amount": 20, "Currency": "skipprop"}, {"Amount": 10, "Currency": "energy"}], "rankReward2": [{"Amount": 20, "Currency": "skipprop"}, {"Amount": 20, "Currency": "energy"}]}, {"target": 30, "round": 3, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 30, "Currency": "skipprop"}, {"Amount": 35, "Currency": "energy"}], "rankReward3": [{"Amount": 20, "Currency": "skipprop"}, {"Amount": 15, "Currency": "energy"}], "rankReward2": [{"Amount": 25, "Currency": "skipprop"}, {"Amount": 25, "Currency": "energy"}]}, {"target": 35, "round": 4, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 1, "Currency": "additem_1"}, {"Amount": 45, "Currency": "energy"}], "rankReward3": [{"Amount": 20, "Currency": "skipprop"}, {"Amount": 20, "Currency": "energy"}], "rankReward2": [{"Amount": 1, "Currency": "additem_1"}, {"Amount": 25, "Currency": "energy"}]}, {"target": 40, "round": 5, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 5, "Currency": "gem"}, {"Amount": 40, "Currency": "skipprop"}, {"Amount": 1, "Currency": "greenbox_1"}], "rankReward3": [{"Amount": 25, "Currency": "skipprop"}, {"Amount": 20, "Currency": "energy"}], "rankReward2": [{"Amount": 5, "Currency": "gem"}, {"Amount": 1, "Currency": "greenbox_1"}]}, {"target": 40, "round": 6, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 40, "Currency": "skipprop"}, {"Amount": 45, "Currency": "energy"}], "rankReward3": [{"Amount": 30, "Currency": "skipprop"}, {"Amount": 20, "Currency": "energy"}], "rankReward2": [{"Amount": 30, "Currency": "skipprop"}, {"Amount": 30, "Currency": "energy"}]}, {"target": 50, "round": 7, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 60, "Currency": "skipprop"}, {"Amount": 50, "Currency": "energy"}], "rankReward3": [{"Amount": 30, "Currency": "skipprop"}, {"Amount": 25, "Currency": "energy"}], "rankReward2": [{"Amount": 1, "Currency": "additem_1"}, {"Amount": 35, "Currency": "energy"}]}, {"target": 55, "round": 8, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 5, "Currency": "gem"}, {"Amount": 60, "Currency": "skipprop"}, {"Amount": 50, "Currency": "energy"}], "rankReward3": [{"Amount": 40, "Currency": "skipprop"}, {"Amount": 25, "Currency": "energy"}], "rankReward2": [{"Amount": 50, "Currency": "skipprop"}, {"Amount": 40, "Currency": "energy"}]}, {"target": 65, "round": 9, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 1, "Currency": "greenbox_1"}, {"Amount": 60, "Currency": "skipprop"}, {"Amount": 50, "Currency": "energy"}], "rankReward3": [{"Amount": 40, "Currency": "skipprop"}, {"Amount": 30, "Currency": "energy"}], "rankReward2": [{"Amount": 60, "Currency": "skipprop"}, {"Amount": 45, "Currency": "energy"}]}, {"target": 75, "round": 10, "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward1": [{"Amount": 10, "Currency": "gem"}, {"Amount": 180, "Currency": "skipprop"}, {"Amount": 50, "Currency": "energy"}], "rankReward3": [{"Amount": 60, "Currency": "skipprop"}, {"Amount": 30, "Currency": "energy"}], "rankReward2": [{"Amount": 70, "Currency": "skipprop"}, {"Amount": 50, "Currency": "energy"}]}], "include": ["coin_race#round10", "generalActivityConf#round10"]}, "md5": "91faddd74539d2594b485b595cc6cb5a"}, "bundleController": {"config": [{"groupId": "starter", "maxBuyNum": 1, "popCD": 360, "is_open": 1, "bundleCondition": [{"taskFinished": {"ChapterId": 2, "TaskCount": 3}}], "uiCode": "starter", "order": ["starter_499"], "bundleContent": [{"price": 4.99, "bundleId": "starter_499", "content": [{"Amount": 240, "Currency": "gem"}, {"Amount": 200, "Currency": "energy"}, {"Amount": 4, "Currency": "additem_1"}, {"Amount": 1, "Currency": "enebox_1"}], "discountTag": "200%", "payID": "starter_bundle_1"}], "id": 1, "dailyBuyNum": 1, "bundleTrigger": [{"trigger": "task_finished", "popNum": 1, "popOrder": ["starter"]}], "include": ["bundleContent#starter_499", "bundleTrigger#task_finished", "bundleCondition#starter_499"], "dailyShowNum": 2, "duration": 720, "specialType": "starter", "buyCD": 10080}, {"sTime": 1741053600, "dailyBuyNum": 2, "order": ["order_1999"], "uiCode": "orderGroup", "bundleTrigger": [{"trigger": "finish_order_group", "popNum": 1, "popOrder": ["orderGroup"]}], "include": ["bundleContent#order_1999", "bundleTrigger#finish_order_group"], "id": 19, "duration": 60, "groupId": "order1", "eTime": 1765411200, "sLv": 8, "bundleContent": [{"price": 19.99, "bundleId": "order_1999", "content": [{"Amount": 560, "Currency": "gem"}, {"Amount": 1400, "Currency": "energy"}, {"Amount": 2, "Currency": "greenbox_1"}, {"Amount": 3, "Currency": "greenbox2_1"}], "discountTag": "150%", "payID": "finish_order_1999"}], "popCD": 60, "dailyShowNum": 5, "is_open": 1, "specialType": "orderGroup", "buyCD": 30}, {"sTime": 1742522400, "dailyBuyNum": 5, "order": ["et5.99", "et9.99", "et15.99", "et19.99"], "uiCode": "multiTier", "bundleTrigger": [{"trigger": "lack_energy", "popNum": 1, "popOrder": ["energy"]}], "include": ["bundleContent#et5.99", "bundleContent#et9.99", "bundleContent#et15.99", "bundleContent#et19.99", "bundleTrigger#lack_energy", "generalBundleConf#15.99"], "id": 25, "duration": 30, "groupId": "energytier1", "eTime": 1765411200, "sLv": 8, "bundleContent": [{"price": 5.99, "bundleId": "et5.99", "payID": "energytier_599", "content": [{"Amount": 820, "Currency": "energy"}], "discountTag": "140%", "originPrice": 8.99}, {"price": 9.99, "bundleId": "et9.99", "payID": "energytier_999", "content": [{"Amount": 1400, "Currency": "energy"}], "discountTag": "150%", "originPrice": 14.99}, {"price": 15.99, "bundleId": "et15.99", "payID": "energytier_1599", "content": [{"Amount": 2300, "Currency": "energy"}], "discountTag": "160%", "originPrice": 23.99}, {"price": 19.99, "bundleId": "et19.99", "payID": "energytier_1999", "content": [{"Amount": 3200, "Currency": "energy"}], "discountTag": "170%", "originPrice": 34.99}], "is_open": 1, "popCD": 5, "dailyShowNum": 10, "generalBundleConf": [{"confType": "defaultOrder", "param_string": "et15.99"}], "specialType": "multiTier", "buyCD": 30}, {"sTime": 1749434400, "dailyBuyNum": 2, "order": ["timespeed_1999"], "uiCode": "cdFill", "bundleTrigger": [{"popOrder": ["cdFill"], "trigger": "pd_cd_number"}], "include": ["bundleContent#timespeed_1999", "bundleTrigger#pd_cd_number", "generalBundleConf#pd_cd_number"], "id": 81, "duration": 360, "groupId": "timespeed_1", "eTime": 1765411200, "sLv": 8, "bundleContent": [{"price": 19.99, "payID": "cd_speed_1999", "bundleId": "timespeed_1999", "content": [{"Amount": 800, "Currency": "gem"}, {"Amount": 300, "Currency": "energy"}, {"Amount": 1, "Currency": "timespeed_1"}]}], "is_open": 1, "popCD": 60, "dailyShowNum": 10, "generalBundleConf": [{"confType": "pd_cd_number", "param_int": 4}], "specialType": "cdFill", "buyCD": 30}, {"groupId": "chain7", "rTime": 1750730400, "eTime": 1750644000, "is_open": 1, "uiCode": "chain", "dailyShowNum": 2, "bundleContentChain": [{"price": 0, "skin": "1", "bundleId": "chain_skip_1", "step": 1, "content": [{"Amount": 5, "Currency": "skipprop"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_2", "step": 2, "content": [{"Amount": 10, "Currency": "skipprop"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_3", "step": 3, "content": [{"Amount": 1, "Currency": "ene_1"}]}, {"price": 1.99, "skin": "3", "bundleId": "chain_skip_4", "step": 4, "content": [{"Amount": 30, "Currency": "gem"}, {"Amount": 150, "Currency": "energy"}], "payID": "chaingift_199"}, {"price": 0, "skin": "1", "bundleId": "chain_skip_5", "step": 5, "content": [{"Amount": 15, "Currency": "skipprop"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_6", "step": 6, "content": [{"Amount": 1, "Currency": "ene_2"}]}, {"price": 2.99, "skin": "3", "bundleId": "chain_skip_7", "step": 7, "content": [{"Amount": 40, "Currency": "gem"}, {"Amount": 200, "Currency": "energy"}], "payID": "chaingift_299"}, {"price": 0, "skin": "1", "bundleId": "chain_skip_8", "step": 8, "content": [{"Amount": 1, "Currency": "ene_1"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_9", "step": 9, "content": [{"Amount": 30, "Currency": "skipprop"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_10", "step": 10, "content": [{"Amount": 10, "Currency": "gem"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_11", "step": 11, "content": [{"Amount": 1, "Currency": "ene_3"}]}, {"price": 3.99, "skin": "3", "bundleId": "chain_skip_12", "step": 12, "content": [{"Amount": 60, "Currency": "gem"}, {"Amount": 250, "Currency": "energy"}], "payID": "chaingift_399"}, {"price": 0, "skin": "1", "bundleId": "chain_skip_13", "step": 13, "content": [{"Amount": 40, "Currency": "skipprop"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_14", "step": 14, "content": [{"Amount": 1, "Currency": "greenbox_1"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_15", "step": 15, "content": [{"Amount": 50, "Currency": "skipprop"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_16", "step": 16, "content": [{"Amount": 1, "Currency": "ene_4"}]}, {"price": 4.99, "skin": "3", "bundleId": "chain_skip_17", "step": 17, "content": [{"Amount": 80, "Currency": "gem"}, {"Amount": 300, "Currency": "energy"}], "payID": "chaingift_499"}, {"price": 0, "skin": "1", "bundleId": "chain_skip_18", "step": 18, "content": [{"Amount": 60, "Currency": "skipprop"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_19", "step": 19, "content": [{"Amount": 15, "Currency": "gem"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_20", "step": 20, "content": [{"Amount": 70, "Currency": "skipprop"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_21", "step": 21, "content": [{"Amount": 1, "Currency": "greenbox2_1"}]}, {"price": 6.99, "skin": "3", "bundleId": "chain_skip_22", "step": 22, "content": [{"Amount": 100, "Currency": "gem"}, {"Amount": 450, "Currency": "energy"}], "payID": "chaingift_699"}, {"price": 0, "skin": "1", "bundleId": "chain_skip_23", "step": 23, "content": [{"Amount": 100, "Currency": "skipprop"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_24", "step": 24, "content": [{"Amount": 1, "Currency": "greenbox2_1"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_25", "step": 25, "content": [{"Amount": 150, "Currency": "skipprop"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_26", "step": 26, "content": [{"Amount": 20, "Currency": "gem"}]}, {"price": 15.99, "skin": "3", "bundleId": "chain_skip_27", "step": 27, "content": [{"Amount": 240, "Currency": "gem"}, {"Amount": 1000, "Currency": "energy"}], "payID": "chaingift_1599"}, {"price": 0, "skin": "2", "bundleId": "chain_skip_28", "step": 28, "content": [{"Amount": 1, "Currency": "greenbox2_1"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_29", "step": 29, "content": [{"Amount": 200, "Currency": "skipprop"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_30", "step": 30, "content": [{"Amount": 1, "Currency": "additem_3"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_31", "step": 31, "content": [{"Amount": 400, "Currency": "skipprop"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_32", "step": 32, "content": [{"Amount": 50, "Currency": "gem"}]}, {"price": 19.99, "skin": "3", "bundleId": "chain_skip_33", "step": 33, "content": [{"Amount": 320, "Currency": "gem"}, {"Amount": 1200, "Currency": "energy"}], "payID": "chaingift_1999"}, {"price": 0, "skin": "2", "bundleId": "chain_skip_34", "step": 34, "content": [{"Amount": 200, "Currency": "skipprop"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_35", "step": 35, "content": [{"Amount": 2, "Currency": "greenbox2_1"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_36", "step": 36, "content": [{"Amount": 400, "Currency": "skipprop"}]}, {"price": 0, "skin": "1", "bundleId": "chain_skip_37", "step": 37, "content": [{"Amount": 2, "Currency": "additem_3"}]}, {"price": 0, "skin": "2", "bundleId": "chain_skip_38", "step": 38, "content": [{"Amount": 100, "Currency": "gem"}]}], "sLv": 8, "bundleTrigger": [{"trigger": "login"}], "include": ["bundleContentChain#chain_skip", "bundleTrigger#login"], "id": 83, "duration": 4320, "specialType": "chain", "sTime": 1750384800}], "md5": "30764b33940af559004e1ba10299c1a4"}, "general_conf": {"config": [{"id": 1, "confType": "balance_gold", "param_int": 1}, {"id": 3, "confType": "hint_inner_dish", "param_int": 1}, {"id": 4, "confType": "level_trans_day", "param_int": 1}, {"id": 5, "confType": "social_bind", "param_int": 1}, {"id": 6, "confType": "skipprop_not_enough", "param_int": 1}, {"id": 7, "confType": "ChangeInventory", "param_int": 1}, {"id": 8, "confType": "ingredient_recipe_hint", "param_int": 1}, {"id": 9, "param_int": 200, "confType": "doubleEnergy_trigger", "sLv": 16}, {"id": 10, "confType": "cache", "param_int": 1}, {"id": 11, "confType": "new_order_reward_anim", "param_int": 1}, {"id": 12, "confType": "new_order_reward_anim_skip", "param_int": 1}, {"id": 14, "param_int_array": [3, 10], "confType": "item_auto_recycle"}, {"id": 15, "confType": "order_seq_energy_diff", "param_int": 1}, {"id": 16, "confType": "board_cook_bubble", "param_int": 1}, {"id": 17, "confType": "item_delete_corn_husk", "param_int": 1}, {"id": 18, "confType": "order_new_tag", "param_int": 1}], "md5": "0c452676dbe6fba5117a6814cbfdfbd7"}, "extraBoard6": {"config": {"sTime": 1750644000, "rTime": 1751335200, "eTime": 1751248800, "sLv": 8, "order_token_control": [{"rewardsWeight": [{"Weight": 100, "Amount": 1, "Currency": "eb_1_1"}], "score_max": 30, "score_min": 15}, {"rewardsWeight": [{"Weight": 100, "Amount": 1, "Currency": "eb_1_2"}], "score_max": 60, "score_min": 30}, {"rewardsWeight": [{"Weight": 100, "Amount": 2, "Currency": "eb_1_2"}], "score_max": 90, "score_min": 60}, {"rewardsWeight": [{"Weight": 100, "Amount": 3, "Currency": "eb_1_2"}], "score_max": 120, "score_min": 90}, {"rewardsWeight": [{"Weight": 100, "Amount": 2, "Currency": "eb_1_3"}], "score_max": 180, "score_min": 120}, {"rewardsWeight": [{"Weight": 100, "Amount": 3, "Currency": "eb_1_3"}], "score_max": 240, "score_min": 180}, {"rewardsWeight": [{"Weight": 100, "Amount": 4, "Currency": "eb_1_3"}], "score_min": 240}], "include": ["extraBoardConfig#extraBoard6", "order_token_control#extraBoardA7", "produce_token_control#extraBoardA7"], "id": 109, "produce_token_control": [{"energyNum": 15, "rewardsWeight": [{"Weight": 100, "Amount": 1, "Currency": "eb_1_1"}]}], "extraBoardConfig": [{"maxLevel": 12, "itemPrefix": "eb6", "board": "extraBoardA7", "maxReward": [{"Amount": 100, "Currency": "energy"}]}]}, "md5": "1e5579b874466473b2bd6c63a5eafb8c"}, "rateUs": {"config": [{"task": [{"ChapterId": 2, "TaskCount": 30}, {"ChapterId": 3, "TaskCount": 29}], "sLv": 5, "id": 2, "contact": 0, "link": "market://details?id=com.cola.game"}], "md5": "cc9b438acb2247f9676ad310935caae2"}, "chapterUpdate": {"config": [{"openDay": 1735178400, "enable": 1, "dayLevel": 75, "preDay": 1735005600, "chapter": 5}, {"openDay": 1736388000, "enable": 1, "dayLevel": 100, "preDay": 1736215200, "chapter": 6}, {"openDay": 1737597600, "enable": 1, "dayLevel": 125, "preDay": 1737424800, "chapter": 7}, {"openDay": 1739412000, "enable": 1, "dayLevel": 150, "preDay": 1739239200, "chapter": 8}, {"openDay": 1740621600, "enable": 1, "dayLevel": 165, "preDay": 1740448800, "chapter": 9}, {"openDay": 1741226400, "enable": 1, "dayLevel": 180, "preDay": 1741053600, "chapter": 9}, {"openDay": 1741831200, "enable": 1, "dayLevel": 195, "preDay": 1741658400, "chapter": 10}, {"openDay": 1742436000, "enable": 1, "dayLevel": 210, "preDay": 1741831200, "chapter": 10}, {"openDay": 1743040800, "enable": 1, "dayLevel": 225, "preDay": 1742868000, "chapter": 11}, {"openDay": 1743645600, "enable": 1, "dayLevel": 240, "preDay": 1743040800, "chapter": 11}, {"openDay": 1744250400, "enable": 1, "dayLevel": 255, "preDay": 1744077600, "chapter": 12}, {"openDay": 1744855200, "enable": 1, "dayLevel": 270, "preDay": 1744250400, "chapter": 12}, {"openDay": 1745460000, "enable": 1, "dayLevel": 285, "preDay": 1745287200, "chapter": 13}, {"openDay": 1746064800, "enable": 1, "dayLevel": 300, "preDay": 1745460000, "chapter": 13}, {"openDay": 1746669600, "enable": 1, "dayLevel": 315, "preDay": 1746496800, "chapter": 14}, {"openDay": 1747274400, "enable": 1, "dayLevel": 330, "preDay": 1746669600, "chapter": 14}, {"openDay": 1747879200, "enable": 1, "dayLevel": 345, "preDay": 1747706400, "chapter": 15}, {"openDay": 1748484000, "enable": 1, "dayLevel": 360, "preDay": 1747879200, "chapter": 15}, {"openDay": 1749088800, "enable": 1, "dayLevel": 375, "preDay": 1748916000, "chapter": 16}, {"openDay": 1749693600, "enable": 1, "dayLevel": 390, "preDay": 1749088800, "chapter": 16}, {"openDay": 1750298400, "enable": 1, "dayLevel": 405, "preDay": 1750125600, "chapter": 17}, {"openDay": 1750903200, "enable": 1, "dayLevel": 420, "preDay": 1750298400, "chapter": 17}], "md5": "065ca931c92874f6f680080c8f6949bd"}, "notify": {"config": [{"maxNum": 2, "scene": ["EnergyRefill", "ComeBack_24", "ComeBack_48", "ItemCooldown"], "pushtimetype": 0, "id": 1, "interval": 86400}], "md5": "47c1751e7551193850791d81b02617b6"}, "blindChest1": {"config": {"sTime": 1750384800, "rTime": 1750816800, "eTime": 1750644000, "sLv": 7, "order_token_control": [{"rewardsWeight": [{"Weight": 100, "Amount": 1, "Currency": "blindChest1"}], "score_max": 120, "score_min": 0}, {"rewardsWeight": [{"Weight": 100, "Amount": 2, "Currency": "blindChest1"}], "score_max": 180, "score_min": 120}, {"rewardsWeight": [{"Weight": 100, "Amount": 3, "Currency": "blindChest1"}], "score_max": 240, "score_min": 180}, {"score_min": 240, "rewardsWeight": [{"Weight": 100, "Amount": 4, "Currency": "blindChest1"}]}], "blindChest_rewards": [{"turn": 1, "round": 1, "otherprizes": [{"Amount": 5, "Currency": "skipprop"}, {"Amount": 5, "Currency": "skipprop"}, {"Amount": 10, "Currency": "skipprop"}, {"Amount": 5, "Currency": "energy"}], "bigprize": [{"Amount": 10, "Currency": "energy"}], "boxnum": 6}, {"turn": 2, "round": 1, "otherprizes": [{"Amount": 5, "Currency": "skipprop"}, {"Amount": 5, "Currency": "skipprop"}, {"Amount": 10, "Currency": "skipprop"}, {"Amount": 1, "Currency": "cbox1_1"}], "bigprize": [{"Amount": 15, "Currency": "energy"}], "boxnum": 7}, {"turn": 3, "round": 1, "otherprizes": [{"Amount": 5, "Currency": "skipprop"}, {"Amount": 10, "Currency": "skipprop"}, {"Amount": 15, "Currency": "skipprop"}, {"Amount": 1, "Currency": "freebox_1"}], "bigprize": [{"Amount": 20, "Currency": "energy"}], "boxnum": 8}, {"turn": 4, "round": 1, "otherprizes": [{"Amount": 5, "Currency": "energy"}, {"Amount": 5, "Currency": "energy"}, {"Amount": 1, "Currency": "cbox1_1"}, {"Amount": 5, "Currency": "skipprop"}, {"Amount": 5, "Currency": "skipprop"}], "bigprize": [{"Amount": 50, "Currency": "skipprop"}], "boxnum": 8}, {"turn": 5, "round": 1, "otherprizes": [{"Amount": 1, "Currency": "gem_1"}, {"Amount": 1, "Currency": "freebox_1"}, {"Amount": 5, "Currency": "skipprop"}, {"Amount": 10, "Currency": "skipprop"}, {"Amount": 15, "Currency": "skipprop"}], "bigprize": [{"Amount": 20, "Currency": "energy"}], "boxnum": 10}, {"turn": 6, "round": 1, "otherprizes": [{"Amount": 1, "Currency": "gem_1"}, {"Amount": 1, "Currency": "cbox2_1"}, {"Amount": 5, "Currency": "skipprop"}, {"Amount": 10, "Currency": "skipprop"}, {"Amount": 15, "Currency": "skipprop"}, {"Amount": 5, "Currency": "skipprop"}], "bigprize": [{"Amount": 30, "Currency": "energy"}], "boxnum": 10}, {"turn": 7, "round": 1, "otherprizes": [{"Amount": 1, "Currency": "gem_2"}, {"Amount": 5, "Currency": "skipprop"}, {"Amount": 10, "Currency": "skipprop"}, {"Amount": 15, "Currency": "skipprop"}, {"Amount": 1, "Currency": "freebox_1"}], "bigprize": [{"Amount": 30, "Currency": "energy"}], "boxnum": 12}, {"turn": 8, "round": 1, "otherprizes": [{"Amount": 5, "Currency": "skipprop"}, {"Amount": 10, "Currency": "skipprop"}, {"Amount": 15, "Currency": "skipprop"}, {"Amount": 5, "Currency": "skipprop"}, {"Amount": 10, "Currency": "skipprop"}, {"Amount": 15, "Currency": "skipprop"}, {"Amount": 5, "Currency": "energy"}], "bigprize": [{"Amount": 80, "Currency": "skipprop"}], "boxnum": 12}, {"turn": 0, "bigprize": [{"Amount": 10, "Currency": "gem"}, {"Amount": 50, "Currency": "energy"}, {"Amount": 100, "Currency": "skipprop"}, {"Amount": 1, "Currency": "greenbox_1"}], "round": 1, "boxnum": 0}], "include": ["blindChest_rewards#blindChest2", "blindChest_probability#blindChest2", "order_token_control#blindChest2"], "id": 106, "blindChest_probability": [{"getin10": 0, "getin3": 75, "round": 1, "getin8": 0, "getin6": 0, "getin4": 100, "getin7": 0, "turn": 1, "getin1": 0, "getin5": 0, "getin12": 0, "getin11": 0, "getin2": 20, "getin9": 0, "group": 1}, {"getin10": 0, "getin3": 20, "round": 1, "getin8": 0, "getin6": 100, "getin4": 61, "getin7": 0, "turn": 2, "getin1": 0, "getin5": 64, "getin12": 0, "getin11": 0, "getin2": 10, "getin9": 0, "group": 1}, {"getin10": 0, "getin3": 20, "round": 1, "getin8": 0, "getin6": 0, "getin4": 75, "getin7": 0, "turn": 3, "getin1": 0, "getin5": 100, "getin12": 0, "getin11": 0, "getin2": 0, "getin9": 0, "group": 1}, {"getin10": 0, "getin3": 0, "round": 1, "getin8": 0, "getin6": 75, "getin4": 0, "getin7": 100, "turn": 4, "getin1": 0, "getin5": 20, "getin12": 0, "getin11": 0, "getin2": 0, "getin9": 0, "group": 1}, {"getin10": 0, "getin3": 20, "round": 1, "getin8": 0, "getin6": 0, "getin4": 75, "getin7": 0, "turn": 5, "getin1": 0, "getin5": 100, "getin12": 0, "getin11": 0, "getin2": 0, "getin9": 0, "group": 1}, {"getin10": 0, "getin3": 0, "round": 1, "getin8": 100, "getin6": 20, "getin4": 0, "getin7": 75, "turn": 6, "getin1": 0, "getin5": 0, "getin12": 0, "getin11": 0, "getin2": 0, "getin9": 0, "group": 1}, {"getin10": 0, "getin3": 20, "round": 1, "getin8": 0, "getin6": 100, "getin4": 61, "getin7": 0, "turn": 7, "getin1": 0, "getin5": 64, "getin12": 0, "getin11": 0, "getin2": 10, "getin9": 0, "group": 1}, {"getin10": 0, "getin3": 0, "round": 1, "getin8": 75, "getin6": 0, "getin4": 0, "getin7": 20, "turn": 8, "getin1": 0, "getin5": 0, "getin12": 0, "getin11": 0, "getin2": 0, "getin9": 100, "group": 1}]}, "md5": "b158f9e994251e8262c3d4da3491728b"}, "pkRace": {"config": {"sTime": 1750384800, "rTime": 1750816800, "eTime": 1750644000, "include": ["pk_race#pk_race", "order_token_control#pk_race"], "order_token_control": [{"roundType": "down_1", "rewardsWeight": [{"Weight": 100, "Amount": 1, "Currency": "pkRace"}], "ratio": 0.33}], "pk_race": [{"target": 30, "round": 1, "playerNumWeight": [{"weight": 10, "group_type": "pk", "child_type": "easy"}, {"weight": 10, "group_type": "pk", "child_type": "normal"}, {"weight": 80, "group_type": "pk", "child_type": "fail"}], "rankReward1": [{"Amount": 30, "Currency": "energy"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Amount": 10, "Currency": "energy"}]}, {"target": 30, "uiCode": "dig_chest_2", "round": 2, "playerNum": [{"group_num": 1, "group_type": "pk"}], "playerNumWeight": [{"weight": 60, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 10, "group_type": "pk", "child_type": "hard"}], "rankReward1": [{"Amount": 1, "Currency": "greenbox_1"}], "roundRewards": [{"Amount": 20, "Currency": "skipprop"}], "rankReward2": [{"Amount": 1, "Currency": "cbox1_1"}]}, {"target": 50, "round": 3, "playerNumWeight": [{"weight": 30, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 30, "group_type": "pk", "child_type": "hard"}], "rankReward1": [{"Amount": 40, "Currency": "energy"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Amount": 15, "Currency": "energy"}]}, {"target": 50, "uiCode": "dig_chest_2", "round": 4, "playerNum": [{"group_num": 1, "group_type": "pk"}], "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 40, "group_type": "pk", "child_type": "normal"}, {"weight": 40, "group_type": "pk", "child_type": "hard"}], "rankReward1": [{"Amount": 40, "Currency": "energy"}], "roundRewards": [{"Amount": 1, "Currency": "additem_1"}], "rankReward2": [{"Amount": 15, "Currency": "energy"}]}, {"target": 70, "round": 5, "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward1": [{"Amount": 50, "Currency": "energy"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Amount": 20, "Currency": "energy"}]}, {"target": 70, "round": 6, "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward1": [{"Amount": 1, "Currency": "greenbox_1"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Amount": 1, "Currency": "cbox1_1"}]}, {"target": 100, "uiCode": "dig_chest_2", "round": 7, "playerNum": [{"group_num": 1, "group_type": "pk"}], "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward1": [{"Amount": 60, "Currency": "energy"}], "roundRewards": [{"Amount": 60, "Currency": "skipprop"}], "rankReward2": [{"Amount": 25, "Currency": "energy"}]}, {"target": 120, "round": 8, "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward1": [{"Amount": 80, "Currency": "energy"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Amount": 30, "Currency": "energy"}]}, {"target": 150, "round": 9, "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward1": [{"Amount": 1, "Currency": "greenbox2_1"}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward2": [{"Amount": 1, "Currency": "cbox2_1"}]}, {"target": 150, "uiCode": "dig_chest_3", "round": 10, "playerNum": [{"group_num": 1, "group_type": "pk"}], "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward1": [{"Amount": 80, "Currency": "energy"}], "roundRewards": [{"Amount": 150, "Currency": "energy"}], "rankReward2": [{"Amount": 30, "Currency": "energy"}]}], "id": 103, "sLv": 7}, "md5": "ae9c698ccb7304742486db23b78d9791"}, "flambeTime": {"md5": "b2c26e81d71d7d26b46e3b95e5f9b4a3", "config": {"sTime": 1750644000, "eTime": 1802570400, "sLv": 7, "id": 115, "generalActivityConf": [{"confType": "fromOrderGroupConfig", "param_int": 1}, {"confType": "linkInstruSpeed", "param_int": 10}, {"confType": "modeInstruSpeed", "param_int": 10}], "include": ["generalActivityConf#mode"]}}, "bp7": {"config": {"sTime": 1749607200, "rTime": 1752199200, "eTime": 1752026400, "sLv": 7, "generalActivityConf": [{"confType": "battlePassClone", "param_int": 2}], "battlePassReward": [{"golden_reward": [{"Amount": 150, "Currency": "energy"}], "level": 1, "reward": [{"Amount": 30, "Currency": "energy"}], "require": 0}, {"golden_reward": [{"Amount": 1, "Currency": "gem_2"}], "level": 2, "reward": [{"Amount": 1, "Currency": "it_1_2_2"}], "require": 60}, {"golden_reward": [{"Amount": 50, "Currency": "skipprop"}], "level": 3, "reward": [{"Amount": 1, "Currency": "ene_2"}], "require": 90}, {"golden_reward": [{"Amount": 1, "Currency": "additem_1"}], "level": 4, "reward": [{"Amount": 30, "Currency": "skipprop"}], "require": 120}, {"golden_reward": [{"Amount": 1, "Currency": "ene_3"}], "level": 5, "reward": [{"Amount": 1, "Currency": "it_1_1_5"}], "require": 150}, {"golden_reward": [{"Amount": 1, "Currency": "it_1_1_6"}], "level": 6, "reward": [{"Amount": 1, "Currency": "ene_2"}], "require": 180}, {"golden_reward": [{"Amount": 1, "Currency": "gem_2"}], "level": 7, "reward": [{"Amount": 1, "Currency": "freebox_1"}], "require": 220}, {"golden_reward": [{"Amount": 100, "Currency": "skipprop"}], "level": 8, "reward": [{"Amount": 1, "Currency": "additem_1"}], "require": 260}, {"golden_reward": [{"Amount": 2, "Currency": "additem_1"}], "level": 9, "reward": [{"Amount": 1, "Currency": "gem_1"}], "require": 280}, {"golden_reward": [{"Amount": 1, "Currency": "enebox_1"}], "level": 10, "reward": [{"Amount": 1, "Currency": "greenbox_1"}], "require": 340}, {"golden_reward": [{"Amount": 1, "Currency": "it_2_3_2"}], "level": 11, "reward": [{"Amount": 50, "Currency": "skipprop"}], "require": 380}, {"golden_reward": [{"Amount": 1, "Currency": "ene_3"}], "level": 12, "reward": [{"Amount": 1, "Currency": "it_2_3_1"}], "require": 410}, {"golden_reward": [{"Amount": 1, "Currency": "gem_2"}], "level": 13, "reward": [{"Amount": 1, "Currency": "gem_1"}], "require": 460}, {"golden_reward": [{"Amount": 100, "Currency": "skipprop"}], "level": 14, "reward": [{"Amount": 1, "Currency": "ene_2"}], "require": 500}, {"golden_reward": [{"Amount": 1, "Currency": "ene_4"}], "level": 15, "reward": [{"Amount": 1, "Currency": "freebox_1"}], "require": 540}, {"golden_reward": [{"Amount": 1, "Currency": "freebox_1"}], "level": 16, "reward": [{"Amount": 1, "Currency": "additem_1"}], "require": 580}, {"golden_reward": [{"Amount": 1, "Currency": "ene_4"}], "level": 17, "reward": [{"Amount": 50, "Currency": "skipprop"}], "require": 640}, {"golden_reward": [{"Amount": 1, "Currency": "it_3_1_7"}], "level": 18, "reward": [{"Amount": 1, "Currency": "gem_2"}], "require": 700}, {"golden_reward": [{"Amount": 2, "Currency": "additem_1"}], "level": 19, "reward": [{"Amount": 1, "Currency": "it_3_1_6"}], "require": 780}, {"golden_reward": [{"Amount": 50, "Currency": "energy"}], "level": 20, "reward": [{"Amount": 30, "Currency": "energy"}], "require": 850}, {"golden_reward": [{"Amount": 10, "Currency": "gem"}], "level": 21, "reward": [{"Amount": 1, "Currency": "ene_3"}], "require": 940}, {"golden_reward": [{"Amount": 1, "Currency": "ene_5"}], "level": 22, "reward": [{"Amount": 1, "Currency": "gem_2"}], "require": 1020}, {"golden_reward": [{"Amount": 1, "Currency": "it_3_2_5"}], "level": 23, "reward": [{"Amount": 1, "Currency": "enebox_1"}], "require": 1120}, {"golden_reward": [{"Amount": 2, "Currency": "additem_1"}], "level": 24, "reward": [{"Amount": 1, "Currency": "additem_1"}], "require": 1220}, {"golden_reward": [{"Amount": 100, "Currency": "skipprop"}], "level": 25, "reward": [{"Amount": 50, "Currency": "skipprop"}], "require": 1320}, {"golden_reward": [{"Amount": 1, "Currency": "enebox_1"}], "level": 26, "reward": [{"Amount": 1, "Currency": "it_3_2_4"}], "require": 1430}, {"golden_reward": [{"Amount": 1, "Currency": "gem_3"}], "level": 27, "reward": [{"Amount": 1, "Currency": "gem_2"}], "require": 1540}, {"golden_reward": [{"Amount": 1, "Currency": "it_4_2_5"}], "level": 28, "reward": [{"Amount": 1, "Currency": "ene_4"}], "require": 1650}, {"golden_reward": [{"Amount": 1, "Currency": "ene_4"}], "level": 29, "reward": [{"Amount": 1, "Currency": "it_4_2_4"}], "require": 1760}, {"golden_reward": [{"Amount": 15, "Currency": "gem"}], "level": 30, "reward": [{"Amount": 1, "Currency": "additem_1"}], "require": 1880}, {"golden_reward": [{"Amount": 1, "Currency": "enebox_1"}, {"Amount": 1, "Currency": "greenbox_1"}], "level": 31, "reward": [{"Amount": 1, "Currency": "gem_3"}, {"Amount": 1, "Currency": "greenbox_1"}], "require": 2000}, {"golden_reward": [{"Amount": 1, "Currency": "ene_5"}], "level": 32, "reward": [{"Amount": 1, "Currency": "ene_4"}], "require": 2120}, {"golden_reward": [{"Amount": 1, "Currency": "gem_4"}], "level": 33, "reward": [{"Amount": 50, "Currency": "skipprop"}], "require": 2220}, {"golden_reward": [{"Amount": 100, "Currency": "skipprop"}], "level": 34, "reward": [{"Amount": 1, "Currency": "gem_4"}], "require": 2360}, {"golden_reward": [{"Amount": 25, "Currency": "gem"}], "level": 35, "reward": [{"Amount": 1, "Currency": "enebox_1"}], "require": 2400}, {"golden_reward": [{"Amount": 100, "Currency": "energy"}, {"Amount": 1, "Currency": "skiptime_1"}, {"Amount": 1, "Currency": "greenbox_1"}], "level": 36, "reward": [{"Amount": 1, "Currency": "skiptime_1"}], "require": 2480}], "id": 99, "include": ["battlePassReward#test1", "battlePassTask#test1", "generalActivityConf#bpClone"], "battlePassTask": [{"order": 1, "tasks": [{"tokenNum": 40, "count": 2, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 25, "count": 5, "type": "gem"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}], "time": 1, "type": "timelimit"}, {"order": 2, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 50, "count": 500, "type": "gold"}, {"tokenNum": 60, "count": 600, "type": "gold"}, {"tokenNum": 70, "count": 700, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 100, "count": 1000, "type": "gold"}, {"tokenNum": 120, "count": 1200, "type": "gold"}], "time": 2, "type": "timelimit"}, {"order": 3, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 45, "count": 450, "type": "gold"}, {"tokenNum": 50, "count": 500, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 25, "count": 5, "type": "gem"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}], "time": 3, "type": "timelimit"}, {"order": 4, "tasks": [{"tokenNum": 40, "count": 2, "type": "customer"}, {"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 60, "count": 600, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 110, "count": 1100, "type": "gold"}], "time": 4, "type": "timelimit"}, {"order": 5, "tasks": [{"tokenNum": 40, "count": 2, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 140, "count": 7, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 60, "count": 600, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 120, "count": 1200, "type": "gold"}], "time": 5, "type": "timelimit"}, {"order": 6, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 140, "count": 7, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 220, "count": 11, "type": "customer"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}, {"tokenNum": 100, "count": 20, "type": "gem"}], "time": 6, "type": "timelimit"}, {"order": 7, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 50, "count": 500, "type": "gold"}, {"tokenNum": 60, "count": 600, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 90, "count": 900, "type": "gold"}, {"tokenNum": 110, "count": 1100, "type": "gold"}, {"tokenNum": 130, "count": 1300, "type": "gold"}], "time": 7, "type": "timelimit"}, {"order": 8, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 50, "count": 500, "type": "gold"}, {"tokenNum": 65, "count": 650, "type": "gold"}, {"tokenNum": 100, "count": 1000, "type": "gold"}, {"tokenNum": 25, "count": 5, "type": "gem"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}], "time": 8, "type": "timelimit"}, {"order": 9, "tasks": [{"tokenNum": 40, "count": 2, "type": "customer"}, {"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 140, "count": 7, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 90, "count": 900, "type": "gold"}, {"tokenNum": 120, "count": 1200, "type": "gold"}], "time": 9, "type": "timelimit"}, {"order": 10, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 45, "count": 450, "type": "gold"}, {"tokenNum": 60, "count": 600, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 25, "count": 5, "type": "gem"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}], "time": 10, "type": "timelimit"}, {"order": 11, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 90, "count": 900, "type": "gold"}, {"tokenNum": 120, "count": 1200, "type": "gold"}], "time": 11, "type": "timelimit"}, {"order": 12, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 220, "count": 11, "type": "customer"}, {"tokenNum": 60, "count": 600, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 100, "count": 1000, "type": "gold"}], "time": 12, "type": "timelimit"}, {"order": 13, "tasks": [{"tokenNum": 40, "count": 2, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 140, "count": 7, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 70, "count": 700, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 110, "count": 1100, "type": "gold"}], "time": 13, "type": "timelimit"}, {"order": 14, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 140, "count": 7, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 95, "count": 950, "type": "gold"}, {"tokenNum": 110, "count": 1100, "type": "gold"}], "time": 14, "type": "timelimit"}, {"order": 15, "tasks": [{"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 140, "count": 7, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 220, "count": 11, "type": "customer"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 90, "count": 900, "type": "gold"}, {"tokenNum": 120, "count": 1200, "type": "gold"}], "time": 15, "type": "timelimit"}, {"order": 16, "tasks": [{"tokenNum": 40, "count": 2, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 220, "count": 11, "type": "customer"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 90, "count": 900, "type": "gold"}, {"tokenNum": 120, "count": 1200, "type": "gold"}], "time": 16, "type": "timelimit"}, {"order": 17, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}, {"tokenNum": 100, "count": 20, "type": "gem"}], "time": 17, "type": "timelimit"}, {"order": 18, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 50, "count": 500, "type": "gold"}, {"tokenNum": 60, "count": 600, "type": "gold"}, {"tokenNum": 85, "count": 850, "type": "gold"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}, {"tokenNum": 100, "count": 20, "type": "gem"}], "time": 18, "type": "timelimit"}, {"order": 19, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 60, "count": 600, "type": "gold"}, {"tokenNum": 70, "count": 700, "type": "gold"}, {"tokenNum": 100, "count": 1000, "type": "gold"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}, {"tokenNum": 100, "count": 20, "type": "gem"}], "time": 19, "type": "timelimit"}, {"order": 20, "tasks": [{"tokenNum": 40, "count": 2, "type": "customer"}, {"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 140, "count": 7, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 70, "count": 700, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 90, "count": 900, "type": "gold"}], "time": 20, "type": "timelimit"}, {"order": 21, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 60, "count": 600, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 100, "count": 1000, "type": "gold"}], "time": 21, "type": "timelimit"}, {"order": 22, "tasks": [{"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}, {"tokenNum": 100, "count": 20, "type": "gem"}], "time": 22, "type": "timelimit"}, {"order": 23, "tasks": [{"tokenNum": 40, "count": 2, "type": "customer"}, {"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 140, "count": 7, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 70, "count": 700, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 100, "count": 1000, "type": "gold"}], "time": 23, "type": "timelimit"}, {"order": 24, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 45, "count": 450, "type": "gold"}, {"tokenNum": 50, "count": 500, "type": "gold"}, {"tokenNum": 80, "count": 600, "type": "gold"}, {"tokenNum": 70, "count": 700, "type": "gold"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 95, "count": 950, "type": "gold"}], "time": 24, "type": "timelimit"}, {"order": 25, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 50, "count": 500, "type": "gold"}, {"tokenNum": 65, "count": 650, "type": "gold"}, {"tokenNum": 85, "count": 850, "type": "gold"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}, {"tokenNum": 100, "count": 20, "type": "gem"}], "time": 25, "type": "timelimit"}, {"order": 26, "tasks": [{"tokenNum": 80, "count": 4, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 140, "count": 7, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 220, "count": 11, "type": "customer"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 90, "count": 900, "type": "gold"}, {"tokenNum": 100, "count": 1000, "type": "gold"}], "time": 26, "type": "timelimit"}, {"order": 27, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 80, "count": 800, "type": "gold"}, {"tokenNum": 90, "count": 900, "type": "gold"}, {"tokenNum": 100, "count": 1000, "type": "gold"}], "time": 27, "type": "timelimit"}, {"order": 28, "tasks": [{"tokenNum": 60, "count": 3, "type": "customer"}, {"tokenNum": 100, "count": 5, "type": "customer"}, {"tokenNum": 120, "count": 6, "type": "customer"}, {"tokenNum": 160, "count": 8, "type": "customer"}, {"tokenNum": 180, "count": 9, "type": "customer"}, {"tokenNum": 200, "count": 10, "type": "customer"}, {"tokenNum": 50, "count": 10, "type": "gem"}, {"tokenNum": 75, "count": 15, "type": "gem"}, {"tokenNum": 100, "count": 20, "type": "gem"}], "time": 28, "type": "timelimit"}, {"order": 29, "tasks": [{"tokenNum": 100, "count": 100, "type": "merge"}], "time": 0, "type": "cycle"}]}, "md5": "e640c564ebec6fa5ac8a49b767eb7310"}, "fileReplace": {"config": [{"id": 35, "suffix": "_energy", "file": "ExtraBoardItemModelConfig"}], "md5": "2b8d9fe1c1b9776da07120db730897c2"}}, "key": "key"}}, "bundles": {"cdFill": {"name": "cdFill", "data": "{\"timespeed_1\":{\"data\":\"{\\\"tgDailyShowNum_pd_cd_number\\\":{\\\"value\\\":1},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20261\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20261\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"sTime\\\\\\\":1749434400@\\\\\\\"dailyBuyNum\\\\\\\":2@\\\\\\\"order\\\\\\\":[\\\\\\\"timespeed_1999\\\\\\\"]@\\\\\\\"uiCode\\\\\\\":\\\\\\\"cdFill\\\\\\\"@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popOrder\\\\\\\":[\\\\\\\"cdFill\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"pd_cd_number\\\\\\\"}]@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#timespeed_1999\\\\\\\"@\\\\\\\"bundleTrigger#pd_cd_number\\\\\\\"@\\\\\\\"generalBundleConf#pd_cd_number\\\\\\\"]@\\\\\\\"id\\\\\\\":81@\\\\\\\"duration\\\\\\\":360@\\\\\\\"groupId\\\\\\\":\\\\\\\"timespeed_1\\\\\\\"@\\\\\\\"eTime\\\\\\\":1765411200@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"price\\\\\\\":19.99@\\\\\\\"payID\\\\\\\":\\\\\\\"cd_speed_1999\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"timespeed_1999\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":800@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":300@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"timespeed_1\\\\\\\"}]}]@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"popCD\\\\\\\":60@\\\\\\\"dailyShowNum\\\\\\\":10@\\\\\\\"generalBundleConf\\\\\\\":[{\\\\\\\"confType\\\\\\\":\\\\\\\"pd_cd_number\\\\\\\"@\\\\\\\"param_int\\\\\\\":4}]@\\\\\\\"specialType\\\\\\\":\\\\\\\"cdFill\\\\\\\"@\\\\\\\"buyCD\\\\\\\":30}\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1750607429\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1750608002\\\"}}\"}}"}, "multiTier": {"name": "multiTier", "data": "{\"energytier1\":{\"data\":\"{\\\"dailyBuyNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20261\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20261\\\"},\\\"curGearBundleId\\\":{\\\"value\\\":\\\"et9.99\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1750607670\\\"},\\\"missedNum\\\":{\\\"value\\\":\\\"0\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"sTime\\\\\\\":1742522400@\\\\\\\"dailyBuyNum\\\\\\\":5@\\\\\\\"order\\\\\\\":[\\\\\\\"et5.99\\\\\\\"@\\\\\\\"et9.99\\\\\\\"@\\\\\\\"et15.99\\\\\\\"@\\\\\\\"et19.99\\\\\\\"]@\\\\\\\"uiCode\\\\\\\":\\\\\\\"multiTier\\\\\\\"@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"trigger\\\\\\\":\\\\\\\"lack_energy\\\\\\\"@\\\\\\\"popNum\\\\\\\":1@\\\\\\\"popOrder\\\\\\\":[\\\\\\\"energy\\\\\\\"]}]@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#et5.99\\\\\\\"@\\\\\\\"bundleContent#et9.99\\\\\\\"@\\\\\\\"bundleContent#et15.99\\\\\\\"@\\\\\\\"bundleContent#et19.99\\\\\\\"@\\\\\\\"bundleTrigger#lack_energy\\\\\\\"@\\\\\\\"generalBundleConf#15.99\\\\\\\"]@\\\\\\\"id\\\\\\\":25@\\\\\\\"duration\\\\\\\":30@\\\\\\\"groupId\\\\\\\":\\\\\\\"energytier1\\\\\\\"@\\\\\\\"eTime\\\\\\\":1765411200@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"price\\\\\\\":5.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et5.99\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_599\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":820@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"discountTag\\\\\\\":\\\\\\\"140%\\\\\\\"@\\\\\\\"originPrice\\\\\\\":8.99}@{\\\\\\\"price\\\\\\\":9.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et9.99\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_999\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1400@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"discountTag\\\\\\\":\\\\\\\"150%\\\\\\\"@\\\\\\\"originPrice\\\\\\\":14.99}@{\\\\\\\"price\\\\\\\":15.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et15.99\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_1599\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":2300@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"discountTag\\\\\\\":\\\\\\\"160%\\\\\\\"@\\\\\\\"originPrice\\\\\\\":23.99}@{\\\\\\\"price\\\\\\\":19.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et19.99\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_1999\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":3200@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"discountTag\\\\\\\":\\\\\\\"170%\\\\\\\"@\\\\\\\"originPrice\\\\\\\":34.99}]@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"popCD\\\\\\\":5@\\\\\\\"dailyShowNum\\\\\\\":10@\\\\\\\"generalBundleConf\\\\\\\":[{\\\\\\\"confType\\\\\\\":\\\\\\\"defaultOrder\\\\\\\"@\\\\\\\"param_string\\\\\\\":\\\\\\\"et15.99\\\\\\\"}]@\\\\\\\"specialType\\\\\\\":\\\\\\\"multiTier\\\\\\\"@\\\\\\\"buyCD\\\\\\\":30}\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1750607670\\\"},\\\"lastGear\\\":{\\\"value\\\":\\\"0\\\"},\\\"tgDailyShowNum_lack_energy\\\":{\\\"value\\\":1}}\"}}"}, "cd": {"name": "cd", "data": "{}"}, "orderGroup": {"name": "orderGroup", "data": "{\"order1\":{\"data\":\"{\\\"triggerTime\\\":{\\\"value\\\":\\\"1750606737\\\"},\\\"dailyBuyNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20261\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20261\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"sTime\\\\\\\":1741053600@\\\\\\\"dailyBuyNum\\\\\\\":2@\\\\\\\"order\\\\\\\":[\\\\\\\"order_1999\\\\\\\"]@\\\\\\\"uiCode\\\\\\\":\\\\\\\"orderGroup\\\\\\\"@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"trigger\\\\\\\":\\\\\\\"finish_order_group\\\\\\\"@\\\\\\\"popNum\\\\\\\":1@\\\\\\\"popOrder\\\\\\\":[\\\\\\\"orderGroup\\\\\\\"]}]@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#order_1999\\\\\\\"@\\\\\\\"bundleTrigger#finish_order_group\\\\\\\"]@\\\\\\\"id\\\\\\\":19@\\\\\\\"duration\\\\\\\":60@\\\\\\\"groupId\\\\\\\":\\\\\\\"order1\\\\\\\"@\\\\\\\"eTime\\\\\\\":1765411200@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"price\\\\\\\":19.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"order_1999\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":560@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":1400@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":2@\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox_1\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":3@\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"}]@\\\\\\\"discountTag\\\\\\\":\\\\\\\"150%\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"finish_order_1999\\\\\\\"}]@\\\\\\\"popCD\\\\\\\":60@\\\\\\\"dailyShowNum\\\\\\\":5@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"specialType\\\\\\\":\\\\\\\"orderGroup\\\\\\\"@\\\\\\\"buyCD\\\\\\\":30}\\\"},\\\"tgDailyShowNum_finish_order_group\\\":{\\\"value\\\":1},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1750607957\\\"}}\"}}"}, "chain": {"name": "chain", "data": "{\"chain7\":{\"data\":\"{\\\"tgDailyShowNum_login\\\":{\\\"value\\\":2},\\\"triggerTime\\\":{\\\"value\\\":\\\"1750385712\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20261\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20261\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"2\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"groupId\\\\\\\":\\\\\\\"chain7\\\\\\\"@\\\\\\\"rTime\\\\\\\":1750730400@\\\\\\\"eTime\\\\\\\":1750644000@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"uiCode\\\\\\\":\\\\\\\"chain\\\\\\\"@\\\\\\\"dailyShowNum\\\\\\\":2@\\\\\\\"bundleContentChain\\\\\\\":[{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_1\\\\\\\"@\\\\\\\"step\\\\\\\":1@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":5@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_2\\\\\\\"@\\\\\\\"step\\\\\\\":2@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":10@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_3\\\\\\\"@\\\\\\\"step\\\\\\\":3@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_1\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":1.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_4\\\\\\\"@\\\\\\\"step\\\\\\\":4@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":30@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":150@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_199\\\\\\\"}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_5\\\\\\\"@\\\\\\\"step\\\\\\\":5@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":15@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_6\\\\\\\"@\\\\\\\"step\\\\\\\":6@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_2\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":2.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_7\\\\\\\"@\\\\\\\"step\\\\\\\":7@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":40@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":200@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_299\\\\\\\"}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_8\\\\\\\"@\\\\\\\"step\\\\\\\":8@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_1\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_9\\\\\\\"@\\\\\\\"step\\\\\\\":9@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":30@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_10\\\\\\\"@\\\\\\\"step\\\\\\\":10@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":10@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_11\\\\\\\"@\\\\\\\"step\\\\\\\":11@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_3\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":3.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_12\\\\\\\"@\\\\\\\"step\\\\\\\":12@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":60@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":250@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_399\\\\\\\"}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_13\\\\\\\"@\\\\\\\"step\\\\\\\":13@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":40@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_14\\\\\\\"@\\\\\\\"step\\\\\\\":14@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox_1\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_15\\\\\\\"@\\\\\\\"step\\\\\\\":15@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":50@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_16\\\\\\\"@\\\\\\\"step\\\\\\\":16@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_4\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":4.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_17\\\\\\\"@\\\\\\\"step\\\\\\\":17@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":80@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":300@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_499\\\\\\\"}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_18\\\\\\\"@\\\\\\\"step\\\\\\\":18@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":60@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_19\\\\\\\"@\\\\\\\"step\\\\\\\":19@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":15@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_20\\\\\\\"@\\\\\\\"step\\\\\\\":20@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":70@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_21\\\\\\\"@\\\\\\\"step\\\\\\\":21@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":6.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_22\\\\\\\"@\\\\\\\"step\\\\\\\":22@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":100@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":450@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_699\\\\\\\"}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_23\\\\\\\"@\\\\\\\"step\\\\\\\":23@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":100@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_24\\\\\\\"@\\\\\\\"step\\\\\\\":24@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_25\\\\\\\"@\\\\\\\"step\\\\\\\":25@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":150@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_26\\\\\\\"@\\\\\\\"step\\\\\\\":26@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":20@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":15.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_27\\\\\\\"@\\\\\\\"step\\\\\\\":27@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":240@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":1000@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_1599\\\\\\\"}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_28\\\\\\\"@\\\\\\\"step\\\\\\\":28@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_29\\\\\\\"@\\\\\\\"step\\\\\\\":29@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":200@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_30\\\\\\\"@\\\\\\\"step\\\\\\\":30@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_3\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_31\\\\\\\"@\\\\\\\"step\\\\\\\":31@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":400@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_32\\\\\\\"@\\\\\\\"step\\\\\\\":32@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":50@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":19.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_33\\\\\\\"@\\\\\\\"step\\\\\\\":33@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":320@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":1200@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_1999\\\\\\\"}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_34\\\\\\\"@\\\\\\\"step\\\\\\\":34@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":200@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_35\\\\\\\"@\\\\\\\"step\\\\\\\":35@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":2@\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_36\\\\\\\"@\\\\\\\"step\\\\\\\":36@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":400@\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_37\\\\\\\"@\\\\\\\"step\\\\\\\":37@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":2@\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_3\\\\\\\"}]}@{\\\\\\\"price\\\\\\\":0@\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_38\\\\\\\"@\\\\\\\"step\\\\\\\":38@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":100@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}]}]@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"trigger\\\\\\\":\\\\\\\"login\\\\\\\"}]@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContentChain#chain_skip\\\\\\\"@\\\\\\\"bundleTrigger#login\\\\\\\"]@\\\\\\\"id\\\\\\\":83@\\\\\\\"duration\\\\\\\":4320@\\\\\\\"specialType\\\\\\\":\\\\\\\"chain\\\\\\\"@\\\\\\\"sTime\\\\\\\":1750384800}\\\"},\\\"curIndex\\\":{\\\"value\\\":\\\"4\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1750606785\\\"}}\"}}"}, "starter": {"name": "starter", "data": "{\"starter\":{\"data\":\"{\\\"maxBuyNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20234\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20234\\\"},\\\"lastBuyDay\\\":{\\\"value\\\":\\\"20234\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1748242899\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"groupId\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"maxBuyNum\\\\\\\":1@\\\\\\\"popCD\\\\\\\":360@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"bundleCondition\\\\\\\":[{\\\\\\\"taskFinished\\\\\\\":{\\\\\\\"ChapterId\\\\\\\":2@\\\\\\\"TaskCount\\\\\\\":3}}]@\\\\\\\"uiCode\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"order\\\\\\\":[\\\\\\\"starter_499\\\\\\\"]@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"price\\\\\\\":4.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"starter_499\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Amount\\\\\\\":240@\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":200@\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":4@\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_1\\\\\\\"}@{\\\\\\\"Amount\\\\\\\":1@\\\\\\\"Currency\\\\\\\":\\\\\\\"enebox_1\\\\\\\"}]@\\\\\\\"discountTag\\\\\\\":\\\\\\\"200%\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"starter_bundle_1\\\\\\\"}]@\\\\\\\"id\\\\\\\":1@\\\\\\\"dailyBuyNum\\\\\\\":1@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"trigger\\\\\\\":\\\\\\\"task_finished\\\\\\\"@\\\\\\\"popNum\\\\\\\":1@\\\\\\\"popOrder\\\\\\\":[\\\\\\\"starter\\\\\\\"]}]@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#starter_499\\\\\\\"@\\\\\\\"bundleTrigger#task_finished\\\\\\\"@\\\\\\\"bundleCondition#starter_499\\\\\\\"]@\\\\\\\"dailyShowNum\\\\\\\":2@\\\\\\\"duration\\\\\\\":720@\\\\\\\"specialType\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"buyCD\\\\\\\":10080}\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"dailyBuyNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"tgDailyShowNum_task_finished\\\":{\\\"value\\\":1},\\\"triggerTime\\\":{\\\"value\\\":\\\"1748199699\\\"}}\"}}"}}, "taskMeta": {"TaskCleanGold": {"value": 2443, "key": "TaskCleanGold"}, "OngoingChapterId": {"value": 5, "key": "OngoingChapterId"}, "ChapterFinishWin": {"value": "4", "key": "ChapterFinishWin"}, "OngoingTaskIds": {"value": "11", "key": "OngoingTaskIds"}, "TaskProgress": {"value": 5010, "key": "TaskProgress"}, "CleanGoldCost": {"value": "1#50;2#45;3#39;4#39;5#45;6#48;7#50;8#45;9#53;10#43;11#43;12#50;13#50;14#50;15#46;16#53;17#48;18#51;19#48;20#57;21#51;22#54;23#48;24#54;25#53;26#64;27#50;28#53;29#42;30#57;31#57;32#61;33#58;34#58;35#58;36#65;37#62;38#54;39#54;40#51;41#68;42#64;43#57;44#61;45#49;46#68;47#64;48#39", "key": "CleanGoldCost"}, "OngoingChapterName": {"value": "Wine", "key": "OngoingChapterName"}}, "energy": {"2": {"id": "2", "energyValue": 100}, "1": {"id": "1", "energyValue": 100, "generateTime": 1750645024}}, "orderMeta": {"CurOrderGroupId": {"value": "3", "key": "CurOrderGroupId"}, "TotalFinishedGroupCount": {"value": "55", "key": "TotalFinishedGroupCount"}, "OrderGroupCostCurDayEnergy": {"value": "64", "key": "OrderGroupCostCurDayEnergy"}, "CurChapterId": {"value": "5", "key": "CurChapterId"}, "OrderGroupConsumeEnergy": {"value": "568", "key": "OrderGroupConsumeEnergy"}, "CurGroupFinishedOrderIds": {"value": "50150;50190;50180;50170;", "key": "CurGroupFinishedOrderIds"}, "SecondOrder": {"value": "1", "key": "SecondOrder"}, "FirstOrder": {"value": "1", "key": "FirstOrder"}, "CurFinishedGroupCount": {"value": "55", "key": "CurFinishedGroupCount"}, "OrderGroupCostPastDayEnergy": {"value": "148", "key": "OrderGroupCostPastDayEnergy"}}, "activity": {"bakeOut": {"name": "bakeOut", "data": "{\"cachedDataMd5\":{\"value\":\"589a1da32255775b93d7f2ace5305150\"},\"cachedData\":{\"value\":\"{\\\"staticInclude\\\":[\\\"bakeout_rank_reward#1st\\\",\\\"bakeout_rank_exchange#default\\\",\\\"bakeout_rank_parameter#1st\\\"],\\\"id\\\":100022,\\\"bakeout_rank_parameter\\\":[{\\\"maxNum\\\":20,\\\"delayTime\\\":90,\\\"noRegisterTime\\\":300,\\\"settlementTime\\\":300,\\\"retainTime\\\":601200,\\\"uploadTime\\\":60}],\\\"bakeout_rank_exchange\\\":[{\\\"cost\\\":10,\\\"time\\\":1},{\\\"cost\\\":11,\\\"time\\\":2},{\\\"cost\\\":14,\\\"time\\\":3},{\\\"cost\\\":17,\\\"time\\\":4},{\\\"cost\\\":21,\\\"time\\\":5},{\\\"cost\\\":28,\\\"time\\\":6},{\\\"cost\\\":37,\\\"time\\\":7},{\\\"cost\\\":51,\\\"time\\\":8},{\\\"cost\\\":72,\\\"time\\\":9},{\\\"cost\\\":100,\\\"time\\\":10}],\\\"eTime\\\":1750903200,\\\"bakeout_rank_reward\\\":[{\\\"end_rank\\\":1,\\\"start_rank\\\":1,\\\"rewards\\\":[\\\"gem-25\\\",\\\"energy-100\\\",\\\"skiptime_1-1\\\"]},{\\\"end_rank\\\":2,\\\"start_rank\\\":2,\\\"rewards\\\":[\\\"gem-20\\\",\\\"energy-80\\\",\\\"additem_1-1\\\"]},{\\\"end_rank\\\":3,\\\"start_rank\\\":3,\\\"rewards\\\":[\\\"gem-15\\\",\\\"energy-50\\\"]},{\\\"end_rank\\\":6,\\\"start_rank\\\":4,\\\"rewards\\\":[\\\"gem-10\\\"]},{\\\"end_rank\\\":10,\\\"start_rank\\\":7,\\\"rewards\\\":[\\\"energy-50\\\"]},{\\\"end_rank\\\":15,\\\"start_rank\\\":11,\\\"rewards\\\":[\\\"skipprop-50\\\"]},{\\\"end_rank\\\":20,\\\"start_rank\\\":16,\\\"rewards\\\":[\\\"skipprop-25\\\"]}],\\\"sTime\\\":1750299000,\\\"rTime\\\":1750903800}\"},\"id\":{\"value\":100022},\"delayedGetRewardTime\":{\"value\":77}}"}, "coinRace": {"name": "coinRace", "data": "{\"windowOpened2\":{\"value\":1},\"lastScore\":{\"value\":\"{\\\"10000064358\\\":0,\\\"10000064352\\\":0,\\\"10000064364\\\":3,\\\"1\\\":0,\\\"10000064370\\\":7,\\\"round\\\":1}\"},\"playerDatas\":{\"value\":\"[{\\\"name\\\":\\\"G4QRJAH\\\",\\\"group_type\\\":\\\"new_fail\\\",\\\"track\\\":4,\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":186,\\\\\\\"seconds\\\\\\\":82},{\\\\\\\"score\\\\\\\":372,\\\\\\\"seconds\\\\\\\":203140}]\\\",\\\"id\\\":10000064352,\\\"icon\\\":\\\"head4\\\"},{\\\"name\\\":\\\"WK8RJAH\\\",\\\"group_type\\\":\\\"new_fail\\\",\\\"track\\\":5,\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":76,\\\\\\\"seconds\\\\\\\":39},{\\\\\\\"score\\\\\\\":287,\\\\\\\"seconds\\\\\\\":113522},{\\\\\\\"score\\\\\\\":387,\\\\\\\"seconds\\\\\\\":117135}]\\\",\\\"id\\\":10000064364,\\\"icon\\\":\\\"head3\\\"},{\\\"name\\\":\\\"PZPRJAH\\\",\\\"group_type\\\":\\\"new_suc_normal\\\",\\\"track\\\":2,\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":97,\\\\\\\"seconds\\\\\\\":16390},{\\\\\\\"score\\\\\\\":216,\\\\\\\"seconds\\\\\\\":35416},{\\\\\\\"score\\\\\\\":470,\\\\\\\"seconds\\\\\\\":35428}]\\\",\\\"id\\\":10000064358,\\\"icon\\\":\\\"head7\\\"},{\\\"name\\\":\\\"JI9RTAH\\\",\\\"group_type\\\":\\\"new_suc_normal\\\",\\\"track\\\":1,\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":157,\\\\\\\"seconds\\\\\\\":45},{\\\\\\\"score\\\\\\\":343,\\\\\\\"seconds\\\\\\\":331},{\\\\\\\"score\\\\\\\":437,\\\\\\\"seconds\\\\\\\":540},{\\\\\\\"score\\\\\\\":512,\\\\\\\"seconds\\\\\\\":14061}]\\\",\\\"id\\\":10000064370,\\\"icon\\\":\\\"head6\\\"}]\"},\"entryTime\":{\"value\":1750645037},\"id\":{\"value\":112},\"round\":{\"value\":1}}"}, "BlindChest1": {"name": "BlindChest1", "data": "{\"windowOpened2\":{\"value\":1},\"KeyCount\":{\"value\":0},\"SlotOpened4\":{\"value\":true},\"ath_o_50200\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"blindChest1\\\"@\\\"Amount\\\":1}\"},\"SlotOpened7\":{\"value\":false},\"SlotOpened5\":{\"value\":true},\"hasAddToken\":{\"value\":1},\"SlotOpened8\":{\"value\":false},\"ath_o_50210\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"blindChest1\\\"@\\\"Amount\\\":2}\"},\"Group\":{\"value\":1},\"RewardTaken6\":{\"value\":false},\"ath_o_50160\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"blindChest1\\\"@\\\"Amount\\\":1}\"},\"RewardTaken2\":{\"value\":false},\"SlotOpened3\":{\"value\":false},\"windowOpened3\":{\"value\":1},\"SlotOpened6\":{\"value\":true},\"SlotOpened2\":{\"value\":false},\"Turn\":{\"value\":6},\"id\":{\"value\":106},\"Round\":{\"value\":1},\"TurnCount\":{\"value\":5},\"SlotOpened9\":{\"value\":true},\"RewardTaken5\":{\"value\":true},\"SlotOpened10\":{\"value\":false},\"RewardTaken4\":{\"value\":false},\"RewardTaken1\":{\"value\":true},\"RewardTaken3\":{\"value\":false},\"SlotOpened1\":{\"value\":false}}"}, "extraBoard6": {"name": "extraBoard6", "data": "{\"windowOpened2\":{\"value\":1},\"ath_o_50160\":{\"value\":\"{\\\"Weight\\\":100@\\\"Amount\\\":3@\\\"Currency\\\":\\\"eb_1_2\\\"}\"},\"ath_o_50210\":{\"value\":\"{\\\"Weight\\\":100@\\\"Amount\\\":2@\\\"Currency\\\":\\\"eb_1_3\\\"}\"},\"id\":{\"value\":109},\"extraBoardItemGived\":{\"value\":1},\"ath_o_50200\":{\"value\":\"{\\\"Weight\\\":100@\\\"Amount\\\":3@\\\"Currency\\\":\\\"eb_1_2\\\"}\"}}"}, "album1": {"name": "album1", "data": "{\"cacheProperty\":{\"value\":\"{}\"}}"}, "BP7": {"name": "BP7", "data": "{\"RewardEffect19_0\":{\"value\":1},\"ProgressEffect_20\":{\"value\":1},\"RewardTaken22_0\":{\"value\":1},\"TimelimitTaskFinishedCount3\":{\"value\":0},\"ProgressEffect_18\":{\"value\":1},\"RewardEffect21_0\":{\"value\":1},\"TimelimitTaskFinished9\":{\"value\":0},\"ProgressEffect_7\":{\"value\":1},\"ProgressEffect_10\":{\"value\":1},\"TimelimitTaskFinishedCount7\":{\"value\":0},\"TimelimitTaskFinished7\":{\"value\":0},\"RewardEffect9_0\":{\"value\":1},\"RewardTaken6_0\":{\"value\":1},\"ProgressEffect_12\":{\"value\":1},\"ProgressEffect_9\":{\"value\":1},\"ProgressEffect_17\":{\"value\":1},\"windowOpenedBP7MainWindow\":{\"value\":1},\"TimelimitTaskFinished2\":{\"value\":0},\"RewardEffect22_0\":{\"value\":1},\"CycleTaskFinishedCount\":{\"value\":10},\"TimelimitTaskFinished8\":{\"value\":0},\"ProgressEffect_6\":{\"value\":1},\"TimelimitTaskFinishedCount2\":{\"value\":0},\"RewardTaken14_0\":{\"value\":1},\"ProgressEffect_8\":{\"value\":1},\"RewardTaken20_0\":{\"value\":1},\"TimelimitTaskFinished6\":{\"value\":0},\"TimelimitTaskOrder\":{\"value\":13},\"ProgressEffect_16\":{\"value\":1},\"TimelimitTaskFinishedCount5\":{\"value\":0},\"TimelimitTaskFinished4\":{\"value\":0},\"RewardTaken1_0\":{\"value\":1},\"TimelimitTaskFinishedCount8\":{\"value\":0},\"RewardTaken3_0\":{\"value\":1},\"RewardEffect15_0\":{\"value\":1},\"RewardEffect5_0\":{\"value\":1},\"ProgressEffect_13\":{\"value\":1},\"ProgressEffect_15\":{\"value\":1},\"RewardEffect16_0\":{\"value\":1},\"RewardEffect3_0\":{\"value\":1},\"FinishedLevel\":{\"value\":22},\"ProgressEffect_19\":{\"value\":1},\"RewardEffect6_0\":{\"value\":1},\"RewardEffect1_0\":{\"value\":1},\"ProgressEffect_2\":{\"value\":1},\"RewardEffect8_0\":{\"value\":1},\"RewardEffect20_0\":{\"value\":1},\"RewardEffect11_0\":{\"value\":1},\"TimelimitTaskFinishedCount4\":{\"value\":0},\"ProgressEffect_1\":{\"value\":1},\"ProgressEffect_4\":{\"value\":1},\"RewardEffect10_0\":{\"value\":1},\"CycleTaskIndex\":{\"value\":1},\"RewardTaken19_0\":{\"value\":1},\"ProgressEffect_11\":{\"value\":1},\"RewardEffect12_0\":{\"value\":1},\"RewardEffect7_0\":{\"value\":1},\"TokenNumber\":{\"value\":9760},\"TimelimitTaskFinishedCount9\":{\"value\":0},\"RewardEffect17_0\":{\"value\":1},\"FinTLtask\":{\"value\":59},\"id\":{\"value\":99},\"RewardEffect14_0\":{\"value\":1},\"windowOpened2\":{\"value\":1},\"ProgressEffect_5\":{\"value\":1},\"TimelimitTaskFinished5\":{\"value\":0},\"RewardEffect4_0\":{\"value\":1},\"ProgressEffect_3\":{\"value\":1},\"TimelimitTaskFinishedCount6\":{\"value\":0},\"RewardTaken21_0\":{\"value\":1},\"ProgressEffect_14\":{\"value\":1},\"TimelimitTaskFinished3\":{\"value\":0},\"RewardEffect18_0\":{\"value\":1},\"TimelimitTaskFinished1\":{\"value\":0},\"RewardEffect2_0\":{\"value\":1},\"TimelimitTaskFinishedCount1\":{\"value\":0},\"ProgressEffect_21\":{\"value\":1},\"RewardEffect13_0\":{\"value\":1},\"ProgressEffect_22\":{\"value\":1}}"}, "extraBoard6ItemLayer": {"name": "extraBoard6ItemLayer", "data": "{\"6_5\":{\"itemId\":\"1750645030030\"},\"6_1\":{\"itemId\":\"1750645030006\"},\"5_1\":{\"itemId\":\"1750645030005\"},\"6_8\":{\"itemId\":\"1750645030046\"},\"5_8\":{\"itemId\":\"1750645030045\"},\"2_8\":{\"itemId\":\"1750645030044\"},\"1_8\":{\"itemId\":\"1750645030043\"},\"6_7\":{\"itemId\":\"1750645030042\"},\"5_5\":{\"itemId\":\"1750645030029\"},\"4_7\":{\"itemId\":\"1750645030040\"},\"3_7\":{\"itemId\":\"1750645030039\"},\"2_7\":{\"itemId\":\"1750645030038\"},\"1_7\":{\"itemId\":\"1750645030037\"},\"6_6\":{\"itemId\":\"1750645030036\"},\"5_7\":{\"itemId\":\"1750645030041\"},\"6_4\":{\"itemId\":\"1750645030024\"},\"4_5\":{\"itemId\":\"1750645030028\"},\"1_3\":{\"itemId\":\"1750645030013\"},\"2_3\":{\"itemId\":\"1750645030014\"},\"3_3\":{\"itemId\":\"1750645030015\"},\"4_3\":{\"itemId\":\"1750645030016\"},\"5_3\":{\"itemId\":\"1750645030017\"},\"1_1\":{\"itemId\":\"1750645030001\"},\"6_3\":{\"itemId\":\"1750645030018\"},\"1_4\":{\"itemId\":\"1750645030019\"},\"1_6\":{\"itemId\":\"1750645030031\"},\"3_4\":{\"itemId\":\"1750645030021\"},\"2_4\":{\"itemId\":\"1750645030020\"},\"5_4\":{\"itemId\":\"1750645030023\"},\"4_4\":{\"itemId\":\"1750645030022\"},\"2_1\":{\"itemId\":\"1750645030002\"},\"1_5\":{\"itemId\":\"1750645030025\"},\"4_1\":{\"itemId\":\"1750645030004\"},\"3_1\":{\"itemId\":\"1750645030003\"},\"5_2\":{\"itemId\":\"1750645030011\"},\"6_2\":{\"itemId\":\"1750645030012\"},\"2_5\":{\"itemId\":\"1750645030026\"},\"3_5\":{\"itemId\":\"1750645030027\"},\"1_2\":{\"itemId\":\"1750645030007\"},\"2_2\":{\"itemId\":\"1750645030008\"},\"3_2\":{\"itemId\":\"1750645030009\"},\"4_2\":{\"itemId\":\"1750645030010\"},\"5_6\":{\"itemId\":\"1750645030035\"},\"4_6\":{\"itemId\":\"1750645030034\"},\"3_6\":{\"itemId\":\"1750645030033\"},\"2_6\":{\"itemId\":\"1750645030032\"}}"}, "pkRace": {"name": "pkRace", "data": "{\"roundRanks\":{\"value\":\"{\\\"1\\\":1,\\\"6\\\":2,\\\"7\\\":2,\\\"4\\\":1,\\\"5\\\":1,\\\"2\\\":1,\\\"3\\\":1}\"},\"windowOpened2\":{\"value\":1},\"windowOpened3\":{\"value\":1},\"id\":{\"value\":103},\"round\":{\"value\":8},\"ath_o_50200\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"@\\\"Amount\\\":39}\"},\"settled\":{\"value\":true},\"entryTime\":{\"value\":1750607335},\"playerDatas\":{\"value\":\"[{\\\"score\\\":\\\"[{\\\\\\\"seconds\\\\\\\":312,\\\\\\\"score\\\\\\\":27.19},{\\\\\\\"seconds\\\\\\\":33599,\\\\\\\"score\\\\\\\":95.17},{\\\\\\\"seconds\\\\\\\":33609,\\\\\\\"score\\\\\\\":155.84},{\\\\\\\"seconds\\\\\\\":33813,\\\\\\\"score\\\\\\\":164.17},{\\\\\\\"seconds\\\\\\\":39633,\\\\\\\"score\\\\\\\":203.06},{\\\\\\\"seconds\\\\\\\":42081,\\\\\\\"score\\\\\\\":220.84},{\\\\\\\"seconds\\\\\\\":42260,\\\\\\\"score\\\\\\\":263.57},{\\\\\\\"seconds\\\\\\\":42290,\\\\\\\"score\\\\\\\":334.69},{\\\\\\\"seconds\\\\\\\":55288,\\\\\\\"score\\\\\\\":470.69}]\\\",\\\"group_type\\\":\\\"pk_normal\\\",\\\"id\\\":10000078767,\\\"icon\\\":\\\"head7\\\",\\\"name\\\":\\\"JHECAAA\\\"}]\"},\"ath_o_50210\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"@\\\"Amount\\\":46}\"},\"myScore\":{\"score\":34,\"time\":414},\"lastScore\":{\"value\":\"{\\\"1\\\":34,\\\"10000078767\\\":8,\\\"round\\\":8}\"},\"ath_o_50160\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"@\\\"Amount\\\":33}\"}}"}, "extraBoard6ItemCache": {"name": "extraBoard6ItemCache", "data": "{\"1750645030048\":{\"codeStr\":\"eb6_1_1\",\"type\":3,\"cost\":\"{}\"},\"1750645030047\":{\"codeStr\":\"eb6_1_1\",\"type\":3,\"cost\":\"{}\"},\"1750645030049\":{\"codeStr\":\"eb6_1_1\",\"type\":3,\"cost\":\"{}\"}}"}, "extraBoard6Item": {"name": "extraBoard6Item", "data": "{\"1750645030006\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_2_4\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030010\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_5\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030018\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_6\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030043\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_2\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030034\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_3\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030026\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030009\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030038\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_2\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030002\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030007\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030022\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_2_2\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030025\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030045\":{\"shopGemCost\":0,\"codeStr\":\"c#eb6_1_1\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030001\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_2_4\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030030\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030004\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_2_4\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030003\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_2_2\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030016\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030029\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030013\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_6\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030020\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_5\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030028\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_5\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030012\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030017\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030019\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030005\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030044\":{\"shopGemCost\":0,\"codeStr\":\"c#eb6_1_1\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030041\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_2\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030023\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_5\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030036\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_2\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030042\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_3\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030040\":{\"shopGemCost\":0,\"codeStr\":\"c#eb6_1_1\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030032\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_3\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030039\":{\"shopGemCost\":0,\"codeStr\":\"c#eb6_1_1\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030027\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_5\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030024\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030033\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_3\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030014\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030021\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_2_1\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030035\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_3\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030031\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_1\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030011\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030015\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_5\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030008\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_7\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030037\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_3\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0},\"1750645030046\":{\"shopGemCost\":0,\"codeStr\":\"pb#c#eb6_1_2\",\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"cookGemCost\":0}}"}}, "returnUser": {"rewardexpiredTime": {"value": "0", "key": "rewardexpiredTime"}, "returnReward": {"value": "0", "key": "returnReward"}, "rewardLeaveDay": {"value": "0", "key": "rewardLeaveDay"}}, "misc": {"FlambeTimeLinkOrder": {"value": "", "key": "FlambeTimeLinkOrder"}, "EnergyBoostWindowOpenState": {"value": "0", "key": "EnergyBoostWindowOpenState"}, "EnergyBoostTriggerEndTime": {"value": "1750608853", "key": "EnergyBoostTriggerEndTime"}, "PDItemSPIndex": {"value": "pd_1_6-1;pd_2_4-4;pd_1_5-4;pd_2_6-1;greenbox_1-1;pd_2_5-2;pd_1_4-7;pd_3_4-1", "key": "PDItemSPIndex"}, "EnergyBoostUserOn": {"value": "1", "key": "EnergyBoostUserOn"}, "DataBalanceDiff": {"value": "", "key": "DataBalanceDiff"}, "FlambeTimeFinishTime": {"value": 0, "key": "FlambeTimeFinishTime"}, "IsFlambeTimeOrderGroup": {"value": 1, "key": "IsFlambeTimeOrderGroup"}, "FlambeTimeInstruSpeed": {"value": 1, "key": "FlambeTimeInstruSpeed"}, "FreeRefillEnergy": {"value": "1", "key": "FreeRefillEnergy"}, "FlambeTimeType": {"value": "mode", "key": "FlambeTimeType"}, "InventoryBoughtCap": {"value": "14", "key": "InventoryBoughtCap"}, "CheckItemRecycle": {"value": "0", "key": "CheckItemRecycle"}, "FlambeTimeInstruChains": {"value": "", "key": "FlambeTimeInstruChains"}, "EQPieceRemove": {"value": "1", "key": "EQPieceRemove"}, "ItemTypeDeleteStateit_1_1_2": {"value": "1", "key": "ItemTypeDeleteStateit_1_1_2"}, "FlambeTimePDChains": {"value": "", "key": "FlambeTimePDChains"}, "DataBalanceVersion": {"value": "1.19", "key": "DataBalanceVersion"}}, "ItemUnlock": {"ds_e1icytre_2": {"state": 2, "type": "ds_e1icytre_2"}, "ds_grillsf_7": {"state": 3, "type": "ds_grillsf_7"}, "it_5_1_6": {"state": 3, "type": "it_5_1_6"}, "pd_4_4": {"state": 3, "type": "pd_4_4"}, "it_1_2_1_2": {"state": 3, "type": "it_1_2_1_2"}, "it_5_1_7": {"state": 3, "type": "it_5_1_7"}, "it_1_1_9": {"state": 3, "type": "it_1_1_9"}, "eq_3_3": {"state": 3, "type": "eq_3_3"}, "pd_6_3": {"state": 3, "type": "pd_6_3"}, "ds_fd_21": {"state": 3, "type": "ds_fd_21"}, "it_2_1_5": {"state": 3, "type": "it_2_1_5"}, "ds_flb_1": {"state": 3, "type": "ds_flb_1"}, "pd_7_2": {"state": 3, "type": "pd_7_2"}, "gem_1_1": {"state": 2, "type": "gem_1_1"}, "ds_fd_10": {"state": 3, "type": "ds_fd_10"}, "pd_1_7": {"state": 3, "type": "pd_1_7"}, "eb4_1_3": {"state": 2, "type": "eb4_1_3"}, "it_2_3_4": {"state": 3, "type": "it_2_3_4"}, "ds_fd_14": {"state": 3, "type": "ds_fd_14"}, "it_1_1_1_3": {"state": 3, "type": "it_1_1_1_3"}, "it_1_2_1": {"state": 3, "type": "it_1_2_1"}, "it_2_2_3": {"state": 3, "type": "it_2_2_3"}, "ds_grillmt_8": {"state": 3, "type": "ds_grillmt_8"}, "it_2_3_3": {"state": 3, "type": "it_2_3_3"}, "eb2_1_6": {"state": 2, "type": "eb2_1_6"}, "it_1_2_8": {"state": 3, "type": "it_1_2_8"}, "it_1_1_1": {"state": 3, "type": "it_1_1_1"}, "eb4_2_4": {"state": 2, "type": "eb4_2_4"}, "ds_friedsf_4": {"state": 3, "type": "ds_friedsf_4"}, "additem_1_3": {"state": 2, "type": "additem_1_3"}, "ene_2": {"state": 3, "type": "ene_2"}, "it_1_1_5": {"state": 3, "type": "it_1_1_5"}, "pd_5_6": {"state": 3, "type": "pd_5_6"}, "eb4_2_2": {"state": 2, "type": "eb4_2_2"}, "it_2_2_6": {"state": 3, "type": "it_2_2_6"}, "it_2_3_2": {"state": 3, "type": "it_2_3_2"}, "it_3_1_4": {"state": 3, "type": "it_3_1_4"}, "it_3_1_1": {"state": 3, "type": "it_3_1_1"}, "it_3_2_3": {"state": 3, "type": "it_3_2_3"}, "pd_3_2": {"state": 3, "type": "pd_3_2"}, "eb2_2_2": {"state": 2, "type": "eb2_2_2"}, "it_3_1_6": {"state": 3, "type": "it_3_1_6"}, "eb4_1_2": {"state": 2, "type": "eb4_1_2"}, "ds_friedmt_4": {"state": 3, "type": "ds_friedmt_4"}, "pd_3_5": {"state": 3, "type": "pd_3_5"}, "pd_4_6": {"state": 3, "type": "pd_4_6"}, "greenbox2_1": {"state": 2, "type": "greenbox2_1"}, "ds_sal_1": {"state": 3, "type": "ds_sal_1"}, "eb4_1_10": {"state": 2, "type": "eb4_1_10"}, "it_1_2_7": {"state": 3, "type": "it_1_2_7"}, "eq_4_3": {"state": 3, "type": "eq_4_3"}, "pd_3_4": {"state": 3, "type": "pd_3_4"}, "ds_fd_12": {"state": 3, "type": "ds_fd_12"}, "ds_fd_23": {"state": 3, "type": "ds_fd_23"}, "ds_grillmt_6": {"state": 3, "type": "ds_grillmt_6"}, "ds_fd_7": {"state": 3, "type": "ds_fd_7"}, "it_3_1_5": {"state": 3, "type": "it_3_1_5"}, "pd_6_2": {"state": 3, "type": "pd_6_2"}, "it_3_2_6": {"state": 3, "type": "it_3_2_6"}, "it_1_1_2": {"state": 3, "type": "it_1_1_2"}, "ds_mixdrk_5": {"state": 3, "type": "ds_mixdrk_5"}, "pd_1_3": {"state": 3, "type": "pd_1_3"}, "eq_3_4": {"state": 3, "type": "eq_3_4"}, "pd_1_6": {"state": 3, "type": "pd_1_6"}, "pd_2_7": {"state": 3, "type": "pd_2_7"}, "ds_chopve_4": {"state": 3, "type": "ds_chopve_4"}, "it_2_3_1_4": {"state": 3, "type": "it_2_3_1_4"}, "it_1_2_2": {"state": 3, "type": "it_1_2_2"}, "it_2_1_4": {"state": 3, "type": "it_2_1_4"}, "eq_3_5": {"state": 3, "type": "eq_3_5"}, "ds_mixdrk_1": {"state": 3, "type": "ds_mixdrk_1"}, "ds_fd_8": {"state": 3, "type": "ds_fd_8"}, "ds_e1cockt_1": {"state": 3, "type": "ds_e1cockt_1"}, "freebox_1": {"state": 2, "type": "freebox_1"}, "it_4_1_4": {"state": 3, "type": "it_4_1_4"}, "eq_4_2": {"state": 3, "type": "eq_4_2"}, "eb4_2_3": {"state": 2, "type": "eb4_2_3"}, "ds_chopfru_1": {"state": 3, "type": "ds_chopfru_1"}, "ds_fd_18": {"state": 3, "type": "ds_fd_18"}, "eb2_1_9": {"state": 2, "type": "eb2_1_9"}, "it_5_2_2": {"state": 3, "type": "it_5_2_2"}, "eq_2_4": {"state": 3, "type": "eq_2_4"}, "ds_flb_2": {"state": 3, "type": "ds_flb_2"}, "it_7_2_5": {"state": 3, "type": "it_7_2_5"}, "it_1_1_4": {"state": 3, "type": "it_1_1_4"}, "ds_grillmt_9": {"state": 3, "type": "ds_grillmt_9"}, "it_2_3_1_2": {"state": 3, "type": "it_2_3_1_2"}, "pd_6_4": {"state": 3, "type": "pd_6_4"}, "it_1_2_1_1": {"state": 3, "type": "it_1_2_1_1"}, "cbox2_1": {"state": 2, "type": "cbox2_1"}, "pd_5_4": {"state": 3, "type": "pd_5_4"}, "it_4_2_7": {"state": 3, "type": "it_4_2_7"}, "eb4_1_11": {"state": 2, "type": "eb4_1_11"}, "it_2_2_5": {"state": 3, "type": "it_2_2_5"}, "skipprop_1": {"state": 2, "type": "skipprop_1"}, "ds_fd_9": {"state": 3, "type": "ds_fd_9"}, "eb2_2_3": {"state": 2, "type": "eb2_2_3"}, "ds_mixdrk_4": {"state": 3, "type": "ds_mixdrk_4"}, "it_2_2_1": {"state": 3, "type": "it_2_2_1"}, "it_4_2_1": {"state": 3, "type": "it_4_2_1"}, "eb2_2_5": {"state": 2, "type": "eb2_2_5"}, "ds_friedmt_2": {"state": 3, "type": "ds_friedmt_2"}, "eq_2_3": {"state": 3, "type": "eq_2_3"}, "ds_grillsf_4": {"state": 3, "type": "ds_grillsf_4"}, "eq_1_6": {"state": 3, "type": "eq_1_6"}, "it_4_1_9": {"state": 3, "type": "it_4_1_9"}, "ds_chopve_1": {"state": 3, "type": "ds_chopve_1"}, "eq_2_6": {"state": 3, "type": "eq_2_6"}, "ene_5": {"state": 3, "type": "ene_5"}, "pd_5_3": {"state": 3, "type": "pd_5_3"}, "eq_4_5": {"state": 3, "type": "eq_4_5"}, "it_2_1_1": {"state": 3, "type": "it_2_1_1"}, "it_4_2_3": {"state": 3, "type": "it_4_2_3"}, "ds_juice_6": {"state": 3, "type": "ds_juice_6"}, "it_5_2_7": {"state": 3, "type": "it_5_2_7"}, "ds_fd_4": {"state": 3, "type": "ds_fd_4"}, "it_3_1_9": {"state": 3, "type": "it_3_1_9"}, "ds_mixdrk_3": {"state": 3, "type": "ds_mixdrk_3"}, "eb4_1_6": {"state": 2, "type": "eb4_1_6"}, "eq_6_2": {"state": 3, "type": "eq_6_2"}, "enebox_1": {"state": 2, "type": "enebox_1"}, "it_3_1_7": {"state": 3, "type": "it_3_1_7"}, "it_4_2_2": {"state": 3, "type": "it_4_2_2"}, "eb4_1_1": {"state": 2, "type": "eb4_1_1"}, "ds_grillmt_12": {"state": 3, "type": "ds_grillmt_12"}, "gem_4": {"state": 3, "type": "gem_4"}, "eq_5_3": {"state": 3, "type": "eq_5_3"}, "ds_friedve_1": {"state": 3, "type": "ds_friedve_1"}, "it_5_2_4": {"state": 3, "type": "it_5_2_4"}, "additem_1_2": {"state": 2, "type": "additem_1_2"}, "it_3_2_4": {"state": 3, "type": "it_3_2_4"}, "it_1_1_3": {"state": 3, "type": "it_1_1_3"}, "skipprop_2": {"state": 2, "type": "skipprop_2"}, "it_5_2_1": {"state": 3, "type": "it_5_2_1"}, "cbox1_1": {"state": 2, "type": "cbox1_1"}, "it_7_1_5": {"state": 3, "type": "it_7_1_5"}, "it_4_1_7": {"state": 3, "type": "it_4_1_7"}, "pd_6_1": {"state": 3, "type": "pd_6_1"}, "it_1_2_3": {"state": 3, "type": "it_1_2_3"}, "it_4_2_4": {"state": 3, "type": "it_4_2_4"}, "it_5_2_8": {"state": 3, "type": "it_5_2_8"}, "gem_1": {"state": 3, "type": "gem_1"}, "it_6_1_5": {"state": 3, "type": "it_6_1_5"}, "eq_1_3": {"state": 3, "type": "eq_1_3"}, "it_3_1_8": {"state": 3, "type": "it_3_1_8"}, "it_1_1_1_1": {"state": 3, "type": "it_1_1_1_1"}, "eq_1_4": {"state": 3, "type": "eq_1_4"}, "it_3_2_5": {"state": 3, "type": "it_3_2_5"}, "eq_2_2": {"state": 3, "type": "eq_2_2"}, "it_2_1_8": {"state": 3, "type": "it_2_1_8"}, "ds_mixdrk_9": {"state": 3, "type": "ds_mixdrk_9"}, "eb2_1_11": {"state": 2, "type": "eb2_1_11"}, "gem_3": {"state": 3, "type": "gem_3"}, "it_6_1_2": {"state": 3, "type": "it_6_1_2"}, "skiptime_1": {"state": 2, "type": "skiptime_1"}, "ds_grillsf_5": {"state": 3, "type": "ds_grillsf_5"}, "ds_juice_4": {"state": 3, "type": "ds_juice_4"}, "ds_grillve_1": {"state": 3, "type": "ds_grillve_1"}, "eb2_1_2": {"state": 2, "type": "eb2_1_2"}, "eb2_1_8": {"state": 2, "type": "eb2_1_8"}, "it_7_2_1": {"state": 3, "type": "it_7_2_1"}, "it_2_3_6": {"state": 3, "type": "it_2_3_6"}, "ds_grillsf_3": {"state": 3, "type": "ds_grillsf_3"}, "eb2_1_5": {"state": 2, "type": "eb2_1_5"}, "ds_fd_15": {"state": 3, "type": "ds_fd_15"}, "ds_chopfs_1": {"state": 3, "type": "ds_chopfs_1"}, "ds_grillmt_11": {"state": 3, "type": "ds_grillmt_11"}, "eb4_1_9": {"state": 2, "type": "eb4_1_9"}, "eq_3_2": {"state": 3, "type": "eq_3_2"}, "pd_2_3": {"state": 3, "type": "pd_2_3"}, "it_2_1_9": {"state": 3, "type": "it_2_1_9"}, "pd_1_2": {"state": 3, "type": "pd_1_2"}, "it_7_1_4": {"state": 3, "type": "it_7_1_4"}, "it_4_1_8": {"state": 3, "type": "it_4_1_8"}, "ds_mixdrk_2": {"state": 3, "type": "ds_mixdrk_2"}, "ds_friedve_3": {"state": 3, "type": "ds_friedve_3"}, "it_2_1_3": {"state": 3, "type": "it_2_1_3"}, "ds_chopve_2": {"state": 3, "type": "ds_chopve_2"}, "it_5_1_5": {"state": 3, "type": "it_5_1_5"}, "pd_4_5": {"state": 3, "type": "pd_4_5"}, "eb2_1_1": {"state": 2, "type": "eb2_1_1"}, "ds_fd_20": {"state": 3, "type": "ds_fd_20"}, "it_2_2_4": {"state": 3, "type": "it_2_2_4"}, "pd_1_1": {"state": 3, "type": "pd_1_1"}, "ds_juice_8": {"state": 3, "type": "ds_juice_8"}, "it_5_1_8": {"state": 3, "type": "it_5_1_8"}, "ds_mixdrk_6": {"state": 3, "type": "ds_mixdrk_6"}, "pd_7_5": {"state": 3, "type": "pd_7_5"}, "it_5_2_6": {"state": 3, "type": "it_5_2_6"}, "it_7_1_7": {"state": 3, "type": "it_7_1_7"}, "pd_7_4": {"state": 3, "type": "pd_7_4"}, "ds_grillsf_2": {"state": 3, "type": "ds_grillsf_2"}, "ds_fd_16": {"state": 3, "type": "ds_fd_16"}, "eq_3_6": {"state": 3, "type": "eq_3_6"}, "ds_chopfr_1": {"state": 3, "type": "ds_chopfr_1"}, "ds_grillsf_1": {"state": 3, "type": "ds_grillsf_1"}, "it_3_2_1": {"state": 3, "type": "it_3_2_1"}, "it_1_2_4": {"state": 3, "type": "it_1_2_4"}, "it_3_2_2": {"state": 3, "type": "it_3_2_2"}, "it_1_1_8": {"state": 3, "type": "it_1_1_8"}, "eb2_2_4": {"state": 2, "type": "eb2_2_4"}, "ene_3": {"state": 3, "type": "ene_3"}, "greenbox_1": {"state": 2, "type": "greenbox_1"}, "ds_friedsf_2": {"state": 3, "type": "ds_friedsf_2"}, "eb4_1_12": {"state": 2, "type": "eb4_1_12"}, "ds_grillve_2": {"state": 3, "type": "ds_grillve_2"}, "pd_2_1": {"state": 3, "type": "pd_2_1"}, "ds_grillmt_4": {"state": 3, "type": "ds_grillmt_4"}, "pd_1_5": {"state": 3, "type": "pd_1_5"}, "it_1_2_1_3": {"state": 3, "type": "it_1_2_1_3"}, "ds_grillmt_1": {"state": 3, "type": "ds_grillmt_1"}, "ds_friedsf_1": {"state": 3, "type": "ds_friedsf_1"}, "ds_fd_5": {"state": 3, "type": "ds_fd_5"}, "it_2_1_2": {"state": 3, "type": "it_2_1_2"}, "ds_grillve_3": {"state": 3, "type": "ds_grillve_3"}, "it_6_1_4": {"state": 3, "type": "it_6_1_4"}, "ds_grillmt_10": {"state": 3, "type": "ds_grillmt_10"}, "it_2_3_1": {"state": 3, "type": "it_2_3_1"}, "it_5_1_4": {"state": 3, "type": "it_5_1_4"}, "ds_e1cockt_14": {"state": 3, "type": "ds_e1cockt_14"}, "it_1_1_7": {"state": 3, "type": "it_1_1_7"}, "ds_juice_2": {"state": 3, "type": "ds_juice_2"}, "it_1_2_6": {"state": 3, "type": "it_1_2_6"}, "it_2_2_2": {"state": 3, "type": "it_2_2_2"}, "eb4_1_4": {"state": 2, "type": "eb4_1_4"}, "it_5_1_3": {"state": 3, "type": "it_5_1_3"}, "it_4_1_5": {"state": 3, "type": "it_4_1_5"}, "it_6_1_3": {"state": 3, "type": "it_6_1_3"}, "ds_friedmt_5": {"state": 3, "type": "ds_friedmt_5"}, "it_3_1_2": {"state": 3, "type": "it_3_1_2"}, "eb4_1_8": {"state": 2, "type": "eb4_1_8"}, "eq_6_3": {"state": 3, "type": "eq_6_3"}, "it_7_1_3": {"state": 3, "type": "it_7_1_3"}, "it_4_1_2": {"state": 3, "type": "it_4_1_2"}, "it_5_2_5": {"state": 3, "type": "it_5_2_5"}, "eq_5_4": {"state": 3, "type": "eq_5_4"}, "ds_chopve_3": {"state": 3, "type": "ds_chopve_3"}, "additem_1_1": {"state": 2, "type": "additem_1_1"}, "eq_2_5": {"state": 3, "type": "eq_2_5"}, "eq_2_1": {"state": 3, "type": "eq_2_1"}, "it_2_3_1_1": {"state": 3, "type": "it_2_3_1_1"}, "ds_fd_19": {"state": 3, "type": "ds_fd_19"}, "ds_e1cockt_4": {"state": 3, "type": "ds_e1cockt_4"}, "eb6_1_1": {"state": 1, "type": "eb6_1_1"}, "skipprop_4": {"state": 2, "type": "skipprop_4"}, "additem_1": {"state": 2, "type": "additem_1"}, "ds_grillmt_3": {"state": 3, "type": "ds_grillmt_3"}, "it_2_3_1_3": {"state": 3, "type": "it_2_3_1_3"}, "ds_e1hotdrk_1": {"state": 3, "type": "ds_e1hotdrk_1"}, "eq_4_6": {"state": 3, "type": "eq_4_6"}, "eb2_1_3": {"state": 2, "type": "eb2_1_3"}, "pd_3_1": {"state": 3, "type": "pd_3_1"}, "it_4_1_3": {"state": 3, "type": "it_4_1_3"}, "it_6_1_1": {"state": 3, "type": "it_6_1_1"}, "it_7_2_2": {"state": 3, "type": "it_7_2_2"}, "pd_2_4": {"state": 3, "type": "pd_2_4"}, "eb2_1_7": {"state": 2, "type": "eb2_1_7"}, "pd_3_6": {"state": 3, "type": "pd_3_6"}, "pd_1_4": {"state": 3, "type": "pd_1_4"}, "ds_mixdrk_7": {"state": 3, "type": "ds_mixdrk_7"}, "ds_friedmt_3": {"state": 3, "type": "ds_friedmt_3"}, "ds_fd_6": {"state": 3, "type": "ds_fd_6"}, "ene_1": {"state": 3, "type": "ene_1"}, "it_3_1_3": {"state": 3, "type": "it_3_1_3"}, "pd_4_1": {"state": 3, "type": "pd_4_1"}, "pd_4_2": {"state": 3, "type": "pd_4_2"}, "skipprop_5": {"state": 2, "type": "skipprop_5"}, "eq_4_4": {"state": 3, "type": "eq_4_4"}, "it_2_1_7": {"state": 3, "type": "it_2_1_7"}, "eb4_1_5": {"state": 2, "type": "eb4_1_5"}, "eb4_1_7": {"state": 2, "type": "eb4_1_7"}, "eq_1_7": {"state": 3, "type": "eq_1_7"}, "ds_juice_7": {"state": 3, "type": "ds_juice_7"}, "it_6_1_6": {"state": 3, "type": "it_6_1_6"}, "it_7_1_6": {"state": 3, "type": "it_7_1_6"}, "ene_4": {"state": 3, "type": "ene_4"}, "pd_6_5": {"state": 3, "type": "pd_6_5"}, "pd_3_3": {"state": 3, "type": "pd_3_3"}, "pd_2_6": {"state": 3, "type": "pd_2_6"}, "cbox3_1": {"state": 2, "type": "cbox3_1"}, "ds_friedmt_1": {"state": 3, "type": "ds_friedmt_1"}, "it_7_1_2": {"state": 3, "type": "it_7_1_2"}, "pd_5_5": {"state": 3, "type": "pd_5_5"}, "ds_juice_3": {"state": 3, "type": "ds_juice_3"}, "it_5_1_2": {"state": 3, "type": "it_5_1_2"}, "ds_friedve_4": {"state": 3, "type": "ds_friedve_4"}, "pd_7_1": {"state": 3, "type": "pd_7_1"}, "it_5_2_3": {"state": 3, "type": "it_5_2_3"}, "eq_6_1": {"state": 3, "type": "eq_6_1"}, "skipprop_3": {"state": 2, "type": "skipprop_3"}, "ds_friedve_2": {"state": 3, "type": "ds_friedve_2"}, "it_1_2_5": {"state": 3, "type": "it_1_2_5"}, "ds_fd_3": {"state": 3, "type": "ds_fd_3"}, "additem_2": {"state": 2, "type": "additem_2"}, "ds_fd_1": {"state": 3, "type": "ds_fd_1"}, "eq_1_5": {"state": 3, "type": "eq_1_5"}, "eq_6_4": {"state": 3, "type": "eq_6_4"}, "ds_juice_1": {"state": 3, "type": "ds_juice_1"}, "eq_3_1": {"state": 3, "type": "eq_3_1"}, "ds_grillsf_6": {"state": 3, "type": "ds_grillsf_6"}, "eb4_2_1": {"state": 2, "type": "eb4_2_1"}, "it_4_2_5": {"state": 3, "type": "it_4_2_5"}, "eb2_1_10": {"state": 2, "type": "eb2_1_10"}, "it_6_1_7": {"state": 3, "type": "it_6_1_7"}, "ds_grillve_4": {"state": 3, "type": "ds_grillve_4"}, "ds_dst_1": {"state": 3, "type": "ds_dst_1"}, "ds_grillmt_7": {"state": 3, "type": "ds_grillmt_7"}, "ds_mixdrk_8": {"state": 3, "type": "ds_mixdrk_8"}, "eb2_2_1": {"state": 2, "type": "eb2_2_1"}, "it_7_2_3": {"state": 3, "type": "it_7_2_3"}, "it_1_1_6": {"state": 3, "type": "it_1_1_6"}, "ds_friedve_5": {"state": 3, "type": "ds_friedve_5"}, "eb2_1_4": {"state": 2, "type": "eb2_1_4"}, "ds_fd_2": {"state": 3, "type": "ds_fd_2"}, "ds_grillmt_2": {"state": 3, "type": "ds_grillmt_2"}, "it_4_1_1": {"state": 3, "type": "it_4_1_1"}, "it_4_2_6": {"state": 3, "type": "it_4_2_6"}, "ds_fd_13": {"state": 3, "type": "ds_fd_13"}, "it_1_1_1_2": {"state": 3, "type": "it_1_1_1_2"}, "it_7_2_6": {"state": 3, "type": "it_7_2_6"}, "eq_4_1": {"state": 3, "type": "eq_4_1"}, "pd_2_2": {"state": 3, "type": "pd_2_2"}, "ds_e1cockt_11": {"state": 3, "type": "ds_e1cockt_11"}, "ds_friedsf_3": {"state": 3, "type": "ds_friedsf_3"}, "pd_3_7": {"state": 3, "type": "pd_3_7"}, "pd_2_5": {"state": 3, "type": "pd_2_5"}, "additem_3": {"state": 2, "type": "additem_3"}, "eb4_2_5": {"state": 2, "type": "eb4_2_5"}, "pd_7_3": {"state": 3, "type": "pd_7_3"}, "it_3_2_7": {"state": 3, "type": "it_3_2_7"}, "it_2_3_5": {"state": 3, "type": "it_2_3_5"}, "ds_grillmt_5": {"state": 3, "type": "ds_grillmt_5"}, "pd_4_3": {"state": 3, "type": "pd_4_3"}, "it_7_1_1": {"state": 3, "type": "it_7_1_1"}, "it_5_1_1": {"state": 3, "type": "it_5_1_1"}, "it_7_2_4": {"state": 3, "type": "it_7_2_4"}, "it_4_1_6": {"state": 3, "type": "it_4_1_6"}, "ds_fd_17": {"state": 3, "type": "ds_fd_17"}, "it_1_1_1_4": {"state": 3, "type": "it_1_1_1_4"}, "ds_juice_9": {"state": 3, "type": "ds_juice_9"}, "ds_fd_11": {"state": 3, "type": "ds_fd_11"}, "gem_2": {"state": 3, "type": "gem_2"}, "it_2_1_6": {"state": 3, "type": "it_2_1_6"}, "it_1_1_10": {"state": 3, "type": "it_1_1_10"}, "it_3_1_10": {"state": 3, "type": "it_3_1_10"}}, "board": {"3_3": {"id": "3_3", "itemId": "1749806321001"}, "4_3": {"id": "4_3", "itemId": "1750607932001"}, "6_8": {"id": "6_8", "itemId": "1750607936001"}, "2_3": {"id": "2_3", "itemId": "1750607639001"}, "5_7": {"id": "5_7", "itemId": "1749807123001"}, "7_4": {"id": "7_4", "itemId": "1750606859001"}, "3_7": {"id": "3_7", "itemId": "1750607367002"}, "5_4": {"id": "5_4", "itemId": "1750606851001"}, "7_5": {"id": "7_5", "itemId": "1748240281001"}, "1_8": {"id": "1_8", "itemId": "1750607659001"}, "2_4": {"id": "2_4", "itemId": "1750607933001"}, "1_4": {"id": "1_4", "itemId": "1749006501001"}, "5_1": {"id": "5_1", "itemId": "1749622267001"}, "6_1": {"id": "6_1", "itemId": "1750607105001"}, "7_1": {"id": "7_1", "itemId": "1748241900001"}, "3_8": {"id": "3_8", "itemId": "1750607365001"}, "6_5": {"id": "6_5", "itemId": "1750607735001"}, "2_8": {"id": "2_8", "itemId": "*************"}, "3_4": {"id": "3_4", "itemId": "1750607309001"}, "4_4": {"id": "4_4", "itemId": "1749095656001"}, "3_2": {"id": "3_2", "itemId": "1749621842001"}, "7_7": {"id": "7_7", "itemId": "1750607625003"}, "6_7": {"id": "6_7", "itemId": "1750607843004"}, "7_3": {"id": "7_3", "itemId": "1749913956001"}, "1_3": {"id": "1_3", "itemId": "1748920325001"}, "5_3": {"id": "5_3", "itemId": "1748946057001"}, "6_3": {"id": "6_3", "itemId": "1750606877001"}, "3_1": {"id": "3_1", "itemId": "1749544421001"}, "7_9": {"id": "7_9", "itemId": "1748245772001"}, "7_2": {"id": "7_2", "itemId": "1749109138001"}, "2_7": {"id": "2_7", "itemId": "1750494859001"}, "5_2": {"id": "5_2", "itemId": "1748485268001"}, "6_2": {"id": "6_2", "itemId": "1750162839001"}, "2_9": {"id": "2_9", "itemId": "1750607621001"}, "1_9": {"id": "1_9", "itemId": "1750496288001"}, "7_6": {"id": "7_6", "itemId": "1748241561001"}, "3_9": {"id": "3_9", "itemId": "1750607794001"}, "4_7": {"id": "4_7", "itemId": "1750607612001"}, "7_8": {"id": "7_8", "itemId": "1750607867001"}, "1_7": {"id": "1_7", "itemId": "1748240816001"}, "6_4": {"id": "6_4", "itemId": "1749804104001"}, "6_6": {"id": "6_6", "itemId": "1750607862002"}, "4_8": {"id": "4_8", "itemId": "1750607817002"}, "1_1": {"id": "1_1", "itemId": "1750606830001"}, "2_1": {"id": "2_1", "itemId": "1748663697001"}, "1_5": {"id": "1_5", "itemId": "1749007435001"}, "4_1": {"id": "4_1", "itemId": "1749266069001"}, "3_5": {"id": "3_5", "itemId": "1750607237002"}, "2_5": {"id": "2_5", "itemId": "1750495980002"}, "5_5": {"id": "5_5", "itemId": "1750606806001"}, "4_5": {"id": "4_5", "itemId": "1750494719001"}, "2_2": {"id": "2_2", "itemId": "1750607399001"}, "1_2": {"id": "1_2", "itemId": "1750606868001"}, "4_2": {"id": "4_2", "itemId": "1749181140001"}, "1_6": {"id": "1_6", "itemId": "1749915329001"}, "2_6": {"id": "2_6", "itemId": "1750053834001"}, "3_6": {"id": "3_6", "itemId": "1750607309003"}, "4_6": {"id": "4_6", "itemId": "1750607588001"}, "5_6": {"id": "5_6", "itemId": "1750388716001"}}, "item": {"1750607817002": {"spreadCodeWeightPairs": "it_2_3_1_1-1", "spreadStorageRestNumber": 1, "cookGemCost": 0, "spreadCount": 0, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1750607817002", "spreadItemBoxChain": "", "costEnergyCurDay": 2, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadInherit": 0, "spreadWeightType": 2, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "it_2_3_2", "materialInfo": "", "choices": ""}, "*************": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "*************", "codeStr": "gem_1", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1748240281001": {"spreadCodeWeightPairs": "it_1_1_1-1", "spreadStorageRestNumber": 42, "cookGemCost": 0, "spreadCount": 2666, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1748240281001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_1_7", "materialInfo": "", "choices": ""}, "1750389899004": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 14, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750389899004", "codeStr": "it_6_1_5", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1750494859001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 8, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750494859001", "codeStr": "it_4_2_4", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1750606806001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "shopGemCost": 0, "cookSpeedTime": 0, "id": "1750606806001", "cookState": 1, "spreadItemBoxChain": "", "materialInfo": "", "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergyCurDay": 0, "codeStr": "eq_6_4", "cookLastUpdateTime": 0, "choices": ""}, "1749007435001": {"spreadCodeWeightPairs": "it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1", "spreadStorageRestNumber": 24, "cookGemCost": 0, "spreadCount": 121, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1749007435001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_1_4", "materialInfo": "", "choices": ""}, "1749095656001": {"spreadCodeWeightPairs": "it_5_1_1-8;it_5_1_2-2;it_5_2_1-8", "spreadStorageRestNumber": 30, "cookGemCost": 0, "spreadCount": 802, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1749095656001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 2, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_5_6", "materialInfo": "", "choices": ""}, "*************": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "*************", "codeStr": "pd_1_3", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1748920325001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1748920325001", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 0, "codeStr": "eq_5_4", "materialInfo": "", "cookLastUpdateTime": 0}, "1750607621001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 16, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607621001", "codeStr": "it_1_2_6", "costEnergyCurDay": 16, "spreadItemBoxChain": ""}, "*************": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 8, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "*************", "codeStr": "it_2_3_4", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1749544421001": {"spreadCodeWeightPairs": "it_6_1_1-19", "spreadStorageRestNumber": 24, "cookGemCost": 0, "spreadCount": 81, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1749544421001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 2, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_6_4", "materialInfo": "", "choices": ""}, "1750606859001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 2, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750606859001", "codeStr": "it_7_1_3", "costEnergyCurDay": 2, "spreadItemBoxChain": ""}, "1750607612001": {"spreadCodeWeightPairs": "it_1_1_1_1-1", "spreadStorageRestNumber": 1, "cookGemCost": 0, "spreadCount": 0, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1750607612001", "spreadItemBoxChain": "", "costEnergyCurDay": 22, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadInherit": 0, "spreadWeightType": 2, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "it_1_1_6", "materialInfo": "", "choices": ""}, "1748241561001": {"spreadCodeWeightPairs": "it_2_1_1-1;it_2_3_1-1;it_2_1_2-1;it_2_1_3-1;it_2_1_1-1;it_2_1_1-1;it_2_3_1-1;it_2_1_2-1;it_2_2_1-1;it_2_1_1-1;it_2_1_2-1", "spreadStorageRestNumber": 42, "cookGemCost": 0, "spreadCount": 3855, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1748241561001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_2_7", "materialInfo": "", "choices": ""}, "1750606868001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750606868001", "codeStr": "it_7_1_1", "costEnergyCurDay": 1, "spreadItemBoxChain": ""}, "1750607843004": {"spreadCodeWeightPairs": "it_2_3_1_1-1", "spreadStorageRestNumber": 1, "cookGemCost": 0, "spreadCount": 0, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1750607843004", "spreadItemBoxChain": "", "costEnergyCurDay": 2, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadInherit": 0, "spreadWeightType": 2, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "it_2_3_2", "materialInfo": "", "choices": ""}, "1750053834001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1750053834001", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 0, "codeStr": "eq_3_4", "materialInfo": "", "cookLastUpdateTime": 0}, "1750606877001": {"spreadCodeWeightPairs": "it_7_1_1-12;it_7_1_2-3;it_7_2_1-5", "spreadStorageRestNumber": 36, "cookGemCost": 0, "spreadCount": 0, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1750606877001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadInherit": 0, "spreadWeightType": 2, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_7_5", "materialInfo": "", "choices": ""}, "1748240816001": {"spreadCodeWeightPairs": "it_1_1_1-1;it_1_1_2-1;it_1_1_1-1;it_1_2_1-1;it_1_1_2-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_2-1;it_1_1_3-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_2-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1", "spreadStorageRestNumber": 36, "cookGemCost": 0, "spreadCount": 1640, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1748240816001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_1_6", "materialInfo": "", "choices": ""}, "1750607588001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 25, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607588001", "codeStr": "it_1_1_8", "costEnergyCurDay": 57, "spreadItemBoxChain": ""}, "1750495920001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 8, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750495920001", "codeStr": "it_7_2_4", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1750162839001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750162839001", "codeStr": "eq_2_3", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1748485268001": {"spreadCodeWeightPairs": "it_4_2_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1", "spreadStorageRestNumber": 36, "cookGemCost": 0, "spreadCount": 1618, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1748485268001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_4_6", "materialInfo": "", "choices": ""}, "*************": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 44, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "*************", "codeStr": "it_1_1_1_2", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1748245772001": {"spreadCodeWeightPairs": "it_1_1_1-1;it_1_1_1-1", "spreadStorageRestNumber": 30, "cookGemCost": 0, "spreadCount": 538, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1748245772001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_1_5", "materialInfo": "", "choices": ""}, "*************": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "*************", "codeStr": "it_2_3_4", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1749006501001": {"spreadCodeWeightPairs": "it_4_1_2-1;it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_2-1", "spreadStorageRestNumber": 30, "cookGemCost": 0, "spreadCount": 467, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1749006501001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_4_5", "materialInfo": "", "choices": ""}, "1750607639001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 2, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607639001", "codeStr": "it_1_2_5", "costEnergyCurDay": 14, "spreadItemBoxChain": ""}, "1749915329001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1749915329001", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 0, "codeStr": "eq_4_5", "materialInfo": "", "cookLastUpdateTime": 0}, "1750607237002": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 22, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607237002", "codeStr": "it_3_1_8", "costEnergyCurDay": 60, "spreadItemBoxChain": ""}, "1750607862002": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607862002", "codeStr": "it_2_2_5", "costEnergyCurDay": 16, "spreadItemBoxChain": ""}, "1749109138001": {"spreadCodeWeightPairs": "it_2_1_3-1;it_2_3_1-1;it_2_1_1-1;it_2_1_2-1;it_2_1_1-1;it_2_2_1-1;it_2_1_2-1;it_2_1_1-1;it_2_3_1-1;it_2_1_1-1;it_2_1_2-1;it_2_2_1-1", "spreadStorageRestNumber": 36, "cookGemCost": 0, "spreadCount": 309, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1749109138001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_2_6", "materialInfo": "", "choices": ""}, "1750388716001": {"spreadCodeWeightPairs": "it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1", "spreadStorageRestNumber": 24, "cookGemCost": 0, "spreadCount": 40, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1750388716001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_3_4", "materialInfo": "", "choices": ""}, "*************": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "*************", "codeStr": "pd_4_3", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1750606830001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "shopGemCost": 0, "cookSpeedTime": 0, "id": "1750606830001", "cookState": 1, "spreadItemBoxChain": "", "materialInfo": "", "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergyCurDay": 0, "codeStr": "eq_1_7", "cookLastUpdateTime": 0, "choices": ""}, "1750607659001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607659001", "codeStr": "it_5_2_5", "costEnergyCurDay": 16, "spreadItemBoxChain": ""}, "1750607399001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607399001", "codeStr": "it_3_2_3", "costEnergyCurDay": 4, "spreadItemBoxChain": ""}, "1748924502001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1748924502001", "codeStr": "eq_5_3", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1750389865002": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 14, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750389865002", "codeStr": "it_6_1_5", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1750607625003": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607625003", "codeStr": "it_1_1_5", "costEnergyCurDay": 12, "spreadItemBoxChain": ""}, "1750607105001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 1, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607105001", "codeStr": "it_2_3_5", "costEnergyCurDay": 11, "spreadItemBoxChain": ""}, "1750607905001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607905001", "codeStr": "ene_1", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1748663697001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1748663697001", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 0, "codeStr": "eq_3_6", "materialInfo": "", "cookLastUpdateTime": 0}, "1749915332001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1749915332001", "codeStr": "pd_1_3", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1750607309001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607309001", "codeStr": "it_3_2_2", "costEnergyCurDay": 2, "spreadItemBoxChain": ""}, "1748241900001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1748241900001", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 0, "codeStr": "eq_2_5", "materialInfo": "", "cookLastUpdateTime": 0}, "*************": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 12, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "*************", "codeStr": "it_6_1_5", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1750606851001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750606851001", "codeStr": "it_7_1_2", "costEnergyCurDay": 2, "spreadItemBoxChain": ""}, "1750607309003": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607309003", "codeStr": "it_3_1_6", "costEnergyCurDay": 32, "spreadItemBoxChain": ""}, "1750607794001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607794001", "codeStr": "it_2_3_4", "costEnergyCurDay": 8, "spreadItemBoxChain": ""}, "1750495980002": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 4, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750495980002", "codeStr": "it_2_3_3", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1749181140001": {"spreadCodeWeightPairs": "it_4_1_1-1;it_4_1_1-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1", "spreadStorageRestNumber": 24, "cookGemCost": 0, "spreadCount": 135, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1749181140001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_4_4", "materialInfo": "", "choices": ""}, "1750607933001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607933001", "codeStr": "it_2_1_4", "costEnergyCurDay": 8, "spreadItemBoxChain": ""}, "1750494719001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 9, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750494719001", "codeStr": "ds_mixdrk_7", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1748946057001": {"spreadCodeWeightPairs": "it_6_1_1-7;it_6_1_2-2", "spreadStorageRestNumber": 30, "cookGemCost": 0, "spreadCount": 471, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1748946057001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 2, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_6_5", "materialInfo": "", "choices": ""}, "1750607735001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607735001", "codeStr": "it_5_2_4", "costEnergyCurDay": 8, "spreadItemBoxChain": ""}, "1750607867001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607867001", "codeStr": "it_1_2_2", "costEnergyCurDay": 2, "spreadItemBoxChain": ""}, "1750607367002": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607367002", "codeStr": "it_3_1_3", "costEnergyCurDay": 4, "spreadItemBoxChain": ""}, "1750607932001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607932001", "codeStr": "it_2_1_6", "costEnergyCurDay": 18, "spreadItemBoxChain": ""}, "1749266069001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1749266069001", "cookState": 5, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "ds_grillsf_5", "cookStartTimer": -1, "costEnergy": 0, "codeStr": "eq_2_6", "materialInfo": "it_5_1_7-*************", "cookLastUpdateTime": 1750645040}, "1749622267001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1749622267001", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 0, "codeStr": "eq_4_6", "materialInfo": "", "cookLastUpdateTime": 0}, "1750162536002": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1750162536002", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 6, "codeStr": "it_2_3_1_2", "materialInfo": "", "cookLastUpdateTime": 0}, "1749913956001": {"spreadCodeWeightPairs": "it_5_2_1-1", "spreadStorageRestNumber": 24, "cookGemCost": 0, "spreadCount": 59, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1749913956001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 2, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_5_5", "materialInfo": "", "choices": ""}, "1749806321001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1749806321001", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 0, "codeStr": "eq_2_4", "materialInfo": "", "cookLastUpdateTime": 0}, "1750162627001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1750162627001", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 6, "codeStr": "it_2_3_1_2", "materialInfo": "", "cookLastUpdateTime": 0}, "1750607365001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607365001", "codeStr": "it_3_1_5", "costEnergyCurDay": 16, "spreadItemBoxChain": ""}, "1749804104001": {"spreadCodeWeightPairs": "it_3_1_1-1;it_3_1_3-1;it_3_1_1-1;it_3_2_1-1;it_3_1_2-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_2-1;it_3_1_1-1;it_3_1_3-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_2-1;it_3_1_1-1;it_3_2_1-1", "spreadStorageRestNumber": 42, "cookGemCost": 0, "spreadCount": 923, "choiceDate": "", "shopGemCost": 0, "spreadTierUpLevel": 0, "id": "1749804104001", "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "costEnergy": 0, "spreadWeightType": 1, "spreadInherit": 0, "spreadTierUpCount": 0, "spreadState": 3, "spreadStartTimer": -1, "cookRecipe": "", "spreadEnergyFree": 0, "spreadAddItem": 0, "codeStr": "pd_3_7", "materialInfo": "", "choices": ""}, "1749807123001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 8, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1749807123001", "codeStr": "it_2_3_4", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1750607936001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 39, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750607936001", "codeStr": "ds_e1icytre_2", "costEnergyCurDay": 120, "spreadItemBoxChain": ""}, "*************": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "*************", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 6, "codeStr": "it_2_3_1_2", "materialInfo": "", "cookLastUpdateTime": 0}, "1750496288001": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 4, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "1750496288001", "codeStr": "it_1_1_4", "costEnergyCurDay": 0, "spreadItemBoxChain": ""}, "1749621842001": {"spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "choices": "", "cookSpeedTime": 0, "id": "1749621842001", "cookState": 1, "spreadItemBoxChain": "", "costEnergyCurDay": 0, "cookSkipPropCost": 0, "bubbleGemCost": 0, "shopGemCost": 0, "cookRecipe": "", "cookStartTimer": -1, "costEnergy": 0, "codeStr": "eq_3_5", "materialInfo": "", "cookLastUpdateTime": 0}, "*************": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 0, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "*************", "codeStr": "it_3_1_4", "costEnergyCurDay": 8, "spreadItemBoxChain": ""}, "*************": {"materialInfo": "", "cookSkipPropCost": 0, "spreadCodeWeightPairs": "", "cookGemCost": 0, "choiceDate": "", "costEnergy": 5, "choices": "", "bubbleGemCost": 0, "cookRecipe": "", "shopGemCost": 0, "id": "*************", "codeStr": "it_5_1_7", "costEnergyCurDay": 30, "spreadItemBoxChain": ""}}, "account": {"SocialId": {"value": "110293590390918150288", "key": "SocialId"}, "SocialName": {"value": "agag afgw", "key": "SocialName"}, "SocialType": {"value": "18", "key": "SocialType"}, "SocialPictureUrl": {"value": "", "key": "SocialPictureUrl"}}, "rate": {"1": {"activeConfigId": 0, "rated": 0, "triggeredConfig": "", "canPopup": 0, "id": "1"}}, "inventory": {"*************": {"itemId": "*************", "codeStr": "gem_1", "storeTime": 2000}, "*************": {"itemId": "*************", "codeStr": "it_2_3_4", "storeTime": 4000}, "*************": {"itemId": "*************", "codeStr": "pd_4_3", "storeTime": 5000}, "*************": {"itemId": "*************", "codeStr": "it_1_1_1_2", "storeTime": 11000}, "*************": {"itemId": "*************", "codeStr": "pd_1_3", "storeTime": 7000}, "*************": {"itemId": "*************", "codeStr": "it_2_3_1_2", "storeTime": 9000}, "*************": {"itemId": "*************", "codeStr": "it_2_3_4", "storeTime": 3000}, "*************": {"itemId": "*************", "codeStr": "it_6_1_5", "storeTime": 13000}, "1750389899004": {"itemId": "1750389899004", "codeStr": "it_6_1_5", "storeTime": 14000}, "1750162536002": {"itemId": "1750162536002", "codeStr": "it_2_3_1_2", "storeTime": 8000}, "1748924502001": {"itemId": "1748924502001", "codeStr": "eq_5_3", "storeTime": 1000}, "1750495920001": {"itemId": "1750495920001", "codeStr": "it_7_2_4", "storeTime": 15000}, "1750389865002": {"itemId": "1750389865002", "codeStr": "it_6_1_5", "storeTime": 12000}, "1749915332001": {"itemId": "1749915332001", "codeStr": "pd_1_3", "storeTime": 6000}, "1750162627001": {"itemId": "1750162627001", "codeStr": "it_2_3_1_2", "storeTime": 10000}, "1750607905001": {"itemId": "1750607905001", "codeStr": "ene_1", "storeTime": 1750607906}}, "cacheItem": {}, "bundleMeta": {"energytier_599PurchaseBundleId": {"value": "{\"bundleId\":\"et5.99\"@\"rewards\":[{\"Currency\":\"energy\"@\"Amount\":820@\"Crypt\":\"{JZ\"}]@\"groupId\":\"energytier1\"@\"bundleType\":\"multiTier\"}", "key": "energytier_599PurchaseBundleId"}, "rush_order_799PurchaseBundleId": {"value": "{\"bundleId\":\"cd_799\"@\"rewards\":[{\"Currency\":\"gem\"@\"Amount\":120@\"Crypt\":\"rJZ\"}@{\"Currency\":\"additem_1\"@\"Amount\":8@\"Crypt\":\"{\"}@{\"Currency\":\"skiptime_1\"@\"Amount\":1@\"Crypt\":\"r\"}@{\"Currency\":\"energy\"@\"Amount\":100@\"Crypt\":\"rHZ\"}]@\"groupId\":\"cd1\"@\"bundleType\":\"cd\"}", "key": "rush_order_799PurchaseBundleId"}}, "finishedTasks": [[{"chapterId": 1, "value": 25, "taskId": 1}, {"chapterId": 1, "value": 54, "taskId": 2}, {"chapterId": 1, "value": 48, "taskId": 3}, {"chapterId": 1, "value": 33, "taskId": 4}, {"chapterId": 1, "value": 86, "taskId": 5}, {"chapterId": 1, "value": 81, "taskId": 6}, {"chapterId": 1, "value": 50, "taskId": 7}, {"chapterId": 1, "value": 68, "taskId": 8}, {"chapterId": 1, "value": 38, "taskId": 9}, {"chapterId": 1, "value": 47, "taskId": 10}], [{"chapterId": 2, "value": 122, "taskId": 1}, {"chapterId": 2, "value": 122, "taskId": 2}, {"chapterId": 2, "value": 130, "taskId": 3}, {"chapterId": 2, "value": 138, "taskId": 4}, {"chapterId": 2, "value": 138, "taskId": 5}, {"chapterId": 2, "value": 130, "taskId": 6}, {"chapterId": 2, "value": 117, "taskId": 7}, {"chapterId": 2, "value": 117, "taskId": 8}, {"chapterId": 2, "value": 164, "taskId": 9}, {"chapterId": 2, "value": 164, "taskId": 10}, {"chapterId": 2, "value": 145, "taskId": 11}, {"chapterId": 2, "value": 117, "taskId": 12}, {"chapterId": 2, "value": 155, "taskId": 13}, {"chapterId": 2, "value": 189, "taskId": 14}, {"chapterId": 2, "value": 189, "taskId": 15}, {"chapterId": 2, "value": 142, "taskId": 16}, {"chapterId": 2, "value": 111, "taskId": 17}, {"chapterId": 2, "value": 96, "taskId": 18}, {"chapterId": 2, "value": 174, "taskId": 19}, {"chapterId": 2, "value": 158, "taskId": 20}, {"chapterId": 2, "value": 220, "taskId": 21}, {"chapterId": 2, "value": 226, "taskId": 22}, {"chapterId": 2, "value": 226, "taskId": 23}, {"chapterId": 2, "value": 173, "taskId": 24}, {"chapterId": 2, "value": 208, "taskId": 25}, {"chapterId": 2, "value": 102, "taskId": 26}, {"chapterId": 2, "value": 102, "taskId": 27}, {"chapterId": 2, "value": 155, "taskId": 28}, {"chapterId": 2, "value": 155, "taskId": 29}, {"chapterId": 2, "value": 173, "taskId": 30}], [{"chapterId": 3, "value": 219, "taskId": 1}, {"chapterId": 3, "value": 236, "taskId": 2}, {"chapterId": 3, "value": 253, "taskId": 3}, {"chapterId": 3, "value": 219, "taskId": 4}, {"chapterId": 3, "value": 236, "taskId": 5}, {"chapterId": 3, "value": 253, "taskId": 6}, {"chapterId": 3, "value": 317, "taskId": 7}, {"chapterId": 3, "value": 183, "taskId": 8}, {"chapterId": 3, "value": 295, "taskId": 9}, {"chapterId": 3, "value": 317, "taskId": 10}, {"chapterId": 3, "value": 317, "taskId": 11}, {"chapterId": 3, "value": 183, "taskId": 12}, {"chapterId": 3, "value": 317, "taskId": 13}, {"chapterId": 3, "value": 277, "taskId": 14}, {"chapterId": 3, "value": 373, "taskId": 15}, {"chapterId": 3, "value": 349, "taskId": 16}, {"chapterId": 3, "value": 373, "taskId": 17}, {"chapterId": 3, "value": 277, "taskId": 18}, {"chapterId": 3, "value": 301, "taskId": 19}, {"chapterId": 3, "value": 253, "taskId": 20}, {"chapterId": 3, "value": 294, "taskId": 21}, {"chapterId": 3, "value": 331, "taskId": 22}, {"chapterId": 3, "value": 555, "taskId": 23}, {"chapterId": 3, "value": 257, "taskId": 24}, {"chapterId": 3, "value": 444, "taskId": 25}, {"chapterId": 3, "value": 406, "taskId": 26}, {"chapterId": 3, "value": 369, "taskId": 27}, {"chapterId": 3, "value": 257, "taskId": 28}, {"chapterId": 3, "value": 248, "taskId": 29}, {"chapterId": 3, "value": 663, "taskId": 30}, {"chapterId": 3, "value": 294, "taskId": 31}, {"chapterId": 3, "value": 248, "taskId": 32}, {"chapterId": 3, "value": 617, "taskId": 33}, {"chapterId": 3, "value": 248, "taskId": 34}, {"chapterId": 3, "value": 524, "taskId": 35}, {"chapterId": 3, "value": 386, "taskId": 36}, {"chapterId": 3, "value": 489, "taskId": 37}, {"chapterId": 3, "value": 461, "taskId": 38}, {"chapterId": 3, "value": 378, "taskId": 39}, {"chapterId": 3, "value": 405, "taskId": 40}, {"chapterId": 3, "value": 461, "taskId": 41}, {"chapterId": 3, "value": 378, "taskId": 42}, {"chapterId": 3, "value": 405, "taskId": 43}, {"chapterId": 3, "value": 405, "taskId": 44}], [{"chapterId": 4, "value": 523, "taskId": 1}, {"chapterId": 4, "value": 591, "taskId": 2}, {"chapterId": 4, "value": 557, "taskId": 3}, {"chapterId": 4, "value": 490, "taskId": 4}, {"chapterId": 4, "value": 457, "taskId": 5}, {"chapterId": 4, "value": 490, "taskId": 6}, {"chapterId": 4, "value": 490, "taskId": 7}, {"chapterId": 4, "value": 551, "taskId": 8}, {"chapterId": 4, "value": 604, "taskId": 9}, {"chapterId": 4, "value": 551, "taskId": 10}, {"chapterId": 4, "value": 604, "taskId": 11}, {"chapterId": 4, "value": 551, "taskId": 12}, {"chapterId": 4, "value": 551, "taskId": 13}, {"chapterId": 4, "value": 551, "taskId": 14}, {"chapterId": 4, "value": 635, "taskId": 15}, {"chapterId": 4, "value": 635, "taskId": 16}, {"chapterId": 4, "value": 580, "taskId": 17}, {"chapterId": 4, "value": 742, "taskId": 18}, {"chapterId": 4, "value": 527, "taskId": 19}, {"chapterId": 4, "value": 580, "taskId": 20}, {"chapterId": 4, "value": 635, "taskId": 21}, {"chapterId": 4, "value": 604, "taskId": 22}, {"chapterId": 4, "value": 604, "taskId": 23}, {"chapterId": 4, "value": 649, "taskId": 24}, {"chapterId": 4, "value": 649, "taskId": 25}, {"chapterId": 4, "value": 693, "taskId": 26}, {"chapterId": 4, "value": 738, "taskId": 27}, {"chapterId": 4, "value": 783, "taskId": 28}, {"chapterId": 4, "value": 649, "taskId": 29}, {"chapterId": 4, "value": 775, "taskId": 30}, {"chapterId": 4, "value": 658, "taskId": 31}, {"chapterId": 4, "value": 835, "taskId": 32}, {"chapterId": 4, "value": 775, "taskId": 33}, {"chapterId": 4, "value": 716, "taskId": 34}, {"chapterId": 4, "value": 658, "taskId": 35}, {"chapterId": 4, "value": 658, "taskId": 36}, {"chapterId": 4, "value": 716, "taskId": 37}, {"chapterId": 4, "value": 872, "taskId": 38}, {"chapterId": 4, "value": 812, "taskId": 39}, {"chapterId": 4, "value": 750, "taskId": 40}, {"chapterId": 4, "value": 689, "taskId": 41}, {"chapterId": 4, "value": 750, "taskId": 42}, {"chapterId": 4, "value": 750, "taskId": 43}, {"chapterId": 4, "value": 689, "taskId": 44}, {"chapterId": 4, "value": 787, "taskId": 45}], [{"chapterId": 5, "value": 697, "taskId": 1}, {"chapterId": 5, "value": 621, "taskId": 2}, {"chapterId": 5, "value": 543, "taskId": 3}, {"chapterId": 5, "value": 543, "taskId": 4}, {"chapterId": 5, "value": 621, "taskId": 5}, {"chapterId": 5, "value": 659, "taskId": 6}, {"chapterId": 5, "value": 697, "taskId": 7}, {"chapterId": 5, "value": 621, "taskId": 8}, {"chapterId": 5, "value": 729, "taskId": 9}, {"chapterId": 5, "value": 597, "taskId": 10}]], "openFunc": {"inventory": {"id": "inventory", "state": 1}, "discoveries": {"id": "discoveries", "state": 1}, "bubble": {"id": "bubble", "state": 1}, "shop": {"id": "shop", "state": 1}}}