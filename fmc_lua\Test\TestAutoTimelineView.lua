TestAutoTimelineView = setmetatable({
  eViewType = EViewType.Other
}, BaseView)
TestAutoTimelineView.__index = TestAutoTimelineView

function TestAutoTimelineView:Init()
  self.m_finishCurrentToggle:SetIsOnWithoutNotify(GM.TestAutoRunModel.finishCurrent)
  self.m_useInventoryToggle:SetIsOnWithoutNotify(GM.TestAutoRunModel.useInventory)
  self.m_ignoreBuildToggle:SetIsOnWithoutNotify(GM.TestAutoRunModel.ignoreBuild)
  self.m_orderModel = GM.MainBoardModel:GetOrderModel()
  self.m_autoRunModel = GM.TestAutoRunModel
end

function TestAutoTimelineView:OnStartButtonClicked()
  if GM.TestAutoRunModel.autoRun then
    GM.TestAutoRunModel:StopAutoRun()
    self.m_speedSlider.value = 1
    return
  end
  GM.TestAutoRunModel:StartAutoRun()
  BoardPromptTapSpreadItem.CurrentOrder = nil
  if GM.TestAutoRunModel.finishCurrent then
    MainBoardView.GetInstance():GetCamera().enabled = false
    GM.ModeViewController:GetChapterCam().enabled = false
    self.m_speedSlider.value = self.m_speedSlider.maxValue
    self:OnSpeedChanged()
  end
end

function TestAutoTimelineView:OnExitButtonClicked()
  GM.TestAutoRunModel:StopAutoRun()
  self:Close()
end

function TestAutoTimelineView:OnMoreButtonClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.TestAutoRunSettingWindow)
end

function TestAutoTimelineView:Update()
  local chapterId = GM.TaskManager:GetOngoingChapterId()
  local runningTask = self.m_autoRunModel:GetRunningTask()
  local chapterId, groupId = self.m_orderModel:GetOrderGroupInfo()
  local finishedCount, totalCount = self.m_orderModel:GetGroupProgress()
  local orderGroupInfo = "订单组:" .. chapterId .. "@" .. groupId .. " " .. finishedCount .. "/" .. totalCount
  if BoardPromptTapSpreadItem.CurrentOrder then
    orderGroupInfo = orderGroupInfo .. "  " .. BoardPromptTapSpreadItem.CurrentOrder:GetId()
  end
  if runningTask ~= nil then
    local taskInfo = "任务:" .. chapterId .. "@" .. runningTask
    self.m_currentText.text = taskInfo .. "  " .. orderGroupInfo
  else
    self.m_currentText.text = orderGroupInfo
  end
  self.m_energyText.text = "能量消耗：" .. self.m_autoRunModel.consumedEnergy .. "   上次的消耗：" .. self.m_autoRunModel.lastConsumedEnergy
  self.m_startButtonText.text = self.m_autoRunModel.autoRun and "停止" or "开始"
end

function TestAutoTimelineView:OnSpeedChanged()
  CS.UnityEngine.Time.timeScale = self.m_speedSlider.value
  DOTween.timeScale = self.m_speedSlider.value
end

function TestAutoTimelineView:OnToggleChanged()
  GM.TestAutoRunModel.finishCurrent = self.m_finishCurrentToggle.isOn
  GM.TestAutoRunModel.useInventory = self.m_useInventoryToggle.isOn
  if GM.TestAutoRunModel.useInventory then
    local slotConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.InventorySlot)
    GM.MiscModel:SetInventoryBoughtCap(#slotConfig.UnlockCost)
    EventDispatcher.DispatchEvent(EEventType.OnInventoryCapacityUpdate)
  end
  GM.TestAutoRunModel.ignoreBuild = self.m_ignoreBuildToggle.isOn
end
