ExtraBoardActivityDefinition = {}
local CreateActivityDefinition = function(activityType, prefix, overrideTable)
  local BaseTable = {
    EntryButtonKey = ESceneViewHudButtonKey[prefix],
    ActivityDataTableName = VirtualDBTableName[prefix],
    ItemDataTableName = VirtualDBTableName[prefix .. "Item"],
    ItemLayerDataTableName = VirtualDBTableName[prefix .. "ItemLayer"],
    ItemCacheDataTableName = VirtualDBTableName[prefix .. "ItemCache"],
    StateChangedEvent = EEventType[prefix .. "StateChanged"],
    EntryPrefabName = UIPrefabConfigName[prefix .. "Entry"],
    BoardEntryPrefabName = UIPrefabConfigName[prefix .. "BoardEntry"],
    ReadyWindowPrefabName = UIPrefabConfigName[prefix .. "ReadyWindow"],
    MainWindowPrefabName = UIPrefabConfigName[prefix .. "MainWindow"],
    EndWindowPrefabName = UIPrefabConfigName[prefix .. "EndWindow"],
    RewardRecoverWindowPrefabName = UIPrefabConfigName[prefix .. "RewardRecoverWindow"],
    ResourceLabels = {
      AddressableLabel.ExtraBoardCommon,
      AddressableLabel[prefix]
    },
    TileImageName1 = ImageFileConfigName.HasConfig(activityType .. "_tile_bg1") and ImageFileConfigName[activityType .. "_tile_bg1"] or nil,
    TileImageName2 = ImageFileConfigName.HasConfig(activityType .. "_tile_bg2") and ImageFileConfigName[activityType .. "_tile_bg2"] or nil,
    BoardBgImageName = ImageFileConfigName.HasConfig(activityType .. "_board_bg") and ImageFileConfigName[activityType .. "_board_bg"] or nil,
    TutorialStartCondition = ETutorialStartCondition.ExtraBoardStart
  }
  BaseTable.__index = BaseTable
  ExtraBoardActivityDefinition[activityType] = setmetatable(overrideTable, BaseTable)
  ExtraBoardActivityDefinition[activityType].__index = ExtraBoardActivityDefinition[activityType]
end
CreateActivityDefinition(ActivityType.ExtraBoard1, "ExtraBoard1", {})
CreateActivityDefinition(ActivityType.ExtraBoard2, "ExtraBoard2", {})
CreateActivityDefinition(ActivityType.ExtraBoard3, "ExtraBoard3", {})
CreateActivityDefinition(ActivityType.ExtraBoard4, "ExtraBoard4", {})
CreateActivityDefinition(ActivityType.ExtraBoard5, "ExtraBoard5", {})
CreateActivityDefinition(ActivityType.ExtraBoard6, "ExtraBoard6", {})
CreateActivityDefinition(ActivityType.ExtraBoard7, "ExtraBoard7", {})
