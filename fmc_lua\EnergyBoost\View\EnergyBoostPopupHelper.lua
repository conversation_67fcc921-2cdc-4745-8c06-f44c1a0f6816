EnergyBoostPopupHelper = setmetatable({
  canPopScene = {
    [EPopupScene.Main] = true,
    [EPopupScene.Board] = true
  }
}, BasePopupHelper)
EnergyBoostPopupHelper.__index = EnergyBoostPopupHelper

function EnergyBoostPopupHelper.Create()
  local helper = setmetatable({}, EnergyBoostPopupHelper)
  helper:Init()
  return helper
end

function EnergyBoostPopupHelper:Init()
  BasePopupHelper.Init(self)
  EventDispatcher.AddListener(EEventType.EnergyBoostModeChanged, self, self._OnEnergyBoostModeChanged)
end

function EnergyBoostPopupHelper:_OnEnergyBoostModeChanged()
  self:SetNeedCheckPopup(true)
end

function EnergyBoostPopupHelper:CheckPopup()
  local openState = GM.MiscModel:GetEnergyBoostWindowOpenStateInNumber()
  if GM.EnergyBoostModel:IsEnergyBoostConfigOn() then
    if openState ~= 1 then
      GM.MiscModel:SetEnergyBoostWindowOpenState(1)
      if not GM.EnergyBoostModel:IsTimeLimitedOn() then
        GM.EnergyBoostModel:SetEnergyBoostUserOn(false, false)
      end
      return UIPrefabConfigName.EnergyBoostSettingWindow, {true}
    end
  elseif openState == 1 then
    GM.MiscModel:SetEnergyBoostWindowOpenState(0)
  end
  return nil
end
