BundleBaseWindow = setmetatable({}, BaseWindow)
BundleBaseWindow.__index = BundleBaseWindow

function BundleBaseWindow:Init(bundleType, dataGroup, bUserClick, eTriggerType)
  self.m_bundleType = bundleType
  self.m_dataGroup = dataGroup
  self.m_model = GM.BundleManager:GetModel(self.m_bundleType)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    bUserClick and EBIReferType.UserClick or EBIReferType.AutoPopup,
    eTriggerType
  }, self:GetPurchaseIds())
  EventDispatcher.AddListener(EEventType.BundleDataRefreshed, self, self.OnBundleDataRefreshed)
end

function BundleBaseWindow:GetPurchaseIds()
  Log.Error("GetPurchaseIds接口未重写, 请检查")
end

function BundleBaseWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  EventDispatcher.RemoveTarget(self)
end

function BundleBaseWindow:OnBundleDataRefreshed(msg)
  if self.m_model == nil or self.m_dataGroup == nil then
    return
  end
  if self.m_dataGroup:GetLastDuration() == 0 then
    return
  end
  local bundleState = self.m_model:GetBundleState(self.m_dataGroup)
  if bundleState ~= EBundleState.Opening then
    self:Close()
  end
end
