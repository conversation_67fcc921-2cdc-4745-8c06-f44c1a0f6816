RacePlayerData = {}
RacePlayerData.__index = RacePlayerData

function RacePlayerData.Create(model, tbData, targetScore, lastScore, lastRank)
  local instance = setmetatable(tbData, RacePlayerData)
  instance:_Init(model, targetScore, lastScore, lastRank)
  return instance
end

function RacePlayerData:_Init(model, targetScore, lastScore, lastRank)
  Log.Assert(model ~= nil, "coin race player data error")
  Log.Assert(targetScore ~= nil, "coin race target score error!")
  self.m_model = model
  if self.id == nil then
    self.id = self.userid
  end
  if self.score then
    self.score = json.decode(self.score)
  end
  self.lastScore = lastScore or 0
  self.lastRank = lastRank
  self.targetScore = targetScore
end

function RacePlayerData:GetUserId()
  return self.id
end

function RacePlayerData:GetIcon()
  if self:IsMySelf() then
    self.icon = GM.UserProfileModel:GetIcon()
  end
  return self.icon
end

function RacePlayerData:GetRank()
  return self.m_rank or 0
end

function RacePlayerData:SetRank(rank)
  self.m_rank = rank
end

function RacePlayerData:GetLastRank()
  return self.lastRank or self:GetRank()
end

function RacePlayerData:UpdateLastRank()
  self.lastRank = self:GetRank()
end

function RacePlayerData:SetTrack(track)
  self.track = track
end

function RacePlayerData:GetUserName(maxLimit)
  if self:IsMySelf() then
    return self.name
  end
  return StringUtil.GetPlayerNameWithLimit(self.name, maxLimit or 8)
end

function RacePlayerData:GetTrack()
  return self.track
end

function RacePlayerData:IsMySelf()
  return self.m_model:IsMySelf(self:GetUserId())
end

function RacePlayerData:GetCurScore(gapTime)
  if self:IsMySelf() then
    local model = self.m_model
    if model then
      return model:GetMyScore(), model:GetMyTime()
    end
    return 0, 0
  end
  local score = 0
  local time = 0
  if gapTime >= self.score[#self.score].seconds then
    score = self.score[#self.score].score
    time = self.score[#self.score].seconds
  else
    for i, v in ipairs(self.score) do
      if gapTime < v.seconds then
        if 0 < i - 1 then
          score = self.score[i - 1].score
          time = self.score[i - 1].seconds
        end
        break
      end
    end
  end
  local ratio = self.m_model and self.m_model:GetScoreRatio() or 1
  return math.min(math.floor(score * ratio), self.targetScore), time
end

function RacePlayerData:UpdateContent(gapTime)
  self.m_curScore, self.m_curTime = self:GetCurScore(gapTime)
end

function RacePlayerData:UpdateLastScore()
  self.lastScore = self.m_curScore
end

function RacePlayerData:GetLastScore()
  return self.lastScore or 0
end

function RacePlayerData:GetData()
  return {
    id = self:GetUserId(),
    name = self.name,
    icon = self.icon,
    score = json.encode(self.score),
    track = self.track,
    group_type = self.group_type
  }
end
